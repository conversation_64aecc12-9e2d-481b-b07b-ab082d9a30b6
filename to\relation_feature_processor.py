#!/usr/bin/env python3
"""
关联特征处理器
解决relations_df未被充分利用的问题，构建个股-板块-市场的层次化关联特征
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional, Any
from scipy.stats import pearsonr, spearmanr
from scipy.spatial.distance import cosine
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class RelationFeatureProcessor:
    """
    关联特征处理器
    构建个股与板块、市场之间的多维关联特征
    """
    
    def __init__(self):
        self.sector_stats_cache = {}  # 缓存板块统计信息
        self.market_stats_cache = {}  # 缓存市场统计信息
        self.correlation_cache = {}   # 缓存相关性计算结果
        
    def extract_relation_features(self, stock_data: pd.DataFrame, 
                                sector_data: pd.DataFrame, 
                                relations_df: pd.DataFrame,
                                market_data: Optional[pd.DataFrame] = None) -> Dict[str, np.ndarray]:
        """
        提取股票-板块关联特征
        
        Args:
            stock_data: 个股数据
            sector_data: 板块数据  
            relations_df: 关联关系数据
            market_data: 市场数据（可选）
            
        Returns:
            关联特征字典
        """
        logger.info("开始提取关联特征...")
        
        relation_features = {}
        
        # 1. 构建个股-板块映射
        stock_sector_mapping = self._build_stock_sector_mapping(relations_df)
        
        # 2. 为每只股票提取关联特征
        for stock_code in stock_data['stock_code'].unique():
            if stock_code not in stock_sector_mapping:
                logger.warning(f"股票 {stock_code} 在relations_df中未找到对应板块")
                continue
                
            stock_features = self._extract_single_stock_relation_features(
                stock_code, stock_data, sector_data, stock_sector_mapping,
                market_data
            )
            
            # 合并到总特征字典
            for feature_name, feature_values in stock_features.items():
                if feature_name not in relation_features:
                    relation_features[feature_name] = []
                relation_features[feature_name].extend(feature_values)
        
        # 3. 转换为numpy数组
        for feature_name in relation_features:
            relation_features[feature_name] = np.array(relation_features[feature_name])
        
        logger.info(f"关联特征提取完成，共生成 {len(relation_features)} 个特征")
        return relation_features
    
    def _build_stock_sector_mapping(self, relations_df: pd.DataFrame) -> Dict[str, List[str]]:
        """构建股票-板块映射关系"""
        mapping = {}
        
        for _, row in relations_df.iterrows():
            stock_code = row['stock_code']
            sector_code = row['sector_code']
            
            if stock_code not in mapping:
                mapping[stock_code] = []
            mapping[stock_code].append(sector_code)
        
        logger.info(f"构建股票-板块映射完成，覆盖 {len(mapping)} 只股票")
        return mapping
    
    def _extract_single_stock_relation_features(self, stock_code: str,
                                              stock_data: pd.DataFrame,
                                              sector_data: pd.DataFrame,
                                              stock_sector_mapping: Dict[str, List[str]],
                                              market_data: Optional[pd.DataFrame] = None) -> Dict[str, List[float]]:
        """为单只股票提取关联特征"""
        
        features = {}
        
        # 获取该股票的数据
        stock_df = stock_data[stock_data['stock_code'] == stock_code].copy()
        if stock_df.empty:
            return features
        
        # 获取该股票关联的板块
        related_sectors = stock_sector_mapping.get(stock_code, [])
        
        for _, stock_row in stock_df.iterrows():
            date_str = stock_row['date_str']
            
            # 1. 个股相对板块表现特征
            relative_features = self._calculate_relative_performance_features(
                stock_row, related_sectors, sector_data, date_str
            )
            
            # 2. 个股与板块相关性特征
            correlation_features = self._calculate_correlation_features(
                stock_code, related_sectors, stock_data, sector_data, date_str
            )
            
            # 3. 板块轮动特征
            rotation_features = self._calculate_sector_rotation_features(
                related_sectors, sector_data, date_str
            )
            
            # 4. 市场环境特征
            market_features = self._calculate_market_environment_features(
                stock_row, market_data, date_str
            )
            
            # 5. 板块内部结构特征
            structure_features = self._calculate_sector_structure_features(
                stock_code, related_sectors, stock_data, sector_data, date_str
            )
            
            # 合并所有特征
            all_features = {
                **relative_features,
                **correlation_features,
                **rotation_features,
                **market_features,
                **structure_features
            }
            
            # 添加到特征列表
            for feature_name, feature_value in all_features.items():
                if feature_name not in features:
                    features[feature_name] = []
                features[feature_name].append(feature_value)
        
        return features
    
    def _calculate_relative_performance_features(self, stock_row: pd.Series,
                                               related_sectors: List[str],
                                               sector_data: pd.DataFrame,
                                               date_str: str) -> Dict[str, float]:
        """计算个股相对板块表现特征"""
        features = {}
        
        stock_return = stock_row.get('pct_chg', 0) / 100.0  # 转换为小数
        
        for sector_code in related_sectors:
            # 获取板块当日数据
            sector_row = sector_data[
                (sector_data['sector_code'] == sector_code) & 
                (sector_data['date_str'] == date_str)
            ]
            
            if not sector_row.empty:
                sector_return = sector_row.iloc[0].get('change_percent', 0) / 100.0
                
                # 相对收益率
                relative_return = stock_return - sector_return
                features[f'relative_return_{sector_code}'] = relative_return
                
                # 相对强度
                if sector_return != 0:
                    relative_strength = stock_return / sector_return
                else:
                    relative_strength = 0
                features[f'relative_strength_{sector_code}'] = relative_strength
                
                # 超额收益率
                excess_return = stock_return - sector_return
                features[f'excess_return_{sector_code}'] = excess_return
            else:
                # 如果没有板块数据，使用默认值
                features[f'relative_return_{sector_code}'] = 0
                features[f'relative_strength_{sector_code}'] = 1
                features[f'excess_return_{sector_code}'] = 0
        
        return features
    
    def _calculate_correlation_features(self, stock_code: str,
                                      related_sectors: List[str],
                                      stock_data: pd.DataFrame,
                                      sector_data: pd.DataFrame,
                                      current_date: str) -> Dict[str, float]:
        """计算个股与板块相关性特征"""
        features = {}
        
        # 获取历史数据（过去20个交易日）
        stock_history = stock_data[
            (stock_data['stock_code'] == stock_code) &
            (stock_data['date_str'] <= current_date)
        ].tail(20)
        
        if len(stock_history) < 10:  # 数据不足
            for sector_code in related_sectors:
                features[f'correlation_{sector_code}'] = 0
                features[f'beta_{sector_code}'] = 1
            return features
        
        stock_returns = stock_history['pct_chg'].values / 100.0
        
        for sector_code in related_sectors:
            # 获取对应的板块历史数据
            sector_history = sector_data[
                (sector_data['sector_code'] == sector_code) &
                (sector_data['date_str'].isin(stock_history['date_str']))
            ]
            
            if len(sector_history) >= 10:
                sector_returns = sector_history['change_percent'].values / 100.0
                
                # 确保数据长度一致
                min_len = min(len(stock_returns), len(sector_returns))
                if min_len >= 10:
                    stock_ret = stock_returns[-min_len:]
                    sector_ret = sector_returns[-min_len:]
                    
                    # 计算相关系数
                    try:
                        correlation, _ = pearsonr(stock_ret, sector_ret)
                        if np.isnan(correlation):
                            correlation = 0
                    except:
                        correlation = 0
                    
                    # 计算Beta系数
                    try:
                        if np.var(sector_ret) > 0:
                            beta = np.cov(stock_ret, sector_ret)[0, 1] / np.var(sector_ret)
                        else:
                            beta = 1
                        if np.isnan(beta):
                            beta = 1
                    except:
                        beta = 1
                    
                    features[f'correlation_{sector_code}'] = correlation
                    features[f'beta_{sector_code}'] = beta
                else:
                    features[f'correlation_{sector_code}'] = 0
                    features[f'beta_{sector_code}'] = 1
            else:
                features[f'correlation_{sector_code}'] = 0
                features[f'beta_{sector_code}'] = 1
        
        return features
    
    def _calculate_sector_rotation_features(self, related_sectors: List[str],
                                          sector_data: pd.DataFrame,
                                          current_date: str) -> Dict[str, float]:
        """计算板块轮动特征"""
        features = {}
        
        for sector_code in related_sectors:
            # 获取板块历史数据（过去10个交易日）
            sector_history = sector_data[
                (sector_data['sector_code'] == sector_code) &
                (sector_data['date_str'] <= current_date)
            ].tail(10)
            
            if len(sector_history) >= 5:
                returns = sector_history['change_percent'].values / 100.0
                
                # 动量特征
                momentum_5d = np.mean(returns[-5:]) if len(returns) >= 5 else 0
                momentum_10d = np.mean(returns) if len(returns) >= 10 else 0
                
                # 波动率特征
                volatility = np.std(returns) if len(returns) >= 3 else 0
                
                # 趋势特征（线性回归斜率）
                if len(returns) >= 5:
                    x = np.arange(len(returns))
                    try:
                        trend = np.polyfit(x, returns, 1)[0]
                    except:
                        trend = 0
                else:
                    trend = 0
                
                features[f'sector_momentum_5d_{sector_code}'] = momentum_5d
                features[f'sector_momentum_10d_{sector_code}'] = momentum_10d
                features[f'sector_volatility_{sector_code}'] = volatility
                features[f'sector_trend_{sector_code}'] = trend
            else:
                features[f'sector_momentum_5d_{sector_code}'] = 0
                features[f'sector_momentum_10d_{sector_code}'] = 0
                features[f'sector_volatility_{sector_code}'] = 0
                features[f'sector_trend_{sector_code}'] = 0
        
        return features
    
    def _calculate_market_environment_features(self, stock_row: pd.Series,
                                             market_data: Optional[pd.DataFrame],
                                             date_str: str) -> Dict[str, float]:
        """计算市场环境特征"""
        features = {}
        
        if market_data is None or market_data.empty:
            # 如果没有市场数据，使用默认值
            features.update({
                'market_relative_return': 0,
                'market_relative_strength': 1,
                'market_beta': 1,
                'market_correlation': 0
            })
            return features
        
        # 获取市场当日数据
        market_row = market_data[market_data['date_str'] == date_str]
        
        if not market_row.empty:
            stock_return = stock_row.get('pct_chg', 0) / 100.0
            market_return = market_row.iloc[0].get('pct_chg', 0) / 100.0
            
            # 相对市场表现
            relative_return = stock_return - market_return
            relative_strength = stock_return / market_return if market_return != 0 else 1
            
            features['market_relative_return'] = relative_return
            features['market_relative_strength'] = relative_strength
        else:
            features['market_relative_return'] = 0
            features['market_relative_strength'] = 1
        
        # 可以添加更多市场环境特征，如VIX、北向资金等
        features['market_beta'] = 1  # 简化处理
        features['market_correlation'] = 0  # 简化处理
        
        return features
    
    def _calculate_sector_structure_features(self, stock_code: str,
                                           related_sectors: List[str],
                                           stock_data: pd.DataFrame,
                                           sector_data: pd.DataFrame,
                                           date_str: str) -> Dict[str, float]:
        """计算板块内部结构特征"""
        features = {}
        
        for sector_code in related_sectors:
            # 获取该板块内所有股票在当日的表现
            sector_stocks = stock_data[
                (stock_data['date_str'] == date_str) &
                (stock_data['stock_code'].str.contains('00'))  # 简化处理，实际应该从relations_df获取
            ]
            
            if not sector_stocks.empty:
                sector_returns = sector_stocks['pct_chg'].values / 100.0
                
                # 板块内分化度
                sector_dispersion = np.std(sector_returns) if len(sector_returns) > 1 else 0
                
                # 个股在板块中的排名
                stock_return = stock_data[
                    (stock_data['stock_code'] == stock_code) &
                    (stock_data['date_str'] == date_str)
                ]['pct_chg'].values
                
                if len(stock_return) > 0:
                    stock_ret = stock_return[0] / 100.0
                    rank_percentile = (sector_returns < stock_ret).mean()
                else:
                    rank_percentile = 0.5
                
                # 板块强度
                sector_strength = np.mean(sector_returns > 0) if len(sector_returns) > 0 else 0.5
                
                features[f'sector_dispersion_{sector_code}'] = sector_dispersion
                features[f'sector_rank_percentile_{sector_code}'] = rank_percentile
                features[f'sector_strength_{sector_code}'] = sector_strength
            else:
                features[f'sector_dispersion_{sector_code}'] = 0
                features[f'sector_rank_percentile_{sector_code}'] = 0.5
                features[f'sector_strength_{sector_code}'] = 0.5
        
        return features
    
    def get_feature_importance_ranking(self, relation_features: Dict[str, np.ndarray],
                                     target: np.ndarray) -> Dict[str, float]:
        """计算关联特征的重要性排名"""
        importance_scores = {}
        
        for feature_name, feature_values in relation_features.items():
            try:
                # 计算与目标变量的相关性
                valid_mask = ~np.isnan(feature_values) & ~np.isnan(target)
                if np.sum(valid_mask) > 10:
                    correlation, p_value = pearsonr(
                        feature_values[valid_mask], 
                        target[valid_mask]
                    )
                    
                    # 综合相关性和显著性
                    importance = abs(correlation) * (1 - p_value) if not np.isnan(correlation) else 0
                else:
                    importance = 0
                
                importance_scores[feature_name] = importance
                
            except Exception as e:
                logger.warning(f"计算特征 {feature_name} 重要性时出错: {e}")
                importance_scores[feature_name] = 0
        
        # 按重要性排序
        sorted_features = sorted(importance_scores.items(), key=lambda x: x[1], reverse=True)
        
        return dict(sorted_features)
