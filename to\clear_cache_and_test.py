#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理缓存并测试板块unknown问题修复的脚本
"""

import sys
import os
import shutil
import logging
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('clear_cache_and_test.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

def clear_cache_directories():
    """清理缓存目录"""
    logger = logging.getLogger(__name__)
    
    # 定义需要清理的缓存目录
    cache_dirs = [
        Path("cache"),
        Path("data/cache"),
        Path("data/features_cache"),
        Path("data/sector_features_cache"),
        Path("data/change_points_cache"),
        Path("data/relative_features_cache"),
        Path("data/batch_features_cache")
    ]
    
    cleared_count = 0
    
    for cache_dir in cache_dirs:
        if cache_dir.exists():
            try:
                # 清理目录中的所有文件
                for file_path in cache_dir.rglob("*"):
                    if file_path.is_file():
                        if file_path.suffix in ['.pkl', '.parquet', '.cache']:
                            file_path.unlink()
                            cleared_count += 1
                            logger.info(f"删除缓存文件: {file_path}")
                
                logger.info(f"清理缓存目录: {cache_dir}")
            except Exception as e:
                logger.warning(f"清理缓存目录 {cache_dir} 时出错: {e}")
        else:
            logger.info(f"缓存目录不存在: {cache_dir}")
    
    logger.info(f"总共清理了 {cleared_count} 个缓存文件")
    return cleared_count

def test_small_dataset():
    """测试小数据集"""
    logger = logging.getLogger(__name__)
    logger.info("=" * 50)
    logger.info("测试小数据集")
    logger.info("=" * 50)
    
    try:
        from train_sector_models import SectorModelTrainer
        
        trainer = SectorModelTrainer()
        
        # 测试一个小的日期范围
        logger.info("开始测试小数据集...")
        
        # 调用主要方法，但使用较小的日期范围
        result = trainer.select_stocks(
            start_date="20240601",
            end_date="20240610",  # 只测试10天
            top_n=2  # 只选择2只股票
        )
        
        if result is not None:
            logger.info("✅ 小数据集测试成功")
            return True
        else:
            logger.error("❌ 小数据集测试失败")
            return False
            
    except Exception as e:
        logger.error(f"小数据集测试时出错: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("开始清理缓存并测试...")
    
    # 1. 清理缓存
    logger.info("步骤1: 清理缓存文件")
    cleared_count = clear_cache_directories()
    
    if cleared_count > 0:
        logger.info(f"成功清理了 {cleared_count} 个缓存文件")
    else:
        logger.info("没有找到需要清理的缓存文件")
    
    # 2. 测试小数据集
    logger.info("步骤2: 测试小数据集")
    test_result = test_small_dataset()
    
    # 3. 总结
    logger.info("=" * 50)
    logger.info("测试结果总结")
    logger.info("=" * 50)
    
    if test_result:
        logger.info("🎉 测试通过！板块unknown问题已修复")
        logger.info("建议：现在可以运行完整的数据集测试")
    else:
        logger.error("⚠️ 测试失败，需要进一步检查")
        logger.error("建议：检查日志文件以获取更多详细信息")
    
    return test_result

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
