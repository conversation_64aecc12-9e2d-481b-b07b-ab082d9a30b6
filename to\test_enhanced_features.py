#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强特征融合
验证generate_daily_features.py中融合的高级特征是否正常工作
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from generate_daily_features import calculate_technical_indicators

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_test_data():
    """创建测试数据"""
    # 创建模拟股票数据
    dates = pd.date_range(start='2024-01-01', end='2024-03-01', freq='D')
    stock_codes = ['000001', '000002', '300001', '688001']  # 不同板块的股票
    
    data = []
    for stock_code in stock_codes:
        # 生成模拟价格数据
        base_price = 10.0
        for i, date in enumerate(dates):
            # 模拟价格波动
            price_change = np.random.normal(0, 0.02)  # 2%的日波动
            if i == 0:
                open_price = base_price
                close_price = base_price * (1 + price_change)
            else:
                open_price = data[-1]['close'] * (1 + np.random.normal(0, 0.005))
                close_price = open_price * (1 + price_change)
            
            high_price = max(open_price, close_price) * (1 + abs(np.random.normal(0, 0.01)))
            low_price = min(open_price, close_price) * (1 - abs(np.random.normal(0, 0.01)))
            volume = int(np.random.uniform(1000000, 10000000))
            amount = volume * (high_price + low_price) / 2
            
            data.append({
                'stock_code': stock_code,
                'date': date,
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': volume,
                'amount': round(amount, 2)
            })
    
    return pd.DataFrame(data)

def test_enhanced_features():
    """测试增强特征计算"""
    logger.info("=== 测试增强特征融合 ===")
    
    # 创建测试数据
    test_data = create_test_data()
    logger.info(f"创建测试数据: {len(test_data)} 条记录，{len(test_data['stock_code'].unique())} 只股票")
    
    # 计算技术指标和增强特征
    logger.info("开始计算技术指标和增强特征...")
    enhanced_data = calculate_technical_indicators(test_data)
    
    # 检查基础技术指标
    basic_indicators = ['ma5', 'ma10', 'ma20', 'ma60', 'rsi6', 'rsi12', 'rsi24', 
                       'macd', 'macd_signal', 'bb_upper', 'bb_lower', 'atr14']
    
    logger.info("检查基础技术指标...")
    for indicator in basic_indicators:
        if indicator in enhanced_data.columns:
            valid_count = enhanced_data[indicator].notna().sum()
            logger.info(f"  {indicator}: {valid_count}/{len(enhanced_data)} 有效值")
        else:
            logger.warning(f"  缺少指标: {indicator}")
    
    # 检查高级特征
    advanced_features = [
        # K线形态特征
        'pattern_doji', 'pattern_hammer', 'pattern_shooting_star',
        'pattern_engulfing_bullish', 'pattern_engulfing_bearish',
        'candlestick_strength',
        
        # 趋势特征
        'ma20_slope', 'ma60_slope', 'trend_uptrend', 'trend_downtrend',
        'trend_strength', 'trend_consistency',
        
        # 支撑阻力特征
        'support_level', 'resistance_level', 'risk_reward_ratio',
        'support_distance_pct', 'resistance_distance_pct',
        'near_support', 'near_resistance',
        
        # 涨跌停检测
        'is_limit_up', 'is_limit_down',
        
        # 未来收益率
        'future_return_2d',
        
        # 综合评分
        'technical_score', 'pattern_score', 'trend_score', 'composite_score'
    ]
    
    logger.info("检查高级特征...")
    missing_features = []
    for feature in advanced_features:
        if feature in enhanced_data.columns:
            if feature.startswith('pattern_') or feature.startswith('trend_') or feature.startswith('near_') or feature.startswith('is_'):
                # 二元特征
                unique_values = enhanced_data[feature].unique()
                logger.info(f"  {feature}: 取值 {sorted(unique_values)}")
            else:
                # 连续特征
                valid_count = enhanced_data[feature].notna().sum()
                if valid_count > 0:
                    mean_val = enhanced_data[feature].mean()
                    logger.info(f"  {feature}: {valid_count}/{len(enhanced_data)} 有效值, 均值={mean_val:.4f}")
                else:
                    logger.info(f"  {feature}: {valid_count}/{len(enhanced_data)} 有效值")
        else:
            missing_features.append(feature)
    
    if missing_features:
        logger.warning(f"缺少高级特征: {missing_features}")
    else:
        logger.info("✅ 所有高级特征都已成功计算")
    
    # 检查数据类型
    logger.info("检查数据类型...")
    float_features = [f for f in enhanced_data.columns if enhanced_data[f].dtype == 'float32']
    int_features = [f for f in enhanced_data.columns if enhanced_data[f].dtype == 'int32']
    
    logger.info(f"  float32特征: {len(float_features)} 个")
    logger.info(f"  int32特征: {len(int_features)} 个")
    
    # 检查不同股票类型的涨跌停检测
    logger.info("检查涨跌停检测...")
    for stock_code in enhanced_data['stock_code'].unique():
        stock_data = enhanced_data[enhanced_data['stock_code'] == stock_code]
        limit_up_count = stock_data['is_limit_up'].sum()
        limit_down_count = stock_data['is_limit_down'].sum()
        logger.info(f"  {stock_code}: 涨停{limit_up_count}次, 跌停{limit_down_count}次")
    
    # 检查未来收益率计算
    logger.info("检查未来收益率...")
    future_return_valid = enhanced_data['future_return_2d'].notna().sum()
    total_records = len(enhanced_data)
    valid_ratio = future_return_valid / total_records
    logger.info(f"  future_return_2d有效值: {future_return_valid}/{total_records} ({valid_ratio:.2%})")
    
    if future_return_valid > 0:
        mean_return = enhanced_data['future_return_2d'].mean()
        std_return = enhanced_data['future_return_2d'].std()
        logger.info(f"  未来收益率统计: 均值={mean_return:.4f}, 标准差={std_return:.4f}")
    
    # 检查综合评分
    logger.info("检查综合评分...")
    for score_col in ['technical_score', 'pattern_score', 'trend_score', 'composite_score']:
        if score_col in enhanced_data.columns:
            valid_scores = enhanced_data[score_col].notna()
            if valid_scores.any():
                score_data = enhanced_data.loc[valid_scores, score_col]
                logger.info(f"  {score_col}: 范围[{score_data.min():.2f}, {score_data.max():.2f}], 均值={score_data.mean():.2f}")
    
    # 保存测试结果
    output_file = "test_enhanced_features_output.csv"
    enhanced_data.to_csv(output_file, index=False)
    logger.info(f"测试结果已保存到: {output_file}")
    
    return len(missing_features) == 0

def main():
    """主测试函数"""
    logger.info("开始测试增强特征融合...")
    
    try:
        success = test_enhanced_features()
        
        if success:
            logger.info("🎉 增强特征融合测试通过！")
            logger.info("✅ 所有功能已成功从train_sector_models.py融入到generate_daily_features.py")
            return True
        else:
            logger.error("❌ 增强特征融合测试失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
