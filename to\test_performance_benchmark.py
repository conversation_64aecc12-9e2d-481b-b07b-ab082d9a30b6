#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能基准测试
测试优化后的预测系统在不同规模数据下的性能表现
"""

import pandas as pd
import numpy as np
import time
import logging
from train_sector_models import SectorModelTrainer

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('performance_benchmark.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_large_test_data(n_samples=1000):
    """创建大规模测试数据"""
    logger.info(f"创建 {n_samples} 个测试样本...")
    
    # 创建特征数据
    np.random.seed(42)
    
    data = []
    sectors = ['TGN板块0', 'TGN板块1', 'TGN板块2', 'TGN板块3', 'TGN板块4']
    
    for i in range(n_samples):
        sample = {
            'stock_code': f'{i:06d}',
            'date_str': '20240601',
            'date': pd.Timestamp('2024-06-01'),
            'most_relevant_sector': np.random.choice(sectors),
            'future_return_2d': np.random.normal(0, 0.02),  # 2%标准差的收益率
        }
        
        # 添加30个特征
        for j in range(30):
            feature_name = f'feature_{j:02d}'
            if j < 10:  # 个股特征
                sample[feature_name] = np.random.normal(0, 1)
            elif j < 20:  # 板块特征
                sample[feature_name] = np.random.normal(0, 0.5)
            else:  # 相对特征
                sample[feature_name] = np.random.normal(0, 0.3)
        
        data.append(sample)
    
    df = pd.DataFrame(data)
    logger.info(f"创建了 {len(df)} 个测试样本，包含 {len(df.columns)} 个特征")
    return df

def benchmark_similarity_matching(trainer, test_data, n_samples):
    """基准测试相似度匹配"""
    logger.info(f"=== 相似度匹配性能测试 ({n_samples} 样本) ===")
    
    # 训练相似度匹配系统
    start_time = time.time()
    similarity_system = trainer._train_similarity_matching_system(test_data)
    training_time = time.time() - start_time
    logger.info(f"相似度匹配系统训练用时: {training_time:.2f} 秒")
    
    # 预测
    predict_data = test_data.head(min(100, n_samples))  # 预测前100个样本
    start_time = time.time()
    predictions = trainer._predict_with_sector_stratified_similarity(
        predict_data, similarity_system
    )
    prediction_time = time.time() - start_time
    
    logger.info(f"相似度匹配预测用时: {prediction_time:.2f} 秒")
    logger.info(f"预测样本数: {len(predictions)}")
    logger.info(f"平均每样本预测时间: {prediction_time/len(predict_data)*1000:.2f} 毫秒")
    
    return {
        'training_time': training_time,
        'prediction_time': prediction_time,
        'predictions_count': len(predictions),
        'samples_per_second': len(predict_data) / prediction_time if prediction_time > 0 else 0
    }

def benchmark_ml_models(trainer, test_data, n_samples):
    """基准测试机器学习模型"""
    logger.info(f"=== 机器学习模型性能测试 ({n_samples} 样本) ===")
    
    # 训练ML模型
    start_time = time.time()
    ml_system = trainer._train_ml_model_system(test_data)
    training_time = time.time() - start_time
    logger.info(f"ML模型训练用时: {training_time:.2f} 秒")
    
    # 预测
    predict_data = test_data.head(min(100, n_samples))  # 预测前100个样本
    start_time = time.time()
    predictions = trainer._predict_with_trained_models(predict_data, {'ml_system': ml_system})
    prediction_time = time.time() - start_time
    
    logger.info(f"ML模型预测用时: {prediction_time:.2f} 秒")
    logger.info(f"预测样本数: {len(predictions)}")
    logger.info(f"平均每样本预测时间: {prediction_time/len(predict_data)*1000:.2f} 毫秒")
    
    return {
        'training_time': training_time,
        'prediction_time': prediction_time,
        'predictions_count': len(predictions),
        'samples_per_second': len(predict_data) / prediction_time if prediction_time > 0 else 0
    }

def run_performance_benchmark():
    """运行性能基准测试"""
    logger.info("开始性能基准测试...")
    
    # 初始化训练器
    trainer = SectorModelTrainer()
    
    # 测试不同规模的数据
    test_sizes = [100, 500, 1000, 5000]
    results = {}
    
    for size in test_sizes:
        logger.info(f"\n{'='*60}")
        logger.info(f"测试规模: {size} 样本")
        logger.info(f"{'='*60}")
        
        # 创建测试数据
        test_data = create_large_test_data(size)
        
        # 测试相似度匹配
        similarity_results = benchmark_similarity_matching(trainer, test_data, size)
        
        # 测试ML模型
        ml_results = benchmark_ml_models(trainer, test_data, size)
        
        results[size] = {
            'similarity_matching': similarity_results,
            'ml_models': ml_results
        }
        
        # 输出当前规模的总结
        logger.info(f"\n--- 规模 {size} 总结 ---")
        logger.info(f"相似度匹配: {similarity_results['samples_per_second']:.1f} 样本/秒")
        logger.info(f"ML模型: {ml_results['samples_per_second']:.1f} 样本/秒")
    
    # 输出最终总结
    logger.info(f"\n{'='*60}")
    logger.info("性能基准测试总结")
    logger.info(f"{'='*60}")
    
    for size, result in results.items():
        sim_speed = result['similarity_matching']['samples_per_second']
        ml_speed = result['ml_models']['samples_per_second']
        logger.info(f"规模 {size:5d}: 相似度匹配 {sim_speed:8.1f} 样本/秒, ML模型 {ml_speed:8.1f} 样本/秒")
    
    return results

if __name__ == "__main__":
    try:
        results = run_performance_benchmark()
        logger.info("性能基准测试完成！")
    except Exception as e:
        logger.error(f"性能基准测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
