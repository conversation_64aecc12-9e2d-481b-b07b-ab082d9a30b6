#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重构后的相似度匹配和机器学习模型两套预测系统
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from train_sector_models import SectorModelTrainer

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_refactored_systems.log')
        ]
    )
    return logging.getLogger(__name__)

def create_test_data():
    """创建测试数据"""
    logger = logging.getLogger(__name__)
    logger.info("创建测试数据...")
    
    # 创建模拟的变化点特征数据
    np.random.seed(42)
    n_samples = 100
    
    # 基础特征
    test_features = []
    for i in range(n_samples):
        # 模拟个股特征
        stock_features = {
            'stock_code': f'00000{i%10}',
            'date_str': f'2024060{(i%10)+1}',
            'date': datetime(2024, 6, (i%10)+1),
            
            # 价格特征
            'open': 10 + np.random.normal(0, 1),
            'high': 11 + np.random.normal(0, 1),
            'low': 9 + np.random.normal(0, 1),
            'close': 10 + np.random.normal(0, 1),
            'volume': 1000000 + np.random.normal(0, 100000),
            'amount': 10000000 + np.random.normal(0, 1000000),
            'pct_chg': np.random.normal(0, 0.02),
            'turnover_rate': np.random.uniform(0.01, 0.1),
            'amplitude': np.random.uniform(0.01, 0.08),
            
            # 技术指标
            'ma5': 10 + np.random.normal(0, 0.5),
            'ma10': 10 + np.random.normal(0, 0.5),
            'ma20': 10 + np.random.normal(0, 0.5),
            'rsi6': np.random.uniform(20, 80),
            'rsi12': np.random.uniform(20, 80),
            'macd': np.random.normal(0, 0.1),
            'macd_signal': np.random.normal(0, 0.1),
            'bb_upper': 11 + np.random.normal(0, 0.5),
            'bb_lower': 9 + np.random.normal(0, 0.5),
            
            # 板块特征
            'most_relevant_sector': f'TGN板块{i%5}',
            'sector_correlation_score': np.random.uniform(0.3, 0.9),
            'is_sector_leader': np.random.choice([True, False]),
            'avg_pct_chg': np.random.normal(0, 0.015),
            'avg_volume': 5000000 + np.random.normal(0, 500000),
            'avg_turnover': np.random.uniform(0.02, 0.08),
            'limit_up_count': np.random.randint(0, 5),
            'limit_down_count': np.random.randint(0, 3),
            'sector_strength': np.random.uniform(0.3, 0.8),
            'heat_score': np.random.uniform(0.2, 0.9),
            
            # 形态特征
            'pattern_doji': np.random.choice([0, 1]),
            'pattern_hammer': np.random.choice([0, 1]),
            'pattern_engulfing': np.random.choice([0, 1]),
            
            # 标签（未来收益率）
            'future_return_2d': np.random.normal(0, 0.03),
            
            # 元数据
            'change_point_idx': i
        }
        test_features.append(stock_features)
    
    logger.info(f"创建了 {len(test_features)} 个测试样本")
    return test_features

def test_similarity_system(trainer, change_point_features):
    """测试相似度匹配系统"""
    logger = logging.getLogger(__name__)
    logger.info("=" * 50)
    logger.info("测试相似度匹配系统")
    logger.info("=" * 50)
    
    try:
        # 训练相似度匹配系统
        similarity_system = trainer._train_similarity_matching_system(change_point_features)
        
        # 检查训练结果
        historical_db = similarity_system.get('historical_features_db', [])
        sector_groups = similarity_system.get('sector_feature_groups', {})
        feature_stats = similarity_system.get('feature_statistics', {})
        
        logger.info(f"✅ 相似度匹配系统训练成功:")
        logger.info(f"   - 历史样本数: {len(historical_db)}")
        logger.info(f"   - 板块数量: {len(sector_groups)}")
        logger.info(f"   - 特征数量: {len(feature_stats.get('feature_names', []))}")
        
        # 测试预测
        test_df = pd.DataFrame(change_point_features[:10])  # 使用前10个样本测试
        predictions = trainer._predict_with_sector_stratified_similarity(
            test_df, similarity_system
        )
        
        logger.info(f"✅ 相似度匹配预测成功:")
        logger.info(f"   - 预测样本数: {len(predictions)}")
        
        if predictions:
            sample_key = list(predictions.keys())[0]
            sample_pred = predictions[sample_key]
            logger.info(f"   - 样本预测: {sample_pred}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 相似度匹配系统测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_ml_system(trainer, change_point_features):
    """测试机器学习模型系统"""
    logger = logging.getLogger(__name__)
    logger.info("=" * 50)
    logger.info("测试机器学习模型系统")
    logger.info("=" * 50)
    
    try:
        # 训练机器学习模型系统
        ml_system = trainer._train_ml_model_system(change_point_features)
        
        # 检查训练结果
        global_model = ml_system.get('global_model')
        sector_models = ml_system.get('sector_models', {})
        feature_scaler = ml_system.get('feature_scaler')
        training_scores = ml_system.get('training_scores', {})
        
        logger.info(f"✅ 机器学习模型系统训练成功:")
        logger.info(f"   - 全局模型: {'有' if global_model else '无'}")
        logger.info(f"   - 板块模型数: {len(sector_models)}")
        logger.info(f"   - 全局模型R²: {training_scores.get('global_r2', 0):.4f}")
        logger.info(f"   - 板块模型数量: {training_scores.get('sectors_with_models', 0)}")
        
        # 测试预测
        test_df = pd.DataFrame(change_point_features[:10])  # 使用前10个样本测试
        fake_patterns = {'ml_model_system': ml_system}
        predictions = trainer._predict_with_trained_models(test_df, fake_patterns)
        
        logger.info(f"✅ 机器学习模型预测成功:")
        logger.info(f"   - 预测样本数: {len(predictions)}")
        
        if predictions:
            sample_key = list(predictions.keys())[0]
            sample_pred = predictions[sample_key]
            logger.info(f"   - 样本预测: {sample_pred}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 机器学习模型系统测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_integrated_system(trainer, change_point_features):
    """测试完整的集成系统"""
    logger = logging.getLogger(__name__)
    logger.info("=" * 50)
    logger.info("测试完整集成系统")
    logger.info("=" * 50)
    
    try:
        # 使用重构后的训练方法
        results = trainer._analyze_change_point_patterns_direct(change_point_features)
        
        # 检查训练结果
        similarity_system = results.get('similarity_system', {})
        ml_system = results.get('ml_model_system', {})
        training_summary = results.get('training_summary', {})
        
        logger.info(f"✅ 集成系统训练成功:")
        logger.info(f"   - 总样本数: {training_summary.get('total_samples', 0)}")
        logger.info(f"   - 板块数量: {training_summary.get('sectors_count', 0)}")
        logger.info(f"   - 相似度库大小: {training_summary.get('similarity_db_size', 0)}")
        logger.info(f"   - ML模型数量: {training_summary.get('ml_models_trained', 0)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 集成系统测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主测试函数"""
    logger = setup_logging()
    logger.info("开始测试重构后的预测系统...")
    
    # 创建测试数据
    change_point_features = create_test_data()
    
    # 初始化训练器
    trainer = SectorModelTrainer()
    
    # 测试结果
    results = {
        'similarity_system': False,
        'ml_system': False,
        'integrated_system': False
    }
    
    # 1. 测试相似度匹配系统
    results['similarity_system'] = test_similarity_system(trainer, change_point_features)
    
    # 2. 测试机器学习模型系统
    results['ml_system'] = test_ml_system(trainer, change_point_features)
    
    # 3. 测试完整集成系统
    results['integrated_system'] = test_integrated_system(trainer, change_point_features)
    
    # 总结测试结果
    logger.info("=" * 50)
    logger.info("测试结果总结")
    logger.info("=" * 50)
    
    success_count = sum(results.values())
    total_tests = len(results)
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n总体结果: {success_count}/{total_tests} 测试通过")
    
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！重构成功！")
        return True
    else:
        logger.error("⚠️  部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
