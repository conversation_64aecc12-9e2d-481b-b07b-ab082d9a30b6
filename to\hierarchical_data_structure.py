#!/usr/bin/env python3
"""
层次化数据结构
支持个股-板块-市场的多层次数据组织和高效访问
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional, Any, Union
from collections import defaultdict
import pickle
import gzip
from pathlib import Path
import threading
from concurrent.futures import ThreadPoolExecutor
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class HierarchicalDataStructure:
    """
    层次化数据结构
    支持多层次数据的高效存储、索引和访问
    """
    
    def __init__(self, cache_size: int = 1000):
        self.data_hierarchy = {
            'market': {},      # 市场层数据
            'sector': {},      # 板块层数据  
            'stock': {},       # 个股层数据
            'relations': {}    # 关联关系数据
        }
        
        self.indices = {
            'date_index': defaultdict(list),           # 日期索引
            'stock_index': defaultdict(list),          # 股票索引
            'sector_index': defaultdict(list),         # 板块索引
            'stock_sector_mapping': defaultdict(list), # 股票-板块映射
            'sector_stock_mapping': defaultdict(list)  # 板块-股票映射
        }
        
        self.cache = {}           # 数据缓存
        self.cache_size = cache_size
        self.lock = threading.RLock()  # 线程锁
        
        logger.info("层次化数据结构初始化完成")
    
    def add_stock_data(self, stock_data: pd.DataFrame) -> None:
        """添加个股数据"""
        with self.lock:
            logger.info(f"添加个股数据，共 {len(stock_data)} 条记录")
            
            for _, row in stock_data.iterrows():
                stock_code = row['stock_code']
                date_str = row['date_str']
                
                # 存储数据
                if stock_code not in self.data_hierarchy['stock']:
                    self.data_hierarchy['stock'][stock_code] = {}
                
                self.data_hierarchy['stock'][stock_code][date_str] = row.to_dict()
                
                # 更新索引
                self.indices['date_index'][date_str].append(('stock', stock_code))
                self.indices['stock_index'][stock_code].append(date_str)
    
    def add_sector_data(self, sector_data: pd.DataFrame) -> None:
        """添加板块数据"""
        with self.lock:
            logger.info(f"添加板块数据，共 {len(sector_data)} 条记录")
            
            for _, row in sector_data.iterrows():
                sector_code = row['sector_code']
                date_str = row['date_str']
                
                # 存储数据
                if sector_code not in self.data_hierarchy['sector']:
                    self.data_hierarchy['sector'][sector_code] = {}
                
                self.data_hierarchy['sector'][sector_code][date_str] = row.to_dict()
                
                # 更新索引
                self.indices['date_index'][date_str].append(('sector', sector_code))
                self.indices['sector_index'][sector_code].append(date_str)
    
    def add_market_data(self, market_data: pd.DataFrame) -> None:
        """添加市场数据"""
        with self.lock:
            logger.info(f"添加市场数据，共 {len(market_data)} 条记录")
            
            for _, row in market_data.iterrows():
                date_str = row['date_str']
                
                # 存储数据
                self.data_hierarchy['market'][date_str] = row.to_dict()
                
                # 更新索引
                self.indices['date_index'][date_str].append(('market', 'market'))
    
    def add_relations_data(self, relations_df: pd.DataFrame) -> None:
        """添加关联关系数据"""
        with self.lock:
            logger.info(f"添加关联关系数据，共 {len(relations_df)} 条记录")
            
            for _, row in relations_df.iterrows():
                stock_code = row['stock_code']
                sector_code = row['sector_code']
                
                # 存储关联关系
                if stock_code not in self.data_hierarchy['relations']:
                    self.data_hierarchy['relations'][stock_code] = []
                
                self.data_hierarchy['relations'][stock_code].append(row.to_dict())
                
                # 更新映射索引
                self.indices['stock_sector_mapping'][stock_code].append(sector_code)
                self.indices['sector_stock_mapping'][sector_code].append(stock_code)
    
    def get_stock_data(self, stock_code: str, date_str: Optional[str] = None,
                      date_range: Optional[Tuple[str, str]] = None) -> Union[Dict, List[Dict]]:
        """获取个股数据"""
        cache_key = f"stock_{stock_code}_{date_str}_{date_range}"
        
        # 检查缓存
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        with self.lock:
            if stock_code not in self.data_hierarchy['stock']:
                return {} if date_str else []
            
            stock_data = self.data_hierarchy['stock'][stock_code]
            
            if date_str:
                # 获取特定日期数据
                result = stock_data.get(date_str, {})
            elif date_range:
                # 获取日期范围数据
                start_date, end_date = date_range
                result = []
                for date in sorted(stock_data.keys()):
                    if start_date <= date <= end_date:
                        result.append(stock_data[date])
            else:
                # 获取所有数据
                result = [stock_data[date] for date in sorted(stock_data.keys())]
            
            # 更新缓存
            self._update_cache(cache_key, result)
            
            return result
    
    def get_sector_data(self, sector_code: str, date_str: Optional[str] = None,
                       date_range: Optional[Tuple[str, str]] = None) -> Union[Dict, List[Dict]]:
        """获取板块数据"""
        cache_key = f"sector_{sector_code}_{date_str}_{date_range}"
        
        # 检查缓存
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        with self.lock:
            if sector_code not in self.data_hierarchy['sector']:
                return {} if date_str else []
            
            sector_data = self.data_hierarchy['sector'][sector_code]
            
            if date_str:
                result = sector_data.get(date_str, {})
            elif date_range:
                start_date, end_date = date_range
                result = []
                for date in sorted(sector_data.keys()):
                    if start_date <= date <= end_date:
                        result.append(sector_data[date])
            else:
                result = [sector_data[date] for date in sorted(sector_data.keys())]
            
            # 更新缓存
            self._update_cache(cache_key, result)
            
            return result
    
    def get_market_data(self, date_str: Optional[str] = None,
                       date_range: Optional[Tuple[str, str]] = None) -> Union[Dict, List[Dict]]:
        """获取市场数据"""
        cache_key = f"market_{date_str}_{date_range}"
        
        # 检查缓存
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        with self.lock:
            market_data = self.data_hierarchy['market']
            
            if date_str:
                result = market_data.get(date_str, {})
            elif date_range:
                start_date, end_date = date_range
                result = []
                for date in sorted(market_data.keys()):
                    if start_date <= date <= end_date:
                        result.append(market_data[date])
            else:
                result = [market_data[date] for date in sorted(market_data.keys())]
            
            # 更新缓存
            self._update_cache(cache_key, result)
            
            return result
    
    def get_stock_sectors(self, stock_code: str) -> List[str]:
        """获取股票关联的板块"""
        return list(set(self.indices['stock_sector_mapping'].get(stock_code, [])))
    
    def get_sector_stocks(self, sector_code: str) -> List[str]:
        """获取板块内的股票"""
        return list(set(self.indices['sector_stock_mapping'].get(sector_code, [])))
    
    def get_hierarchical_data(self, stock_code: str, date_str: str) -> Dict[str, Any]:
        """获取某只股票在特定日期的层次化数据"""
        cache_key = f"hierarchical_{stock_code}_{date_str}"
        
        # 检查缓存
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        result = {
            'stock_data': self.get_stock_data(stock_code, date_str),
            'sector_data': {},
            'market_data': self.get_market_data(date_str),
            'relations': self.data_hierarchy['relations'].get(stock_code, [])
        }
        
        # 获取关联板块数据
        related_sectors = self.get_stock_sectors(stock_code)
        for sector_code in related_sectors:
            result['sector_data'][sector_code] = self.get_sector_data(sector_code, date_str)
        
        # 更新缓存
        self._update_cache(cache_key, result)
        
        return result
    
    def get_date_range_data(self, start_date: str, end_date: str,
                           stock_codes: Optional[List[str]] = None,
                           sector_codes: Optional[List[str]] = None) -> Dict[str, Any]:
        """获取日期范围内的数据"""
        result = {
            'stock_data': {},
            'sector_data': {},
            'market_data': self.get_market_data(date_range=(start_date, end_date))
        }
        
        # 获取股票数据
        if stock_codes:
            for stock_code in stock_codes:
                result['stock_data'][stock_code] = self.get_stock_data(
                    stock_code, date_range=(start_date, end_date)
                )
        
        # 获取板块数据
        if sector_codes:
            for sector_code in sector_codes:
                result['sector_data'][sector_code] = self.get_sector_data(
                    sector_code, date_range=(start_date, end_date)
                )
        
        return result
    
    def _update_cache(self, key: str, value: Any) -> None:
        """更新缓存"""
        if len(self.cache) >= self.cache_size:
            # 简单的LRU策略：删除最旧的缓存项
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
        
        self.cache[key] = value
    
    def clear_cache(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            logger.info("缓存已清空")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据统计信息"""
        with self.lock:
            stats = {
                'stock_count': len(self.data_hierarchy['stock']),
                'sector_count': len(self.data_hierarchy['sector']),
                'market_data_count': len(self.data_hierarchy['market']),
                'relations_count': len(self.data_hierarchy['relations']),
                'cache_size': len(self.cache),
                'date_range': {
                    'start': min(self.indices['date_index'].keys()) if self.indices['date_index'] else None,
                    'end': max(self.indices['date_index'].keys()) if self.indices['date_index'] else None
                }
            }
            
            # 计算数据密度
            total_stock_records = sum(
                len(dates) for dates in self.data_hierarchy['stock'].values()
            )
            total_sector_records = sum(
                len(dates) for dates in self.data_hierarchy['sector'].values()
            )
            
            stats['total_stock_records'] = total_stock_records
            stats['total_sector_records'] = total_sector_records
            
            return stats
    
    def save_to_file(self, filepath: str, compress: bool = True) -> None:
        """保存数据结构到文件"""
        with self.lock:
            logger.info(f"保存数据结构到文件: {filepath}")
            
            data_to_save = {
                'data_hierarchy': self.data_hierarchy,
                'indices': dict(self.indices)  # 转换defaultdict为普通dict
            }
            
            if compress:
                with gzip.open(filepath, 'wb') as f:
                    pickle.dump(data_to_save, f)
            else:
                with open(filepath, 'wb') as f:
                    pickle.dump(data_to_save, f)
            
            logger.info("数据结构保存完成")
    
    def load_from_file(self, filepath: str, compress: bool = True) -> None:
        """从文件加载数据结构"""
        with self.lock:
            logger.info(f"从文件加载数据结构: {filepath}")
            
            if compress:
                with gzip.open(filepath, 'rb') as f:
                    data_loaded = pickle.load(f)
            else:
                with open(filepath, 'rb') as f:
                    data_loaded = pickle.load(f)
            
            self.data_hierarchy = data_loaded['data_hierarchy']
            
            # 重建defaultdict索引
            self.indices = {
                'date_index': defaultdict(list, data_loaded['indices']['date_index']),
                'stock_index': defaultdict(list, data_loaded['indices']['stock_index']),
                'sector_index': defaultdict(list, data_loaded['indices']['sector_index']),
                'stock_sector_mapping': defaultdict(list, data_loaded['indices']['stock_sector_mapping']),
                'sector_stock_mapping': defaultdict(list, data_loaded['indices']['sector_stock_mapping'])
            }
            
            # 清空缓存
            self.cache.clear()
            
            logger.info("数据结构加载完成")
    
    def optimize_indices(self) -> None:
        """优化索引结构"""
        with self.lock:
            logger.info("开始优化索引结构...")
            
            # 去重和排序
            for key in self.indices:
                if isinstance(self.indices[key], defaultdict):
                    for subkey in self.indices[key]:
                        if isinstance(self.indices[key][subkey], list):
                            # 去重并排序
                            self.indices[key][subkey] = sorted(list(set(self.indices[key][subkey])))
            
            logger.info("索引结构优化完成")
    
    def validate_data_integrity(self) -> Dict[str, Any]:
        """验证数据完整性"""
        with self.lock:
            logger.info("开始验证数据完整性...")
            
            validation_report = {
                'errors': [],
                'warnings': [],
                'statistics': {}
            }
            
            # 检查股票-板块关联的一致性
            for stock_code in self.indices['stock_sector_mapping']:
                if stock_code not in self.data_hierarchy['stock']:
                    validation_report['errors'].append(
                        f"股票 {stock_code} 在关联关系中存在但在股票数据中不存在"
                    )
            
            # 检查数据日期的一致性
            all_dates = set()
            for stock_data in self.data_hierarchy['stock'].values():
                all_dates.update(stock_data.keys())
            
            market_dates = set(self.data_hierarchy['market'].keys())
            missing_market_dates = all_dates - market_dates
            
            if missing_market_dates:
                validation_report['warnings'].append(
                    f"缺少市场数据的日期: {sorted(list(missing_market_dates))[:10]}..."
                )
            
            validation_report['statistics'] = self.get_statistics()
            
            logger.info("数据完整性验证完成")
            return validation_report
