#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试内存修复和ML预测修复
"""

import pandas as pd
import numpy as np
import logging
from train_sector_models import SectorModelTrainer

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_memory_fix():
    """测试内存修复"""
    logger.info("=== 测试内存修复 ===")
    
    # 创建小规模测试数据
    trainer = SectorModelTrainer()
    
    # 创建测试数据
    np.random.seed(42)
    n_samples = 100
    n_features = 30
    
    data = []
    sectors = ['TGN板块0', 'TGN板块1', 'TGN板块2']
    
    for i in range(n_samples):
        sample = {
            'stock_code': f'{i:06d}',
            'date_str': '20240601',
            'date': pd.Timestamp('2024-06-01'),
            'most_relevant_sector': np.random.choice(sectors),
            'future_return_2d': np.random.normal(0, 0.02),
        }
        
        # 添加特征
        for j in range(n_features):
            sample[f'feature_{j:02d}'] = np.random.normal(0, 1)
        
        data.append(sample)
    
    test_df = pd.DataFrame(data)
    
    # 测试相似度匹配系统
    logger.info("训练相似度匹配系统...")
    similarity_system = trainer._train_similarity_matching_system(test_df)
    
    # 测试预测（使用较小的数据集）
    predict_df = test_df.head(10)
    logger.info("测试分块相似度预测...")
    predictions = trainer._predict_with_sector_stratified_similarity(
        predict_df, similarity_system
    )
    
    logger.info(f"相似度预测结果: {len(predictions)} 个预测")
    if predictions:
        sample_pred = list(predictions.values())[0]
        logger.info(f"样本预测: {sample_pred}")
    
    return len(predictions) > 0

def test_ml_prediction_fix():
    """测试ML预测修复"""
    logger.info("=== 测试ML预测修复 ===")
    
    trainer = SectorModelTrainer()
    
    # 创建测试数据
    np.random.seed(42)
    n_samples = 100
    n_features = 30
    
    data = []
    sectors = ['TGN板块0', 'TGN板块1', 'TGN板块2']
    
    for i in range(n_samples):
        sample = {
            'stock_code': f'{i:06d}',
            'date_str': '20240601',
            'date': pd.Timestamp('2024-06-01'),
            'most_relevant_sector': np.random.choice(sectors),
            'future_return_2d': np.random.normal(0, 0.02),
        }
        
        # 添加特征
        for j in range(n_features):
            sample[f'feature_{j:02d}'] = np.random.normal(0, 1)
        
        data.append(sample)
    
    test_df = pd.DataFrame(data)
    
    # 训练ML模型
    logger.info("训练ML模型...")
    ml_system = trainer._train_ml_model_system(test_df)
    
    # 测试预测
    predict_df = test_df.head(10)
    logger.info("测试ML预测...")
    predictions = trainer._predict_with_trained_models(
        predict_df, {'ml_model_system': ml_system}
    )
    
    logger.info(f"ML预测结果: {len(predictions)} 个预测")
    if predictions:
        sample_pred = list(predictions.values())[0]
        logger.info(f"样本预测: {sample_pred}")
        
        # 检查预测值是否为零
        pred_values = [p['predicted_return'] for p in predictions.values()]
        zero_count = sum(1 for v in pred_values if abs(v) < 1e-6)
        non_zero_count = len(pred_values) - zero_count
        
        logger.info(f"预测值统计: 非零={non_zero_count}, 零值={zero_count}")
        logger.info(f"预测值范围: [{min(pred_values):.6f}, {max(pred_values):.6f}]")
    
    return len(predictions) > 0 and any(abs(p['predicted_return']) > 1e-6 for p in predictions.values())

def main():
    """主测试函数"""
    logger.info("开始测试修复...")
    
    # 测试内存修复
    memory_test_passed = test_memory_fix()
    logger.info(f"内存修复测试: {'通过' if memory_test_passed else '失败'}")
    
    # 测试ML预测修复
    ml_test_passed = test_ml_prediction_fix()
    logger.info(f"ML预测修复测试: {'通过' if ml_test_passed else '失败'}")
    
    # 总结
    if memory_test_passed and ml_test_passed:
        logger.info("🎉 所有测试通过！修复成功！")
        return True
    else:
        logger.error("❌ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
