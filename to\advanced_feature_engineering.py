#!/usr/bin/env python3
"""
高级特征工程模块
基于2025年最新机器学习研究的特征工程系统
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional, Any
from sklearn.preprocessing import (
    RobustScaler, QuantileTransformer, PowerTransformer, 
    MinMaxScaler, StandardScaler
)
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression
from sklearn.decomposition import PCA
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class AdaptiveFeatureScaler:
    """
    自适应特征标准化器
    根据特征类型和分布自动选择最佳标准化方法
    """
    
    def __init__(self):
        self.scalers = {
            'robust': RobustScaler(),
            'quantile': QuantileTransformer(output_distribution='normal', random_state=42),
            'power': PowerTransformer(method='yeo-johnson', standardize=True),
            'minmax': MinMaxScaler(),
            'standard': StandardScaler()
        }
        
        self.feature_scalers = {}  # 存储每个特征使用的标准化器
        self.feature_types = {}    # 存储特征类型
        self.feature_stats = {}    # 存储特征统计信息
        
    def _detect_feature_type(self, feature_name: str, values: np.ndarray) -> str:
        """自动检测特征类型"""
        
        # 移除无效值
        valid_values = values[~np.isnan(values) & ~np.isinf(values)]
        
        if len(valid_values) == 0:
            return 'invalid'
        
        # 价格类特征
        if any(keyword in feature_name.lower() for keyword in 
               ['price', 'close', 'open', 'high', 'low', 'ma', 'ema']):
            return 'price'
        
        # 成交量类特征
        elif any(keyword in feature_name.lower() for keyword in 
                ['volume', 'vol', 'amount', 'turnover']):
            return 'volume'
        
        # 比率类特征
        elif any(keyword in feature_name.lower() for keyword in 
                ['ratio', 'rate', 'pct', 'percent', 'return']):
            return 'ratio'
        
        # 指标类特征
        elif any(keyword in feature_name.lower() for keyword in 
                ['rsi', 'macd', 'kdj', 'cci', 'williams']):
            return 'indicator'
        
        # 计数类特征
        elif any(keyword in feature_name.lower() for keyword in 
                ['count', 'days', 'num', 'times']):
            return 'count'
        
        # 基于数值分布判断
        else:
            # 检查是否为二值特征
            unique_values = np.unique(valid_values)
            if len(unique_values) <= 2:
                return 'binary'
            
            # 检查是否为整数特征
            elif np.all(valid_values == np.round(valid_values)):
                return 'integer'
            
            # 检查分布偏度
            skewness = stats.skew(valid_values)
            if abs(skewness) > 2:
                return 'skewed'
            else:
                return 'normal'
    
    def _select_best_scaler(self, feature_type: str, values: np.ndarray) -> str:
        """根据特征类型选择最佳标准化器"""
        
        valid_values = values[~np.isnan(values) & ~np.isinf(values)]
        
        if len(valid_values) == 0:
            return 'standard'
        
        # 根据特征类型选择标准化器
        if feature_type == 'price':
            # 价格特征通常需要对数变换
            return 'power'
        elif feature_type == 'volume':
            # 成交量通常有很大的偏度，使用分位数变换
            return 'quantile'
        elif feature_type == 'ratio':
            # 比率特征对异常值敏感，使用鲁棒标准化
            return 'robust'
        elif feature_type == 'indicator':
            # 技术指标通常有固定范围，使用MinMax
            return 'minmax'
        elif feature_type in ['count', 'integer']:
            # 计数特征使用标准标准化
            return 'standard'
        elif feature_type == 'binary':
            # 二值特征不需要标准化
            return 'minmax'
        elif feature_type == 'skewed':
            # 偏态分布使用Power变换
            return 'power'
        else:
            # 默认使用鲁棒标准化
            return 'robust'
    
    def _calculate_feature_stats(self, values: np.ndarray) -> Dict[str, float]:
        """计算特征统计信息"""
        valid_values = values[~np.isnan(values) & ~np.isinf(values)]
        
        if len(valid_values) == 0:
            return {
                'mean': 0, 'std': 0, 'min': 0, 'max': 0,
                'skewness': 0, 'kurtosis': 0, 'missing_rate': 1.0,
                'zero_rate': 0, 'unique_count': 0
            }
        
        return {
            'mean': np.mean(valid_values),
            'std': np.std(valid_values),
            'min': np.min(valid_values),
            'max': np.max(valid_values),
            'skewness': stats.skew(valid_values),
            'kurtosis': stats.kurtosis(valid_values),
            'missing_rate': (len(values) - len(valid_values)) / len(values),
            'zero_rate': np.sum(valid_values == 0) / len(valid_values),
            'unique_count': len(np.unique(valid_values))
        }
    
    def fit_transform(self, features: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """拟合并转换特征"""
        scaled_features = {}
        
        logger.info(f"开始自适应特征标准化，共 {len(features)} 个特征")
        
        for feature_name, values in features.items():
            try:
                # 确保是numpy数组
                if isinstance(values, (list, pd.Series)):
                    values = np.array(values, dtype=float)
                
                # 检测特征类型
                feature_type = self._detect_feature_type(feature_name, values)
                self.feature_types[feature_name] = feature_type
                
                # 计算统计信息
                stats_info = self._calculate_feature_stats(values)
                self.feature_stats[feature_name] = stats_info
                
                # 如果特征无效或全为缺失值，跳过
                if feature_type == 'invalid' or stats_info['missing_rate'] > 0.95:
                    logger.warning(f"跳过无效特征: {feature_name} (缺失率: {stats_info['missing_rate']:.2%})")
                    continue
                
                # 选择最佳标准化器
                scaler_type = self._select_best_scaler(feature_type, values)
                scaler = self.scalers[scaler_type]
                
                # 处理缺失值和无穷值
                valid_mask = ~np.isnan(values) & ~np.isinf(values)
                processed_values = values.copy()
                
                if np.sum(valid_mask) > 0:
                    # 用中位数填充缺失值
                    median_value = np.median(values[valid_mask])
                    processed_values[~valid_mask] = median_value
                    
                    # 标准化
                    scaled_values = scaler.fit_transform(processed_values.reshape(-1, 1)).flatten()
                    
                    # 存储标准化器
                    self.feature_scalers[feature_name] = scaler
                    
                    scaled_features[feature_name] = scaled_values
                    
                    logger.debug(f"特征 {feature_name}: 类型={feature_type}, 标准化器={scaler_type}, "
                               f"原始范围=[{stats_info['min']:.3f}, {stats_info['max']:.3f}], "
                               f"标准化后范围=[{scaled_values.min():.3f}, {scaled_values.max():.3f}]")
                else:
                    logger.warning(f"特征 {feature_name} 没有有效值，跳过")
                    
            except Exception as e:
                logger.error(f"处理特征 {feature_name} 时出错: {e}")
                continue
        
        logger.info(f"特征标准化完成，成功处理 {len(scaled_features)} 个特征")
        return scaled_features
    
    def transform(self, features: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """使用已拟合的标准化器转换新特征"""
        scaled_features = {}
        
        for feature_name, values in features.items():
            if feature_name in self.feature_scalers:
                try:
                    # 确保是numpy数组
                    if isinstance(values, (list, pd.Series)):
                        values = np.array(values, dtype=float)
                    
                    # 处理缺失值
                    valid_mask = ~np.isnan(values) & ~np.isinf(values)
                    processed_values = values.copy()
                    
                    if np.sum(valid_mask) > 0:
                        # 用训练时的统计信息填充缺失值
                        if feature_name in self.feature_stats:
                            fill_value = self.feature_stats[feature_name]['mean']
                        else:
                            fill_value = np.median(values[valid_mask])
                        
                        processed_values[~valid_mask] = fill_value
                        
                        # 使用已拟合的标准化器
                        scaler = self.feature_scalers[feature_name]
                        scaled_values = scaler.transform(processed_values.reshape(-1, 1)).flatten()
                        
                        scaled_features[feature_name] = scaled_values
                    else:
                        logger.warning(f"特征 {feature_name} 没有有效值，使用零填充")
                        scaled_features[feature_name] = np.zeros_like(values)
                        
                except Exception as e:
                    logger.error(f"转换特征 {feature_name} 时出错: {e}")
                    # 使用零填充作为备用方案
                    scaled_features[feature_name] = np.zeros_like(values)
            else:
                logger.warning(f"特征 {feature_name} 未在训练时见过，跳过")
        
        return scaled_features
    
    def get_feature_importance_scores(self, features: Dict[str, np.ndarray], 
                                    target: np.ndarray) -> Dict[str, float]:
        """计算特征重要性得分"""
        importance_scores = {}
        
        # 准备数据
        feature_names = list(features.keys())
        feature_matrix = np.column_stack([features[name] for name in feature_names])
        
        # 移除无效样本
        valid_mask = ~np.isnan(target) & ~np.isinf(target)
        if np.sum(valid_mask) == 0:
            return {name: 0.0 for name in feature_names}
        
        feature_matrix = feature_matrix[valid_mask]
        target_clean = target[valid_mask]
        
        try:
            # 使用F统计量计算重要性
            f_scores, p_values = f_regression(feature_matrix, target_clean)
            
            # 使用互信息计算重要性
            mi_scores = mutual_info_regression(feature_matrix, target_clean, random_state=42)
            
            # 综合得分
            for i, feature_name in enumerate(feature_names):
                f_score = f_scores[i] if not np.isnan(f_scores[i]) else 0
                mi_score = mi_scores[i] if not np.isnan(mi_scores[i]) else 0
                
                # 归一化并组合
                importance_scores[feature_name] = 0.6 * f_score + 0.4 * mi_score
                
        except Exception as e:
            logger.error(f"计算特征重要性时出错: {e}")
            importance_scores = {name: 0.0 for name in feature_names}
        
        return importance_scores
    
    def get_quality_report(self) -> Dict[str, Any]:
        """获取特征质量报告"""
        report = {
            'total_features': len(self.feature_types),
            'feature_types_distribution': {},
            'scaler_usage': {},
            'quality_summary': {
                'high_quality': 0,
                'medium_quality': 0,
                'low_quality': 0,
                'invalid': 0
            }
        }
        
        # 统计特征类型分布
        for feature_type in self.feature_types.values():
            report['feature_types_distribution'][feature_type] = \
                report['feature_types_distribution'].get(feature_type, 0) + 1
        
        # 统计标准化器使用情况
        for scaler in self.feature_scalers.values():
            scaler_name = type(scaler).__name__
            report['scaler_usage'][scaler_name] = \
                report['scaler_usage'].get(scaler_name, 0) + 1
        
        # 评估特征质量
        for feature_name, stats in self.feature_stats.items():
            if stats['missing_rate'] > 0.5:
                report['quality_summary']['invalid'] += 1
            elif stats['missing_rate'] > 0.2 or stats['zero_rate'] > 0.8:
                report['quality_summary']['low_quality'] += 1
            elif stats['missing_rate'] > 0.05 or stats['unique_count'] < 10:
                report['quality_summary']['medium_quality'] += 1
            else:
                report['quality_summary']['high_quality'] += 1
        
        return report
