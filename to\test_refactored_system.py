#!/usr/bin/env python3
"""
测试重构后的变化点预测系统
验证relations_df的充分利用和现有特征架构的集成
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_test_data():
    """创建测试数据"""
    np.random.seed(42)
    
    # 创建股票数据
    n_stocks = 50
    n_days = 20
    
    stock_data = []
    for i in range(n_stocks):
        stock_code = f"00{i:04d}.SZ"
        for j in range(n_days):
            date_str = f"2024{(j % 12) + 1:02d}{(j % 28) + 1:02d}"
            
            # 模拟价格数据
            close = np.random.uniform(10, 50)
            volume = np.random.uniform(10000, 1000000)
            pct_chg = np.random.uniform(-10, 10)
            
            # 模拟技术指标
            ma5 = close * np.random.uniform(0.95, 1.05)
            ma20 = close * np.random.uniform(0.9, 1.1)
            rsi = np.random.uniform(20, 80)
            
            # 模拟future_return_2d（小数形式）
            future_return_2d = np.random.uniform(-0.1, 0.1)
            
            stock_data.append({
                'stock_code': stock_code,
                'date_str': date_str,
                'close': close,
                'volume': volume,
                'pct_chg': pct_chg,
                'preClose': close * (1 + np.random.uniform(-0.05, 0.05)),
                'ma5': ma5,
                'ma20': ma20,
                'rsi': rsi,
                'future_return_2d': future_return_2d,
                'most_relevant_sector': f"sector_{i % 5}",  # 5个板块
                'sector_correlation_score': np.random.uniform(0.3, 0.9)
            })
    
    # 创建板块数据
    sector_data = []
    for i in range(5):
        sector_code = f"sector_{i}"
        for j in range(n_days):
            date_str = f"2024{(j % 12) + 1:02d}{(j % 28) + 1:02d}"
            
            sector_data.append({
                'sector_code': sector_code,
                'date_str': date_str,
                'change_percent': np.random.uniform(-5, 5),
                'volume': np.random.uniform(100000, 10000000),
                'amount': np.random.uniform(1000000, 100000000)
            })
    
    # 创建关联关系数据
    relations_data = []
    for i in range(n_stocks):
        stock_code = f"00{i:04d}.SZ"
        sector_code = f"sector_{i % 5}"
        
        relations_data.append({
            'stock_code': stock_code,
            'sector_code': sector_code,
            'weight': np.random.uniform(0.1, 1.0)
        })
    
    return (pd.DataFrame(stock_data), 
            pd.DataFrame(sector_data), 
            pd.DataFrame(relations_data))

def test_stock_sector_mapping():
    """测试股票-板块映射构建"""
    logger.info("=== 测试股票-板块映射构建 ===")
    
    try:
        from train_sector_models import SectorModelTrainer
        
        stock_data, sector_data, relations_data = create_test_data()
        
        trainer = SectorModelTrainer()
        
        # 测试股票-板块映射构建
        stock_sector_mapping = trainer._build_stock_sector_mapping(relations_data)
        
        logger.info(f"✅ 股票-板块映射构建成功")
        logger.info(f"映射覆盖股票数量: {len(stock_sector_mapping)}")
        logger.info(f"示例映射: {dict(list(stock_sector_mapping.items())[:3])}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 股票-板块映射构建测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_feature_enhancement():
    """测试特征增强"""
    logger.info("=== 测试特征增强 ===")
    
    try:
        from train_sector_models import SectorModelTrainer
        
        stock_data, sector_data, relations_data = create_test_data()
        
        trainer = SectorModelTrainer()
        
        # 测试特征增强
        enhanced_features = trainer._enhance_predict_features_with_existing_loaders(
            stock_data.sample(n=20, random_state=42), 
            relations_data
        )
        
        logger.info(f"✅ 特征增强成功")
        logger.info(f"原始特征数量: {stock_data.shape[1]}")
        logger.info(f"增强后特征数量: {enhanced_features.shape[1]}")
        logger.info(f"新增特征数量: {enhanced_features.shape[1] - stock_data.shape[1]}")
        
        # 检查是否有板块特征
        sector_features = [col for col in enhanced_features.columns if col.startswith('sector_')]
        logger.info(f"板块特征数量: {len(sector_features)}")
        if sector_features:
            logger.info(f"板块特征示例: {sector_features[:3]}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 特征增强测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_change_point_analysis():
    """测试变化点分析"""
    logger.info("=== 测试变化点分析 ===")
    
    try:
        from train_sector_models import SectorModelTrainer
        
        stock_data, sector_data, relations_data = create_test_data()
        
        trainer = SectorModelTrainer()
        
        # 模拟变化点检测结果
        change_point_results = {}
        for stock_code in stock_data['stock_code'].unique()[:10]:  # 只测试前10只股票
            stock_dates = stock_data[stock_data['stock_code'] == stock_code]['date_str'].tolist()
            stock_returns = stock_data[stock_data['stock_code'] == stock_code]['future_return_2d'].tolist()
            
            change_point_results[stock_code] = {
                'change_points': [1, 3, 5],  # 模拟变化点索引
                'dates': stock_dates[:6],  # 前6个日期
                'returns': stock_returns[:6]  # 对应的收益率
            }
        
        # 测试变化点模式分析
        patterns = trainer._analyze_stock_change_point_patterns(
            stock_data, change_point_results, relations_data
        )
        
        if patterns and 'patterns' in patterns:
            logger.info(f"✅ 变化点分析成功")
            logger.info(f"生成的模式数量: {len(patterns.get('patterns', []))}")
            logger.info(f"特征重要性数量: {len(patterns.get('feature_importance', {}))}")
            logger.info(f"股票-板块映射数量: {len(patterns.get('stock_sector_mapping', {}))}")
            return True
        else:
            logger.error("❌ 变化点分析未生成有效模式")
            return False
        
    except Exception as e:
        logger.error(f"❌ 变化点分析测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_prediction_with_relations():
    """测试使用relations_df的预测"""
    logger.info("=== 测试使用relations_df的预测 ===")
    
    try:
        from train_sector_models import SectorModelTrainer
        
        stock_data, sector_data, relations_data = create_test_data()
        
        trainer = SectorModelTrainer()
        
        # 先进行变化点分析
        change_point_results = {}
        for stock_code in stock_data['stock_code'].unique()[:10]:
            stock_dates = stock_data[stock_data['stock_code'] == stock_code]['date_str'].tolist()
            stock_returns = stock_data[stock_data['stock_code'] == stock_code]['future_return_2d'].tolist()
            
            change_point_results[stock_code] = {
                'change_points': [1, 3, 5],
                'dates': stock_dates[:6],
                'returns': stock_returns[:6]
            }
        
        patterns = trainer._analyze_stock_change_point_patterns(
            stock_data, change_point_results, relations_data
        )
        
        if not patterns:
            logger.error("❌ 无法获取训练模式")
            return False
        
        # 创建预测数据
        predict_data = stock_data.sample(n=20, random_state=42).copy()
        
        # 测试相似度匹配预测
        predictions = trainer._predict_with_similarity_matching(
            predict_data, relations_data, patterns
        )
        
        if predictions:
            logger.info(f"✅ 预测测试成功")
            logger.info(f"预测结果数量: {len(predictions)}")
            
            # 分析预测结果
            scores = [pred.get('prediction_score', 0) for pred in predictions.values()]
            logger.info(f"预测得分范围: [{min(scores):.6f}, {max(scores):.6f}]")
            logger.info(f"预测得分平均值: {np.mean(scores):.6f}")
            logger.info(f"非零预测数量: {sum(1 for s in scores if abs(s) > 0.001)}")
            
            return True
        else:
            logger.error("❌ 预测未生成结果")
            return False
        
    except Exception as e:
        logger.error(f"❌ 预测测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主测试函数"""
    logger.info("开始测试重构后的变化点预测系统...")
    
    start_time = time.time()
    success_count = 0
    total_tests = 4
    
    # 测试列表
    tests = [
        ("股票-板块映射构建", test_stock_sector_mapping),
        ("特征增强", test_feature_enhancement),
        ("变化点分析", test_change_point_analysis),
        ("使用relations_df的预测", test_prediction_with_relations)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"开始测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} 测试通过")
                success_count += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试出错: {e}")
    
    # 总结
    elapsed_time = time.time() - start_time
    logger.info(f"\n{'='*60}")
    logger.info(f"测试完成！")
    logger.info(f"总测试数: {total_tests}")
    logger.info(f"通过测试: {success_count}")
    logger.info(f"失败测试: {total_tests - success_count}")
    logger.info(f"成功率: {success_count/total_tests*100:.1f}%")
    logger.info(f"总耗时: {elapsed_time:.2f} 秒")
    
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！重构成功！")
        logger.info("✅ relations_df现在被充分利用")
        logger.info("✅ 基于现有特征架构进行扩展")
        logger.info("✅ 删除了重复和无效代码")
        logger.info("✅ 保持了系统的简洁性")
    else:
        logger.error("❌ 部分测试失败，需要进一步调试")
    
    return success_count == total_tests

if __name__ == "__main__":
    main()
