#!/usr/bin/env python3
"""
测试分别预测和评估功能
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from train_sector_models import SectorModelTrainer

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_test_stock_data():
    """创建测试股票数据"""
    np.random.seed(42)
    n_stocks = 100
    n_days = 30
    
    stock_data = []
    for i in range(n_stocks):
        stock_code = f"00{i:04d}"
        for j in range(n_days):
            date = f"2024{j+1:02d}01"
            
            # 模拟价格数据
            close = np.random.uniform(10, 50)
            volume = np.random.uniform(10000, 1000000)
            pct_chg = np.random.uniform(-10, 10)
            
            # 模拟future_return_2d（小数形式，如0.05表示5%）
            future_return_2d = np.random.uniform(-0.1, 0.1)
            
            stock_data.append({
                'stock_code': stock_code,
                'date_str': date,
                'close': close,
                'volume': volume,
                'pct_chg': pct_chg,
                'preClose': close * (1 + np.random.uniform(-0.05, 0.05)),
                'future_return_2d': future_return_2d
            })
    
    return pd.DataFrame(stock_data)

def create_test_predictions():
    """创建测试预测数据"""
    np.random.seed(42)
    
    # 相似度预测结果
    similarity_predictions = {}
    for i in range(50):
        stock_code = f"00{i:04d}"
        date = f"2024{(i % 20) + 1:02d}01"
        key = f"{stock_code}_{date}"
        
        similarity_predictions[key] = {
            'stock_code': stock_code,
            'date': date,
            'prediction_score': np.random.uniform(-0.05, 0.05),  # 小数形式
            'positive_similarity': np.random.uniform(0.3, 0.8),
            'prediction_binary': np.random.choice([0, 1])
        }
    
    # 模型预测结果
    model_predictions = {}
    for i in range(30, 80):  # 部分重叠
        stock_code = f"00{i:04d}"
        date = f"2024{(i % 20) + 1:02d}01"
        key = f"{stock_code}_{date}"
        
        model_predictions[key] = {
            'stock_code': stock_code,
            'date': date,
            'prediction_score': np.random.uniform(-0.03, 0.03),  # 小数形式
            'positive_similarity': np.random.uniform(0.4, 0.7),
            'prediction_binary': np.random.choice([0, 1])
        }
    
    return similarity_predictions, model_predictions

def test_separate_ranking():
    """测试分别排名功能"""
    logger.info("=== 测试分别排名功能 ===")
    
    trainer = SectorModelTrainer()
    stock_df = create_test_stock_data()
    similarity_predictions, model_predictions = create_test_predictions()
    
    # 测试相似度预测排名
    logger.info("测试相似度预测排名...")
    similarity_selected = trainer._rank_and_select_stocks_by_method(
        similarity_predictions, 
        top_n=10, 
        stock_features_df=stock_df,
        method_name="similarity_matching"
    )
    
    # 测试模型预测排名
    logger.info("测试模型预测排名...")
    model_selected = trainer._rank_and_select_stocks_by_method(
        model_predictions, 
        top_n=10, 
        stock_features_df=stock_df,
        method_name="trained_model"
    )
    
    # 验证结果
    success = True
    
    # 检查相似度预测结果
    if similarity_selected and similarity_selected.get('method_name') == 'similarity_matching':
        logger.info(f"✅ 相似度预测排名成功: 选出 {similarity_selected.get('selected_count', 0)} 只股票")
        
        # 检查预测收益率是否为百分比形式
        if similarity_selected.get('selected_stocks'):
            sample_stock = similarity_selected['selected_stocks'][0]
            predicted_return = sample_stock.get('predicted_return', 0)
            if abs(predicted_return) > 0.1:  # 应该是百分比形式
                logger.info(f"✅ 相似度预测收益率已转换为百分比: {predicted_return:.4f}%")
            else:
                logger.error(f"❌ 相似度预测收益率未正确转换: {predicted_return}")
                success = False
    else:
        logger.error("❌ 相似度预测排名失败")
        success = False
    
    # 检查模型预测结果
    if model_selected and model_selected.get('method_name') == 'trained_model':
        logger.info(f"✅ 模型预测排名成功: 选出 {model_selected.get('selected_count', 0)} 只股票")
        
        # 检查预测收益率是否为百分比形式
        if model_selected.get('selected_stocks'):
            sample_stock = model_selected['selected_stocks'][0]
            predicted_return = sample_stock.get('predicted_return', 0)
            if abs(predicted_return) > 0.1:  # 应该是百分比形式
                logger.info(f"✅ 模型预测收益率已转换为百分比: {predicted_return:.4f}%")
            else:
                logger.error(f"❌ 模型预测收益率未正确转换: {predicted_return}")
                success = False
    else:
        logger.error("❌ 模型预测排名失败")
        success = False
    
    return success, similarity_selected, model_selected

def test_unified_return_calculation():
    """测试统一收益率计算"""
    logger.info("=== 测试统一收益率计算 ===")
    
    trainer = SectorModelTrainer()
    stock_df = create_test_stock_data()
    
    # 测试获取统一计量单位的实际收益率
    test_cases = [
        ('000001', '20240101'),
        ('000002', '20240102'),
        ('000003', '20240103')
    ]
    
    success = True
    for stock_code, date in test_cases:
        actual_return = trainer._get_actual_return_unified(
            stock_code, date, stock_df, evaluation_days=2
        )
        
        if actual_return is not None:
            logger.info(f"✅ 股票 {stock_code} 在 {date} 的实际收益率: {actual_return:.4f}%")
            
            # 检查是否为百分比形式
            if abs(actual_return) < 100:  # 合理的百分比范围
                logger.info(f"✅ 实际收益率格式正确（百分比）")
            else:
                logger.warning(f"⚠️ 实际收益率可能过大: {actual_return}")
        else:
            logger.warning(f"⚠️ 无法获取股票 {stock_code} 在 {date} 的实际收益率")
    
    return success

def test_separate_evaluation():
    """测试分别评估功能"""
    logger.info("=== 测试分别评估功能 ===")
    
    trainer = SectorModelTrainer()
    stock_df = create_test_stock_data()
    
    # 获取测试选股结果
    success, similarity_selected, model_selected = test_separate_ranking()
    if not success:
        return False
    
    # 测试相似度预测评估
    logger.info("测试相似度预测评估...")
    similarity_evaluation = trainer._evaluate_selected_stocks_with_method(
        similarity_selected, stock_df, "similarity_matching"
    )
    
    # 测试模型预测评估
    logger.info("测试模型预测评估...")
    model_evaluation = trainer._evaluate_selected_stocks_with_method(
        model_selected, stock_df, "trained_model"
    )
    
    # 验证评估结果
    eval_success = True
    
    # 检查相似度预测评估
    if similarity_evaluation and similarity_evaluation.get('method_name') == 'similarity_matching':
        summary = similarity_evaluation.get('evaluation_summary', {})
        logger.info(f"✅ 相似度预测评估成功:")
        logger.info(f"  - 评估股票数: {summary.get('evaluated_stocks', 0)}")
        logger.info(f"  - 平均实际收益率: {summary.get('avg_actual_return', 0):.4f}%")
        logger.info(f"  - 方向准确率: {summary.get('direction_accuracy', 0):.2%}")
        logger.info(f"  - 胜率: {summary.get('win_rate', 0):.2%}")
        logger.info(f"  - 性能评级: {summary.get('performance_rating', '未知')}")
    else:
        logger.error("❌ 相似度预测评估失败")
        eval_success = False
    
    # 检查模型预测评估
    if model_evaluation and model_evaluation.get('method_name') == 'trained_model':
        summary = model_evaluation.get('evaluation_summary', {})
        logger.info(f"✅ 模型预测评估成功:")
        logger.info(f"  - 评估股票数: {summary.get('evaluated_stocks', 0)}")
        logger.info(f"  - 平均实际收益率: {summary.get('avg_actual_return', 0):.4f}%")
        logger.info(f"  - 方向准确率: {summary.get('direction_accuracy', 0):.2%}")
        logger.info(f"  - 胜率: {summary.get('win_rate', 0):.2%}")
        logger.info(f"  - 性能评级: {summary.get('performance_rating', '未知')}")
    else:
        logger.error("❌ 模型预测评估失败")
        eval_success = False
    
    return eval_success, similarity_evaluation, model_evaluation

def test_method_comparison():
    """测试方法对比功能"""
    logger.info("=== 测试方法对比功能 ===")
    
    trainer = SectorModelTrainer()
    
    # 获取评估结果
    eval_success, similarity_evaluation, model_evaluation = test_separate_evaluation()
    if not eval_success:
        return False
    
    # 测试对比分析
    comparison = trainer._compare_method_evaluations(similarity_evaluation, model_evaluation)
    
    if comparison and 'comparison_summary' in comparison:
        logger.info("✅ 方法对比分析成功:")
        
        summary = comparison['comparison_summary']
        logger.info(f"  - 相似度匹配得分: {summary.get('similarity_score', 0):.2f}")
        logger.info(f"  - 模型预测得分: {summary.get('model_score', 0):.2f}")
        logger.info(f"  - 总体获胜者: {summary.get('overall_winner', '未知')}")
        
        if 'winner_analysis' in comparison:
            winner_analysis = comparison['winner_analysis']
            logger.info(f"  - 推荐: {winner_analysis.get('recommendation', '无推荐')}")
        
        return True
    else:
        logger.error("❌ 方法对比分析失败")
        return False

def test_unit_consistency():
    """测试计量单位一致性"""
    logger.info("=== 测试计量单位一致性 ===")
    
    trainer = SectorModelTrainer()
    
    # 测试预测得分转换
    test_predictions = {
        'test1': {
            'stock_code': '000001',
            'date': '20240101',
            'prediction_score': 0.025,  # 小数形式
            'positive_similarity': 0.6,
            'prediction_binary': 1
        }
    }
    
    processed_df = trainer._vectorized_data_preprocessing_for_method(
        test_predictions, "test_method"
    )
    
    if not processed_df.empty:
        prediction_score_pct = processed_df['prediction_score_pct'].iloc[0]
        prediction_score_original = processed_df['prediction_score_original'].iloc[0]
        
        logger.info(f"原始预测得分: {prediction_score_original}")
        logger.info(f"转换后预测得分: {prediction_score_pct}%")
        
        # 检查转换是否正确
        if abs(prediction_score_pct - prediction_score_original * 100) < 0.001:
            logger.info("✅ 预测得分转换正确")
            return True
        else:
            logger.error("❌ 预测得分转换错误")
            return False
    else:
        logger.error("❌ 数据预处理失败")
        return False

def main():
    """主测试函数"""
    logger.info("开始测试分别预测和评估功能...")
    
    success = True
    
    try:
        # 测试1: 分别排名功能
        ranking_success, _, _ = test_separate_ranking()
        if ranking_success:
            logger.info("✅ 分别排名测试通过")
        else:
            logger.error("❌ 分别排名测试失败")
            success = False
        
        # 测试2: 统一收益率计算
        return_success = test_unified_return_calculation()
        if return_success:
            logger.info("✅ 统一收益率计算测试通过")
        else:
            logger.error("❌ 统一收益率计算测试失败")
            success = False
        
        # 测试3: 分别评估功能
        eval_result = test_separate_evaluation()
        if isinstance(eval_result, tuple) and len(eval_result) >= 3:
            eval_success, _, _ = eval_result
        else:
            eval_success = eval_result

        if eval_success:
            logger.info("✅ 分别评估测试通过")
        else:
            logger.error("❌ 分别评估测试失败")
            success = False
        
        # 测试4: 方法对比功能
        comparison_success = test_method_comparison()
        if comparison_success:
            logger.info("✅ 方法对比测试通过")
        else:
            logger.error("❌ 方法对比测试失败")
            success = False
        
        # 测试5: 计量单位一致性
        unit_success = test_unit_consistency()
        if unit_success:
            logger.info("✅ 计量单位一致性测试通过")
        else:
            logger.error("❌ 计量单位一致性测试失败")
            success = False
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        success = False
    
    if success:
        logger.info("🎉 所有分别预测和评估测试通过！")
        logger.info("✅ 相似度预测和模型预测分别排名 - 独立选股")
        logger.info("✅ 统一计量单位 - predicted_return和actual_return都使用百分比")
        logger.info("✅ 分别评估和对比 - 详细的性能对比分析")
        logger.info("✅ 方法推荐 - 基于综合评分的智能推荐")
    else:
        logger.error("❌ 部分测试失败，需要进一步修复")
    
    return success

if __name__ == "__main__":
    main()
