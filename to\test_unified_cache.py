#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一缓存修复的脚本
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from train_sector_models import SectorModelTrainer

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_unified_cache.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

def test_cache_functionality():
    """测试缓存功能"""
    logger = logging.getLogger(__name__)
    logger.info("=" * 50)
    logger.info("测试统一缓存功能")
    logger.info("=" * 50)
    
    try:
        trainer = SectorModelTrainer()
        
        # 创建测试数据
        stock_features_df = pd.DataFrame({
            'stock_code': ['000001', '000002', '000001', '000002'],
            'date_str': ['20240101', '20240101', '20240102', '20240102'],
            'close': [10.0, 20.0, 10.5, 20.5],
            'volume': [1000000, 2000000, 1100000, 2100000],
            'pct_chg': [2.0, 1.5, 5.0, 2.5],
            'turnover_rate': [3.0, 2.5, 3.5, 2.8],
            'amplitude': [5.0, 4.0, 6.0, 4.5]
        })
        
        sector_features_df = pd.DataFrame({
            'sector_code': ['TGN上证380成份股', 'TGN央企国企改革', 'TGN上证380成份股', 'TGN央企国企改革'],
            'date': ['20240101', '20240101', '20240102', '20240102'],
            'avg_pct_chg': [2.5, 1.8, 3.2, 2.1],
            'avg_volume': [5000000, 4000000, 5500000, 4200000],
            'limit_up_count': [5, 3, 6, 4],
            'sector_strength': [85.5, 72.3, 88.1, 75.6]
        })
        
        change_points = {
            '000001': {
                'change_points': [True, False, True, False],
                'dates': ['20240101', '20240102'],
                'returns': [0.05, 0.03]
            },
            '000002': {
                'change_points': [True, False, True, False],
                'dates': ['20240101', '20240102'],
                'returns': [0.02, 0.04]
            }
        }
        
        logger.info("第一次调用（应该计算并缓存）...")
        result1 = trainer.collect_stock_sector_change_point_features(
            stock_features_df, sector_features_df, change_points, 
            is_training=True, use_cache=True
        )
        
        if result1 is not None and len(result1) > 0:
            logger.info(f"✅ 第一次调用成功，返回 {len(result1)} 条记录，{len(result1.columns)} 个特征")
            
            # 检查most_relevant_sector字段
            if 'most_relevant_sector' in result1.columns:
                sector_counts = result1['most_relevant_sector'].value_counts()
                logger.info(f"第一次调用板块分布: {dict(sector_counts)}")
                unknown_count = (result1['most_relevant_sector'] == 'unknown').sum()
                if unknown_count == 0:
                    logger.info("✅ 第一次调用没有unknown板块")
                else:
                    logger.warning(f"⚠️ 第一次调用发现 {unknown_count} 个unknown板块")
            else:
                logger.error("❌ 第一次调用缺少most_relevant_sector字段")
                return False
        else:
            logger.error("❌ 第一次调用失败")
            return False
        
        logger.info("第二次调用（应该从缓存加载）...")
        result2 = trainer.collect_stock_sector_change_point_features(
            stock_features_df, sector_features_df, change_points, 
            is_training=True, use_cache=True
        )
        
        if result2 is not None and len(result2) > 0:
            logger.info(f"✅ 第二次调用成功，返回 {len(result2)} 条记录，{len(result2.columns)} 个特征")
            
            # 检查most_relevant_sector字段
            if 'most_relevant_sector' in result2.columns:
                sector_counts = result2['most_relevant_sector'].value_counts()
                logger.info(f"第二次调用板块分布: {dict(sector_counts)}")
                unknown_count = (result2['most_relevant_sector'] == 'unknown').sum()
                if unknown_count == 0:
                    logger.info("✅ 第二次调用没有unknown板块")
                else:
                    logger.warning(f"⚠️ 第二次调用发现 {unknown_count} 个unknown板块")
            else:
                logger.error("❌ 第二次调用缺少most_relevant_sector字段")
                return False
            
            # 比较两次结果是否一致
            if len(result1) == len(result2) and len(result1.columns) == len(result2.columns):
                logger.info("✅ 两次调用结果大小一致")
                
                # 检查most_relevant_sector字段是否一致
                if 'most_relevant_sector' in result1.columns and 'most_relevant_sector' in result2.columns:
                    sector1 = result1['most_relevant_sector'].value_counts()
                    sector2 = result2['most_relevant_sector'].value_counts()
                    if sector1.equals(sector2):
                        logger.info("✅ 两次调用的板块分布完全一致")
                        return True
                    else:
                        logger.warning("⚠️ 两次调用的板块分布不一致")
                        logger.info(f"第一次: {dict(sector1)}")
                        logger.info(f"第二次: {dict(sector2)}")
                        return False
                else:
                    logger.error("❌ 无法比较板块分布")
                    return False
            else:
                logger.warning("⚠️ 两次调用结果大小不一致")
                return False
        else:
            logger.error("❌ 第二次调用失败")
            return False
            
    except Exception as e:
        logger.error(f"测试缓存功能时出错: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def main():
    """主测试函数"""
    logger = setup_logging()
    logger.info("开始测试统一缓存修复...")
    
    # 清理旧的缓存文件
    cache_dir = Path("to/models/sector_models/features_cache")
    if cache_dir.exists():
        for cache_file in cache_dir.glob("*.pkl"):
            try:
                cache_file.unlink()
                logger.info(f"删除旧缓存文件: {cache_file}")
            except Exception as e:
                logger.warning(f"删除缓存文件失败: {e}")
    
    # 测试缓存功能
    cache_result = test_cache_functionality()
    
    logger.info("=" * 50)
    logger.info("测试结果总结")
    logger.info("=" * 50)
    
    if cache_result:
        logger.info("🎉 统一缓存功能测试通过！")
        logger.info("✅ 缓存正确保存和加载完整特征")
        logger.info("✅ most_relevant_sector字段正确保留")
        logger.info("✅ 不再出现unknown板块问题")
    else:
        logger.error("⚠️ 统一缓存功能测试失败")
        logger.error("需要进一步检查缓存逻辑")
    
    return cache_result

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
