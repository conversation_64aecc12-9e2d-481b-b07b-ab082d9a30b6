#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
板块轮动模型训练
用于训练基于板块特征的板块轮动预测模型，结合市场状态特征，优化板块选择策略。

注意：此文件已被重构为更模块化的架构。
推荐使用新的API进行开发和维护：
- to/sector_model.py: 主API入口，提供板块轮动模型的训练、预测和回测功能
- to/data_processor.py: 数据处理模块
- to/feature_processor.py: 特征处理模块
- to/model_manager.py: 模型管理模块

```

此文件保留用于兼容现有的调用方式。
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import argparse
from pathlib import Path
import json
import traceback
import gc
from concurrent.futures import ProcessPoolExecutor, as_completed, ThreadPoolExecutor
import xgboost as xgb
import lightgbm as lgb
from sklearn.metrics import mean_squared_error, r2_score, accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, confusion_matrix
from sklearn.model_selection import train_test_split
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.preprocessing import StandardScaler
from torch.utils.data import TensorDataset, DataLoader
from sklearn.linear_model import Ridge
import copy
import pickle
import inspect
import bisect
import joblib
import numpy as np
import pandas as pd
import json
import torch
import torch.nn as nn
import logging
import os
import time
import math
import inspect
from pathlib import Path
import matplotlib.pyplot as plt
from scipy import stats
from concurrent.futures import ProcessPoolExecutor, as_completed
import hashlib
import functools
from sklearn.ensemble import RandomForestClassifier, GradientBoostingRegressor
from sklearn.dummy import DummyClassifier
from scipy.stats import spearmanr, kendalltau
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import multiprocessing



# 导入logger配置 - 修改为相对导入
try:
    from logger_config import configure_logger
except ImportError:
    # 添加当前目录到sys.path
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    from logger_config import configure_logger

# 将原来的logger配置替换为新的配置
logger = configure_logger(__name__, 'train_sector_models.log')

# 自定义JSON编码器，用于处理NumPy和其他特殊类型
class NumpyEncoder(json.JSONEncoder):
    """扩展JSON编码器，处理NumPy和其他特殊类型"""
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.bool_):  # 添加对numpy bool_类型的支持
            return bool(obj)
        elif isinstance(obj, (datetime, pd.Timestamp)):
            return obj.isoformat()
        elif pd.isna(obj):
            return None
        return super(NumpyEncoder, self).default(obj)

# 获取项目根目录
def get_project_root():
    """获取项目根目录"""
    current_file = Path(__file__).resolve()
    return current_file.parent.parent

# 路径设置
def get_data_path(relative_path=None):
    """获取数据目录路径"""
    project_root = get_project_root()
    data_path = project_root / "to" / "local_data"
    if relative_path:
        return data_path / relative_path
    return data_path

def get_model_path():
    """获取模型目录路径"""
    project_root = get_project_root()
    model_path = project_root / "to" / "models" / "sector_models"
    return model_path

def ensure_dir(path):
    """确保目录存在"""
    path = Path(path)
    path.mkdir(parents=True, exist_ok=True)
    return path

class SectorModelTrainer:
    """板块模型训练器"""
    
    def __init__(self, model_dir=None):
        """
        初始化模型训练器
        
        Args:
            model_dir: 模型保存目录
        """
        self.logger = logger
        
        # 设置模型目录
        self.model_dir = Path(model_dir) if model_dir else get_model_path()
        ensure_dir(self.model_dir)
        
        # 特征目录
        self.sector_features_dir = get_data_path("sector_features")
        self.market_status_dir = get_data_path("market_status/daily")
         
        # 交易日历
        self.trading_days = []
        self._load_trading_calendar()
        
        # 添加特征缓存目录
        self.features_cache_dir = Path(get_data_path(), "features_cache")
        ensure_dir(self.features_cache_dir)

        self.logger.info(f"板块模型训练器初始化完成，模型目录: {self.model_dir}")



    def _check_feature_quality(self, stock_features_df, sector_features_df):
        """
        高效检查特征数据质量，找出全为0或NaN的特征

        Args:
            stock_features_df: 个股特征DataFrame
            sector_features_df: 板块特征DataFrame
        """
        import time
        start_time = time.time()

        self.logger.info("=== 开始特征质量检查 ===")

        # 检查个股特征
        if stock_features_df is not None and not stock_features_df.empty:
            self.logger.info(f"个股特征数据形状: {stock_features_df.shape}")

            # 排除非数值列
            exclude_cols = ['stock_code', 'date_str', 'date']
            numeric_cols = [col for col in stock_features_df.columns
                          if col not in exclude_cols and stock_features_df[col].dtype in ['float64', 'float32', 'int64', 'int32']]

            self.logger.info(f"个股数值特征列数: {len(numeric_cols)}")

            # 高效检查：使用向量化操作
            invalid_features = []

            for col in numeric_cols:
                # 检查是否全为0或NaN
                series = stock_features_df[col]
                is_all_zero = (series == 0).all()
                is_all_nan = series.isna().all()
                is_all_zero_or_nan = ((series == 0) | series.isna()).all()

                if is_all_zero:
                    invalid_features.append(f"{col} (全为0)")
                elif is_all_nan:
                    invalid_features.append(f"{col} (全为NaN)")
                elif is_all_zero_or_nan:
                    invalid_features.append(f"{col} (全为0或NaN)")

            if invalid_features:
                self.logger.warning(f"个股无效特征 ({len(invalid_features)}个):")
                for feature in invalid_features:
                    self.logger.warning(f"  - {feature}")
            else:
                self.logger.info("个股特征质量良好，未发现全为0或NaN的特征")

        # 检查板块特征
        if sector_features_df is not None and not sector_features_df.empty:
            self.logger.info(f"板块特征数据形状: {sector_features_df.shape}")

            # 排除非数值列
            exclude_cols = ['sector_code', 'sector_name', 'date_str', 'date']
            numeric_cols = [col for col in sector_features_df.columns
                          if col not in exclude_cols and sector_features_df[col].dtype in ['float64', 'float32', 'int64', 'int32']]

            self.logger.info(f"板块数值特征列数: {len(numeric_cols)}")

            # 高效检查：使用向量化操作
            invalid_features = []

            for col in numeric_cols:
                # 检查是否全为0或NaN
                series = sector_features_df[col]
                is_all_zero = (series == 0).all()
                is_all_nan = series.isna().all()
                is_all_zero_or_nan = ((series == 0) | series.isna()).all()

                if is_all_zero:
                    invalid_features.append(f"{col} (全为0)")
                elif is_all_nan:
                    invalid_features.append(f"{col} (全为NaN)")
                elif is_all_zero_or_nan:
                    invalid_features.append(f"{col} (全为0或NaN)")

            if invalid_features:
                self.logger.warning(f"板块无效特征 ({len(invalid_features)}个):")
                for feature in invalid_features:
                    self.logger.warning(f"  - {feature}")
            else:
                self.logger.info("板块特征质量良好，未发现全为0或NaN的特征")

        elapsed_time = time.time() - start_time
        self.logger.info(f"=== 特征质量检查完成，用时 {elapsed_time:.2f} 秒 ===")

    def _comprehensive_data_filtering(self, stock_features_df, sector_features_df):
        """
        综合数据过滤策略 - 在模型训练前清理无效数据

        Args:
            stock_features_df: 个股特征DataFrame
            sector_features_df: 板块特征DataFrame

        Returns:
            tuple: (过滤后的个股特征, 过滤后的板块特征, 过滤统计信息)
        """
        self.logger.info("=== 开始综合数据过滤 ===")

        filter_stats = {
            'original_stock_count': len(stock_features_df) if stock_features_df is not None else 0,
            'original_sector_count': len(sector_features_df) if sector_features_df is not None else 0,
            'filtered_stock_count': 0,
            'filtered_sector_count': 0,
            'removed_suspended_stocks': 0,
            'removed_invalid_features': 0,
            'removed_insufficient_data': 0,
            'feature_coverage_after_filter': {}
        }

        # 1. 过滤个股数据
        if stock_features_df is not None and not stock_features_df.empty:
            filtered_stock_df = self._filter_stock_data(stock_features_df, filter_stats)
        else:
            filtered_stock_df = stock_features_df

        # 2. 过滤板块数据
        if sector_features_df is not None and not sector_features_df.empty:
            filtered_sector_df = self._filter_sector_data(sector_features_df, filter_stats)
        else:
            filtered_sector_df = sector_features_df

        # 3. 验证过滤后的数据质量
        self._validate_filtered_data(filtered_stock_df, filtered_sector_df, filter_stats)

        # 4. 输出过滤统计
        self._log_filter_statistics(filter_stats)

        return filtered_stock_df, filtered_sector_df, filter_stats

    def _filter_stock_data(self, stock_df, filter_stats):
        """
        过滤个股数据 - 移除停牌股票和无效数据点

        Args:
            stock_df: 原始个股数据
            filter_stats: 过滤统计信息

        Returns:
            DataFrame: 过滤后的个股数据
        """
        self.logger.info(f"开始过滤个股数据，原始数据: {len(stock_df)} 条记录")

        # 1. 移除停牌股票数据点
        valid_mask = self._create_valid_stock_mask(stock_df)
        filtered_df = stock_df[valid_mask].copy()

        suspended_removed = len(stock_df) - len(filtered_df)
        filter_stats['removed_suspended_stocks'] = suspended_removed
        self.logger.info(f"移除停牌/无效股票数据点: {suspended_removed} 条")

        # 2. 移除关键特征缺失的数据点
        critical_features = ['close', 'volume', 'pct_chg']
        before_count = len(filtered_df)

        for feature in critical_features:
            if feature in filtered_df.columns:
                # 移除该特征为NaN或异常值的记录
                valid_feature_mask = (
                    filtered_df[feature].notna() &
                    (filtered_df[feature] != 0) if feature == 'volume' else filtered_df[feature].notna()
                )
                filtered_df = filtered_df[valid_feature_mask]

        critical_removed = before_count - len(filtered_df)
        filter_stats['removed_invalid_features'] += critical_removed
        self.logger.info(f"移除关键特征缺失的数据点: {critical_removed} 条")

        # 3. 按股票分组，移除数据不足的股票
        stock_counts = filtered_df['stock_code'].value_counts()
        min_data_points = 20  # 每只股票至少需要20个数据点
        valid_stocks = stock_counts[stock_counts >= min_data_points].index

        before_count = len(filtered_df)
        filtered_df = filtered_df[filtered_df['stock_code'].isin(valid_stocks)]
        insufficient_removed = before_count - len(filtered_df)
        filter_stats['removed_insufficient_data'] = insufficient_removed
        self.logger.info(f"移除数据不足的股票记录: {insufficient_removed} 条")

        # 4. 特征质量过滤
        filtered_df = self._filter_features_by_quality(filtered_df, filter_stats)

        filter_stats['filtered_stock_count'] = len(filtered_df)
        self.logger.info(f"个股数据过滤完成，保留: {len(filtered_df)} 条记录")

        return filtered_df

    def _create_valid_stock_mask(self, stock_df):
        """
        创建有效股票数据掩码 - 识别停牌和异常数据

        Args:
            stock_df: 股票数据DataFrame

        Returns:
            Series: 布尔掩码，True表示有效数据
        """
        # 基础有效性检查
        valid_mask = pd.Series(True, index=stock_df.index)

        # 1. 价格数据有效性
        if 'close' in stock_df.columns:
            valid_mask &= (stock_df['close'].notna() & (stock_df['close'] > 0))

        # 2. 成交量有效性（成交量为0可能表示停牌）
        if 'volume' in stock_df.columns:
            valid_mask &= (stock_df['volume'].notna() & (stock_df['volume'] > 0))

        # 3. 涨跌幅合理性检查（避免异常数据）
        if 'pct_chg' in stock_df.columns:
            # 涨跌幅在合理范围内（-50%到+50%）
            valid_mask &= (
                stock_df['pct_chg'].notna() &
                (stock_df['pct_chg'] >= -50) &
                (stock_df['pct_chg'] <= 50)
            )

        # 4. 检查是否有前收盘价数据
        if 'preClose' in stock_df.columns:
            valid_mask &= (stock_df['preClose'].notna() & (stock_df['preClose'] > 0))

        return valid_mask

    def _filter_features_by_quality(self, df, filter_stats):
        """
        根据特征质量过滤数据 - 移除无效特征列

        Args:
            df: 数据DataFrame
            filter_stats: 过滤统计信息

        Returns:
            DataFrame: 过滤后的数据
        """
        # 排除非数值列
        exclude_cols = ['stock_code', 'date_str', 'date']
        numeric_cols = [col for col in df.columns
                       if col not in exclude_cols and df[col].dtype in ['float64', 'float32', 'int64', 'int32']]

        # 识别无效特征
        invalid_features = []
        feature_quality = {}

        for col in numeric_cols:
            series = df[col]

            # 计算特征质量指标
            total_count = len(series)
            valid_count = series.notna().sum()
            non_zero_count = (series != 0).sum()
            unique_count = series.nunique()

            valid_ratio = valid_count / total_count if total_count > 0 else 0
            non_zero_ratio = non_zero_count / total_count if total_count > 0 else 0
            diversity_ratio = unique_count / total_count if total_count > 0 else 0

            feature_quality[col] = {
                'valid_ratio': valid_ratio,
                'non_zero_ratio': non_zero_ratio,
                'diversity_ratio': diversity_ratio
            }

            # 判断是否为无效特征
            if (valid_ratio < 0.5 or  # 有效值比例低于50%
                non_zero_ratio < 0.1 or  # 非零值比例低于10%
                diversity_ratio < 0.01):  # 唯一值比例低于1%
                invalid_features.append(col)

        # 移除无效特征
        if invalid_features:
            df = df.drop(columns=invalid_features)
            filter_stats['removed_invalid_features'] += len(invalid_features)
            self.logger.info(f"移除无效特征: {len(invalid_features)} 个")
            for feature in invalid_features[:10]:  # 只显示前10个
                quality = feature_quality[feature]
                self.logger.info(f"  - {feature}: 有效率={quality['valid_ratio']:.2%}, "
                               f"非零率={quality['non_zero_ratio']:.2%}, "
                               f"多样性={quality['diversity_ratio']:.2%}")

        # 保存特征质量信息
        filter_stats['feature_coverage_after_filter'] = {
            k: v for k, v in feature_quality.items() if k not in invalid_features
        }

        return df

    def _filter_sector_data(self, sector_df, filter_stats):
        """
        过滤板块数据 - 移除无效数据点

        Args:
            sector_df: 原始板块数据
            filter_stats: 过滤统计信息

        Returns:
            DataFrame: 过滤后的板块数据
        """
        self.logger.info(f"开始过滤板块数据，原始数据: {len(sector_df)} 条记录")

        # 板块数据过滤逻辑相对简单，主要是特征质量过滤
        filtered_df = self._filter_features_by_quality(sector_df, filter_stats)

        filter_stats['filtered_sector_count'] = len(filtered_df)
        self.logger.info(f"板块数据过滤完成，保留: {len(filtered_df)} 条记录")

        return filtered_df

    def _validate_filtered_data(self, stock_df, sector_df, filter_stats):
        """
        验证过滤后的数据质量

        Args:
            stock_df: 过滤后的个股数据
            sector_df: 过滤后的板块数据
            filter_stats: 过滤统计信息
        """
        # 验证个股数据
        if stock_df is not None and not stock_df.empty:
            # 检查特征多样性
            numeric_cols = stock_df.select_dtypes(include=[np.number]).columns
            feature_diversity = {}

            for col in numeric_cols:
                if col not in ['stock_code', 'date_str', 'date']:
                    unique_ratio = stock_df[col].nunique() / len(stock_df)
                    feature_diversity[col] = unique_ratio

            # 统计高质量特征数量
            high_quality_features = sum(1 for ratio in feature_diversity.values() if ratio > 0.05)
            filter_stats['high_quality_features'] = high_quality_features

            self.logger.info(f"过滤后个股数据包含 {high_quality_features} 个高质量特征")

        # 验证板块数据
        if sector_df is not None and not sector_df.empty:
            self.logger.info(f"过滤后板块数据形状: {sector_df.shape}")

    def _log_filter_statistics(self, filter_stats):
        """
        输出过滤统计信息

        Args:
            filter_stats: 过滤统计信息
        """
        self.logger.info("=== 数据过滤统计 ===")
        self.logger.info(f"原始个股记录数: {filter_stats['original_stock_count']}")
        self.logger.info(f"过滤后个股记录数: {filter_stats['filtered_stock_count']}")
        self.logger.info(f"移除停牌股票: {filter_stats['removed_suspended_stocks']} 条")
        self.logger.info(f"移除无效特征: {filter_stats['removed_invalid_features']} 个")
        self.logger.info(f"移除数据不足股票: {filter_stats['removed_insufficient_data']} 条")

        if filter_stats['original_stock_count'] > 0:
            retention_rate = filter_stats['filtered_stock_count'] / filter_stats['original_stock_count']
            self.logger.info(f"数据保留率: {retention_rate:.2%}")

        if 'high_quality_features' in filter_stats:
            self.logger.info(f"高质量特征数量: {filter_stats['high_quality_features']}")

    def _debug_invalid_features(self, stock_features_df, sector_features_df):
        """
        详细调试无效特征的原因

        Args:
            stock_features_df: 个股特征DataFrame
            sector_features_df: 板块特征DataFrame
        """
        self.logger.info("=== 开始详细调试无效特征 ===")

        # 调试个股K线形态特征
        invalid_patterns = [
            'pattern_morning_star', 'pattern_evening_star',
            'pattern_three_white_soldiers', 'pattern_three_black_crows',
            'pattern_hanging_man', 'pattern_shooting_star'
        ]

        self.logger.info("调试个股K线形态特征...")
        for pattern in invalid_patterns:
            if pattern in stock_features_df.columns:
                total_count = len(stock_features_df)
                non_zero_count = (stock_features_df[pattern] != 0).sum()
                self.logger.info(f"  {pattern}: {non_zero_count}/{total_count} 条记录非零")

                # 如果有非零值，显示一些样本
                if non_zero_count > 0:
                    sample_data = stock_features_df[stock_features_df[pattern] != 0][['stock_code', 'date_str', pattern]].head(3)
                    self.logger.info(f"    样本数据:\n{sample_data}")
            else:
                self.logger.warning(f"  {pattern}: 列不存在")

        # 调试板块特征
        self.logger.info("调试板块特征...")

        # 1. 调试炸板相关特征
        if 'broken_limit_up_count' in sector_features_df.columns:
            broken_count = (sector_features_df['broken_limit_up_count'] > 0).sum()
            total_count = len(sector_features_df)
            self.logger.info(f"  broken_limit_up_count: {broken_count}/{total_count} 条记录大于0")

            if broken_count > 0:
                sample_data = sector_features_df[sector_features_df['broken_limit_up_count'] > 0][
                    ['sector_code', 'date', 'broken_limit_up_count', 'limit_up_count']].head(3)
                self.logger.info(f"    样本数据:\n{sample_data}")

        # 2. 调试最高连板数
        if 'max_consecutive_limit_up' in sector_features_df.columns:
            max_limit_count = (sector_features_df['max_consecutive_limit_up'] > 0).sum()
            total_count = len(sector_features_df)
            self.logger.info(f"  max_consecutive_limit_up: {max_limit_count}/{total_count} 条记录大于0")

            if max_limit_count > 0:
                sample_data = sector_features_df[sector_features_df['max_consecutive_limit_up'] > 0][
                    ['sector_code', 'date', 'max_consecutive_limit_up', 'limit_up_count']].head(3)
                self.logger.info(f"    样本数据:\n{sample_data}")

        # 3. 调试昨日最高连板溢价
        if 'yesterday_max_limit_up_premium' in sector_features_df.columns:
            premium_count = (sector_features_df['yesterday_max_limit_up_premium'] != 0).sum()
            total_count = len(sector_features_df)
            self.logger.info(f"  yesterday_max_limit_up_premium: {premium_count}/{total_count} 条记录非零")

        # # 4. 调试MACD零轴穿越
        # if 'macd_neutral_zero_cross' in sector_features_df.columns:
        #     macd_count = (sector_features_df['macd_neutral_zero_cross'] != 0).sum()
        #     total_count = len(sector_features_df)
        #     self.logger.info(f"  macd_neutral_zero_cross: {macd_count}/{total_count} 条记录非零")

        #     # 检查MACD相关字段是否存在
        #     macd_fields = ['macd', 'macd_signal', 'macd_hist']
        #     for field in macd_fields:
        #         if field in sector_features_df.columns:
        #             non_zero = (sector_features_df[field] != 0).sum()
        #             self.logger.info(f"    {field}: {non_zero}/{total_count} 条记录非零")
        #         else:
        #             self.logger.warning(f"    {field}: 字段不存在")

        # 5. 调试炸板率
        if 'broken_ratio' in sector_features_df.columns:
            ratio_count = (sector_features_df['broken_ratio'] > 0).sum()
            total_count = len(sector_features_df)
            self.logger.info(f"  broken_ratio: {ratio_count}/{total_count} 条记录大于0")

            # 检查计算broken_ratio所需的字段
            if 'broken_limit_up_count' in sector_features_df.columns and 'limit_up_count' in sector_features_df.columns:
                # 计算有涨停的记录数
                has_limit_up = (sector_features_df['limit_up_count'] > 0).sum()
                has_broken = (sector_features_df['broken_limit_up_count'] > 0).sum()
                self.logger.info(f"    有涨停的记录: {has_limit_up}/{total_count}")
                self.logger.info(f"    有炸板的记录: {has_broken}/{total_count}")

        self.logger.info("=== 无效特征调试完成 ===")

    def _load_trading_calendar(self):
        """
        加载交易日历
        
        Returns:
            list: 交易日历列表，如果加载失败则返回None
        """
        try:
            # 尝试从ParquetDataStorage中获取
            sys.path.append(str(get_project_root() / "to"))
            from advanced_quant_data import ParquetDataStorage
            
            storage = ParquetDataStorage()
            
            # 获取较长时间范围的交易日历，但不需要太早的数据
            # 只需要包含训练日期和回溯期即可，'20220101'足够早
            cal_df = storage.get_trading_calendar('20221001', '20250331')
            
            if cal_df is not None and not cal_df.empty:
                if 'date' in cal_df.columns:
                    self.trading_days = cal_df['date'].dt.strftime('%Y%m%d').tolist()
                else:
                    self.trading_days = cal_df.iloc[:, 0].dt.strftime('%Y%m%d').tolist()
                    
                self.logger.info(f"成功加载交易日历，共 {len(self.trading_days)} 个交易日，范围: {self.trading_days[0]} - {self.trading_days[-1]}")
                return self.trading_days
            else:
                self.logger.warning("获取交易日历失败")
                self.trading_days = None
                return None
        except Exception as e:
            print(f"发生错误: {e}")
            self.logger.error(f"加载交易日历出错: {e}")
            self.logger.error(traceback.format_exc())
            self.trading_days = None
            return None
       
    
    def _load_sector_features(self, sector_code, date):
        """
        加载特定板块的特征数据
        
        Args:
            sector_code: 板块代码
            date: 日期，格式为YYYYMMDD
            
        Returns:
            DataFrame: 特征数据
        """
        try:
            # 构建文件路径 - 按板块代码划分目录
            # 优先加载优化后的主特征文件
            main_features_path = self.sector_features_dir / sector_code / f"{sector_code}_features.parquet"
            print(f"main_features_path: {main_features_path}")
            
            if main_features_path.exists():
                # 如果主特征文件存在，直接读取并筛选日期
                self.logger.warning(f"尝试从主特征文件加载板块 {sector_code} 的日期 {date} 数据")
                df = pd.read_parquet(main_features_path)
                
                # 筛选指定日期
                if 'date' in df.columns:
                    date_df = df[df['date_str'] == date].copy()
                    if not date_df.empty:
                        return date_df
                    
                if 'date_str' in df.columns:
                    date_df = df[df['date_str'] == date].copy()
                    if not date_df.empty:
                        return date_df
                
                self.logger.debug(f"主特征文件中未找到日期 {date} 的数据")
            else:
                # 尝试加载单日期特征文件
                date_file = self.sector_features_dir / sector_code / f"{sector_code}_{date}.parquet"
                if date_file.exists():
                    self.logger.debug(f"尝试从单日期文件加载板块 {sector_code} 的日期 {date} 数据")
                    df = pd.read_parquet(date_file)
                    return df
                
                # 尝试加载原始合并特征文件
                orig_file = self.sector_features_dir / sector_code / f"{sector_code}.parquet"
                if orig_file.exists():
                    self.logger.debug(f"尝试从原始合并文件加载板块 {sector_code} 的日期 {date} 数据")
                    df = pd.read_parquet(orig_file)
                    
                    # 筛选指定日期
                    if 'date' in df.columns:
                        date_df = df[df['date_str'] == date].copy()
                        if not date_df.empty:
                            return date_df
                        
                    if 'date_str' in df.columns:
                        date_df = df[df['date_str'] == date].copy()
                        if not date_df.empty:
                            return date_df
                
                self.logger.warning(f"未找到板块 {sector_code} 在日期 {date} 的特征数据")
                return None
        except Exception as e:
            self.logger.error(f"加载板块 {sector_code} 在日期 {date} 的特征数据时出错: {e}")
            return None

    def _load_all_market_status(self):
        """
        加载市场状态数据
        
        Args:
            date: 日期，格式为YYYYMMDD
            
        Returns:
            dict: 市场状态数据
        """
        try:
            # 加载市场状态数据
            status_file = self.market_status_dir / "market_status.json"
            if not status_file.exists():
                self.logger.warning(f"市场状态文件不存在: {status_file}")
                return None
                
            with open(status_file, "r", encoding="utf-8") as f:
                market_status_data = json.load(f)
            return market_status_data   
                
        except Exception as e:
            self.logger.error(f"加载市场状态数据时出错: {e}")
            self.logger.exception(e)
            return None
    
    def train_rotation_model(self, sector_list=None, start_date=None, end_date=None, model_name="sector_rotation_model"):
        """训练板块轮动模型，使用所有板块的特征来预测板块排名
        
        参数:
            sector_list: 板块代码列表
            start_date: 开始日期
            end_date: 结束日期
            model_name: 模型名称
        
        返回:
            训练后的模型包
        """
        logger.info(f"开始训练板块轮动模型...")
        
            
        # 获取所有可用的板块
        if sector_list is None:
            sector_list = self._get_all_sectors()
            
        if isinstance(sector_list, str) and os.path.isfile(sector_list):
            # 从文件加载板块列表
            with open(sector_list, 'r', encoding='utf-8') as f:
                sector_list = [line.strip() for line in f.readlines() if line.strip()]
                
        logger.info(f"使用板块列表: {sector_list}, 共{len(sector_list)}个板块")
        
        # 设置训练日期范围
        if start_date is None:
            start_date = "20200101"  # 默认从2020年开始
        if end_date is None:
            end_date = datetime.now().strftime("%Y%m%d")
        train_end_date = "20250128"
            
        # 设置预测日期范围 - 2025年2月
        predict_start_date = "20250201"
        predict_end_date = "20250331"
        
        # 确定需要加载的整体日期范围
        overall_start_date = start_date
        overall_end_date = predict_end_date  # 确保加载到预测结束日期
            
        logger.info(f"训练日期范围: {start_date} 至 {end_date}")
        logger.info(f"预测日期范围: {predict_start_date} 至 {predict_end_date}")
        
        # 批量加载所有板块特征，包括训练期和预测期
        all_features = self._batch_load_features(sector_list, overall_start_date, overall_end_date)
        
        if all_features is None or len(all_features) == 0:
            logger.error("没有找到有效的特征数据，训练终止")
            return None
                
        logger.info(f"成功加载 {len(all_features)} 个板块的特征数据")
        
        # 拆分数据为训练集和预测集
        if 'date' in all_features.columns:
            # 确保date列是datetime类型
            if not pd.api.types.is_datetime64_any_dtype(all_features['date']):
                all_features['date'] = pd.to_datetime(all_features['date'])
                
            # 筛选训练数据集
            train_mask = ((all_features['date'] >= pd.to_datetime(start_date)) & 
                           (all_features['date'] <= pd.to_datetime(train_end_date)))
            train_features = all_features[train_mask].copy()
            
            # 筛选预测数据集 (2025年2月)
            predict_mask = ((all_features['date'] >= pd.to_datetime(predict_start_date)) & 
                             (all_features['date'] <= pd.to_datetime(predict_end_date)))
            predict_features = all_features[predict_mask].copy()
            
            logger.info(f"筛选出训练数据 {len(train_features)} 条，预测数据 {len(predict_features)} 条")
        else:
            logger.error("特征数据中没有date列，无法按日期筛选")
            return None
            
        # 处理训练特征，准备模型训练
        logger.info("开始准备模型训练数据...")
        
        # 识别涨跌停结构相关特征
        board_structure_features = [col for col in train_features.columns if 
                                   any(col.startswith(prefix) for prefix in 
                                      ['limit_', 'broken_', 'one_word_', 'board_heat', 
                                       'market_cap_', 'consecutive_limit_', 'board_structure_',
                                       'limit_board_', 'limit_type_'])]
        
        logger.info(f"检测到 {len(board_structure_features)} 个涨跌停结构相关特征")
        
        # 准备特征和标签
        # 选择需要的特征列（排除不需要的列）
        exclude_cols = ['date', 'sector_code', 'sector_name', 'change_percent_1d', 
                        'change_percent_3d', 'change_percent_5d', 'change_percent_10d', 
                        'change_percent_20d', 'is_top_performer']
        
        # 获取所有可用的特征列
        all_feature_cols = [col for col in train_features.columns if col not in exclude_cols]
        
        # 确认特征和目标列是否存在
        if 'is_top_performer' not in train_features.columns:
            logger.error("训练数据中没有目标列 'is_top_performer'")
            return None
            
        # 打印特征集的构成
        logger.info(f"特征集包含总共 {len(all_feature_cols)} 个特征，其中 {len(board_structure_features)} 个涨跌停结构特征")
        
        # 特别打印涨跌停结构特征名称
        if board_structure_features:
            logger.info(f"涨跌停结构特征: {', '.join(board_structure_features[:10])}...")
        
        # 记录特征权重，提高涨跌停结构特征的权重
        feature_weights = {}
        for feature in all_feature_cols:
            if feature in board_structure_features:
                # 给涨跌停结构特征更高的权重
                feature_weights[feature] = 1.5
            else:
                feature_weights[feature] = 1.0
                
        # 保存模型训练信息
        model_info = {
            "train_dates": {
                "start_date": start_date,
                "end_date": end_date,
                "train_end_date": train_end_date,
                "predict_start_date": predict_start_date,
                "predict_end_date": predict_end_date
            },
            "feature_counts": {
                "total_features": len(all_feature_cols),
                "board_structure_features": len(board_structure_features)
            },
            "board_structure_features": board_structure_features,
            "feature_weights": feature_weights
        }
        
        # 确保模型目录存在
        model_dir = os.path.join(get_model_path(), model_name)
        ensure_dir(model_dir)
        
        # 保存模型信息
        model_info_file = os.path.join(model_dir, "model_info.json")
        with open(model_info_file, 'w', encoding='utf-8') as f:
            json.dump(model_info, f, ensure_ascii=False, indent=2, cls=NumpyEncoder)
        
        logger.info(f"模型信息已保存到 {model_info_file}")
        
        # 后续模型训练代码...

    def _batch_load_features(self, sector_list, start_date, end_date):
        """批量加载多个板块多个日期的特征数据
        
        Args:
            sector_list: 板块列表
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            DataFrame: 所有特征数据
        """
        # 添加缓存机制，避免重复加载和处理数据
        cache_dir = os.path.join(get_project_root(), "to", "data_cache")
        os.makedirs(cache_dir, exist_ok=True)
        
        # 生成缓存文件名，基于输入参数
        sector_str = "-".join(sorted(sector_list))
        cache_key = f"{sector_str[:100]}-{start_date}-{end_date}"
        import hashlib
        cache_hash = hashlib.md5(cache_key.encode()).hexdigest()
        # cache_hash = "9953d2f3ca428eebeeaf18e6f986f03f"
        cache_file = os.path.join(cache_dir, f"batch_features_{cache_hash}.pkl")
        
        # 检查是否存在缓存
        if os.path.exists(cache_file):
            try:
                self.logger.info(f"发现缓存文件 {cache_file}，尝试加载...")
                with open(cache_file, 'rb') as f:
                    df_all = pickle.load(f)
                self.logger.info(f"成功从缓存加载特征数据，形状: {df_all.shape}")
                return df_all
            except Exception as e:
                self.logger.warning(f"加载缓存文件失败: {e}，将重新生成特征")
        else:
            self.logger.info(f"缓存文件 {cache_file} 不存在，将重新生成特征")
        
        # 确保有交易日历
        if self.trading_days is None:
            self._load_trading_calendar()
            
        # 过滤交易日
        trading_days_in_range = [day for day in self.trading_days if start_date <= day <= end_date]
        self.logger.info(f"在 {start_date} 至 {end_date} 期间共有 {len(trading_days_in_range)} 个交易日")
        
        # 先加载所有板块在整个日期范围的基础数据，以便缓存
        self.logger.info(f"开始加载 {len(sector_list)} 个板块的基础数据...")
        base_features = {}
        
        # 定义板块涨跌停结构特征路径
        board_structure_dir = os.path.join(get_project_root(), "to", "local_data", "sector_features_board")
        self.logger.info(f"板块涨跌停结构特征路径: {board_structure_dir}")
        
        # 存储板块的涨跌停结构特征
        board_structure_features = {}
        
        # 使用多线程并行加载基础数据
        with ThreadPoolExecutor(max_workers=min(10, len(sector_list))) as executor:
            futures = {
                executor.submit(self._load_base_features, sector, start_date, end_date): sector 
                for sector in sector_list
            }
            
            for future in as_completed(futures):
                sector = futures[future]
                try:
                    result = future.result()
                    if result is not None and not result.empty:
                        base_features[sector] = result
                        self.logger.info(f"成功加载板块 {sector} 的基础数据，共 {len(result)} 条记录")
                    else:
                        self.logger.warning(f"板块 {sector} 没有找到基础数据")
                except Exception as e:
                    self.logger.error(f"加载板块 {sector} 基础数据时出错: {e}")
        
        # 加载板块涨跌停结构特征
        self.logger.info("开始加载板块涨跌停结构特征数据...")
        for sector in sector_list:
            try:
                # 构建板块涨跌停结构特征文件路径
                board_file = os.path.join(board_structure_dir, sector, f"{sector}_board_analysis.parquet")
                
                # 检查文件是否存在
                if os.path.exists(board_file):
                    # 加载板块涨跌停结构特征
                    board_df = pd.read_parquet(board_file)
                    
                    # 确保日期列是datetime类型
                    if 'date' in board_df.columns:
                        board_df['date_str'] = pd.to_datetime(board_df['date'])
                    elif 'date_obj' in board_df.columns:
                        board_df['date_str'] = board_df['date_obj']
                    else:
                        self.logger.warning(f"板块 {sector} 的涨跌停结构特征文件没有日期列")
                        continue
                    
                    # 筛选日期范围内的数据
                    mask = (board_df['date_str'] >= pd.to_datetime(start_date)) & (board_df['date_str'] <= pd.to_datetime(end_date))
                    filtered_board_df = board_df[mask].copy()
                    
                    if not filtered_board_df.empty:
                        # 设置日期为索引，便于后续处理
                        filtered_board_df.set_index('date', inplace=True)
                        board_structure_features[sector] = filtered_board_df
                        self.logger.info(f"成功加载板块 {sector} 的涨跌停结构特征，共 {len(filtered_board_df)} 条记录")
                    else:
                        self.logger.warning(f"板块 {sector} 在指定日期范围内没有涨跌停结构特征数据")
                else:
                    self.logger.warning(f"板块 {sector} 的涨跌停结构特征文件不存在: {board_file}")
            except Exception as e:
                self.logger.error(f"加载板块 {sector} 的涨跌停结构特征时出错: {e}")
        
        # 如果没有任何数据，提前返回
        if not base_features:
            self.logger.warning("没有找到任何板块数据")
            return pd.DataFrame()
            
        self.logger.info(f"成功加载 {len(base_features)}/{len(sector_list)} 个板块的基础数据")
        self.logger.info(f"成功加载 {len(board_structure_features)}/{len(sector_list)} 个板块的涨跌停结构特征")
        
        # 提前加载市场环境数据
        self.logger.info("开始加载市场环境数据...")
        market_status_cache = self._load_all_market_status()
        self.logger.info(f"成功加载 {len(market_status_cache)} 个日期的市场环境数据")
        
        # 准备所有任务 - 每个板块每个日期一个任务
        tasks = []
        for sector in base_features.keys():
            for date in trading_days_in_range:
                date_obj = pd.to_datetime(date)
                if date_obj in base_features[sector].index:
                    tasks.append((sector, date))
                    
        self.logger.info(f"共找到 {len(tasks)} 个特征构建任务")
        
        # 使用多线程处理每个板块-日期组合
        all_features = []
        futures = {}
       
        with ThreadPoolExecutor(max_workers=min(20, len(tasks))) as executor:
            for sector, date in tasks:
                date_obj = pd.to_datetime(date)
                
                # 获取市场环境数据
                market_data = market_status_cache.get(date, None)
                
                # 获取板块涨跌停结构特征
                board_data = None
                if sector in board_structure_features and date_obj in board_structure_features[sector].index:
                    board_data = board_structure_features[sector].loc[date_obj].to_dict()
                
                # 提交任务
                future = executor.submit(
                    self._process_features_with_layers,
                    sector_code=sector,
                    date=date_obj,
                    base_data=base_features[sector],
                    market_status=market_data,
                    board_structure=board_data
                )
                
                futures[future] = (sector, date)
            
            # 收集结果
            for i, future in enumerate(as_completed(futures)):
                sector_code, date = futures[future]
                try:
                    result = future.result()
                    if result is not None:
                        # 不再需要再次计算未来收益率，因为已经在_process_features_with_layers中完成
                        all_features.append(result)
                    
                    # 进度日志
                    if (i+1) % 100 == 0 or (i+1) == len(tasks):
                        self.logger.info(f"已处理 {i+1}/{len(tasks)} 个特征构建任务")
                except Exception as e:
                    self.logger.error(f"处理板块 {sector_code} 日期 {date} 的特征时出错: {e}")
        
        self.logger.info(f"批量加载完成，成功构建 {len(all_features)} 条特征记录")
        
        if not all_features:
            self.logger.warning("没有生成任何有效特征")
            return pd.DataFrame()
        
        # 合并所有特征
        try:
            # 合并所有DataFrame
            df_all = pd.concat(all_features, ignore_index=True)
            
            # 添加日期字段(如果没有)
            if 'date' in df_all.columns and not pd.api.types.is_datetime64_any_dtype(df_all['date']):
                df_all['date'] = pd.to_datetime(df_all['date'])
                
            # 按日期排序
            if 'date' in df_all.columns:
                df_all = df_all.sort_values('date')
                
            self.logger.info(f"特征构建完成，共 {len(df_all)} 条记录，{df_all.shape[1]} 个特征")
            
            # 保存到缓存
            try:
                self.logger.info(f"保存特征数据到缓存 {cache_file}...")
                with open(cache_file, 'wb') as f:
                    pickle.dump(df_all, f)
                self.logger.info(f"特征数据缓存保存成功")
                
            except Exception as e:
                self.logger.warning(f"保存特征数据到缓存失败: {e}")
            
            return df_all
            
        except Exception as e:
            print(f"发生错误: {e}")
            self.logger.error(f"合并特征数据时出错: {e}")
            return pd.DataFrame()
        
           

    def _load_base_features(self, sector_code, start_date, end_date):
        """加载单个板块的基础特征数据（价格、成交量等）
        
        Args:
            sector_code: 板块代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            板块基础数据
        """
        try:
            # 获取日期范围内的所有交易日
            if self.trading_days is None:
                self._load_trading_calendar()
                
            trading_days = [day for day in self.trading_days if start_date <= day <= end_date]
            if not trading_days:
                self.logger.warning(f"在{start_date}至{end_date}之间没有有效交易日")
                return None
                
            # 构建板块主特征文件路径
            sector_dir = self.sector_features_dir / f"{sector_code}"
            main_features_path = sector_dir / f"{sector_code}_features.parquet"
            
            # 如果主特征文件不存在，尝试使用合并特征文件
            if not main_features_path.exists():
                merged_file_path = sector_dir / f"{sector_code}.parquet"
                if merged_file_path.exists():
                    main_features_path = merged_file_path
                else:
                    self.logger.warning(f"板块 {sector_code} 没有主特征文件或合并特征文件")
                    return None
            
            # 读取特征文件
            self.logger.info(f"从 {main_features_path} 读取 {sector_code} 板块从 {start_date} 到 {end_date} 的特征数据")
            
            try:
                df = pd.read_parquet(main_features_path)
                
                # 确保日期列存在
                if 'date' not in df.columns:
                    self.logger.warning(f"特征文件 {main_features_path} 没有date列")
                    return None
                
                # 将日期列转换为datetime对象便于比较
                df['date_str'] = pd.to_datetime(df['date_str'])
                
                # 筛选日期范围内的数据
                mask = (df['date_str'] >= pd.to_datetime(start_date)) & (df['date_str'] <= pd.to_datetime(end_date))
                filtered_df = df[mask].copy()
                
                if filtered_df.empty:
                    self.logger.warning(f"板块 {sector_code} 在 {start_date} 至 {end_date} 期间没有数据")
                    return None
                
                # 设置date为索引以便后续处理
                filtered_df.set_index('date', inplace=True)
                
                self.logger.info(f"成功加载 {sector_code} 板块特征，共 {len(filtered_df)} 条记录")
                return filtered_df
                
            except Exception as e:
                print(f"发生错误: {e}")
                self.logger.error(f"读取 {main_features_path} 出错: {e}")
                return None
                
                
        except Exception as e:
            print(f"发生错误: {e}")
            self.logger.error(f"加载板块 {sector_code} 基础数据时发生错误: {e}")
            return None


    def _process_features_with_layers(self, sector_code, date, base_data, market_status, board_structure):
        """使用三层特征体系处理单个板块单个日期的特征
        
        Args:
            sector_code: 板块代码
            date: 日期
            base_data: 基础层数据
            market_status: 市场环境层数据
            board_structure: 板块涨跌停结构特征
        Returns:
            处理后的特征数据
        """
        try:
            # 确保基础数据中包含当前日期
            if base_data is None or date not in base_data.index:
                return None
                
            # 获取当日数据
            current_day = base_data.loc[date]
            
            # 构建特征字典
            features = {}
            
            # 1. 基础层特征 - 仅保留重要的基础指标
            # ------------------------------------------------------------
            features["sector_code"] = sector_code
            features["date"] = date
            
            # 添加基础价格类指标 - 正确的字段映射
            for field in ["close", "change_percent", "volume", "amount", "turnover_rate", "amplitude"]:
                if field in current_day:
                    # 直接使用原字段名，不进行错误的映射
                    # change_percent 是板块涨跌幅，保持原名
                    # pct_chg 是个股涨跌幅，在个股数据中使用
                    features[field] = current_day[field]
            
            # 添加股票统计信息
            for field in ["stock_count", "up_count", "down_count", "flat_count"]:
                if field in current_day:
                    features[field] = current_day[field]
                    
            # 提取上涨/下跌比例
            if all(f in current_day for f in ["up_count", "stock_count"]) and current_day["stock_count"] > 0:
                features["up_ratio"] = current_day["up_count"] / current_day["stock_count"]
                
            if all(f in current_day for f in ["down_count", "stock_count"]) and current_day["stock_count"] > 0:
                features["down_ratio"] = current_day["down_count"] / current_day["stock_count"]
            
            # ===== 新增：资金流动相关特征 =====
            # ------------------------------------------------------------
            # 资金流动动能指标（现有特征基础上构建）
            if "amount" in current_day and base_data is not None:
                try:
                    # 计算5日平均成交额
                    date_idx = base_data.index.get_loc(date)
                    if date_idx >= 4:  # 确保有足够的历史数据
                        historical_dates = base_data.index[date_idx-4:date_idx+1]
                        historical_amounts = base_data.loc[historical_dates, "amount"]
                        avg_amount_5d = historical_amounts.mean()
                        if avg_amount_5d > 0:
                            features["fund_flow_momentum"] = current_day["amount"] / avg_amount_5d
                except Exception as e:
                    self.logger.debug(f"计算fund_flow_momentum时出错: {e}")
            
            # 相对成交量密集度（衡量资金聚集程度）
            if "volume" in current_day and "volatility_20d" in current_day and current_day["volatility_20d"] > 0:
                features["volume_intensity"] = current_day["volume"] / current_day["volatility_20d"]
            
            # 上涨成交量占比（衡量资金质量）
            if all(k in current_day for k in ["up_count", "volume", "stock_count"]) and "volume" in base_data.columns:
                try:
                    date_idx = base_data.index.get_loc(date)
                    if date_idx >= 4:  # 确保有足够的历史数据
                        historical_dates = base_data.index[date_idx-4:date_idx+1]
                        avg_volume_5d = base_data.loc[historical_dates, "volume"].mean()
                        if avg_volume_5d > 0 and current_day["stock_count"] > 0:
                            features["up_volume_ratio"] = (current_day["up_count"] * current_day["volume"]) / (current_day["stock_count"] * avg_volume_5d)
                except Exception as e:
                    self.logger.debug(f"计算up_volume_ratio时出错: {e}")
            
            # 2. 技术指标层 - 直接使用预计算的技术指标
            # ------------------------------------------------------------
            # 添加均线数据
            # for ma in ["ma5", "ma10", "ma20", "ma30", "ma60", "ma120"]:
            #     if ma in current_day:
            #         features[ma] = current_day[ma]
                    
            # 添加均线比率指标
            for ratio in ["ma5_10_ratio", "ma5_20_ratio", "ma10_20_ratio", 
                         "ma20_60_ratio", "ma60_120_ratio", "price_ma5_ratio", 
                         "price_ma20_ratio", "price_ma60_ratio"]:
                if ratio in current_day:
                    features[ratio] = current_day[ratio]
                    
            # 添加价格位置指标
            for pos in ["price_above_ma5", "price_above_ma20", "price_above_ma60"]:
                if pos in current_day:
                    features[pos] = current_day[pos]
                    
            # 添加趋势指标
            for trend in ["trend_direction_score", "ma_bull_trend", "ma_bear_trend", 
                         "ma_neutral_trend", "ma5_slope", "ma20_slope"]:
                if trend in current_day:
                    features[trend] = current_day[trend]
                    
            # 添加动量指标 - 注意：change_pct 是个股字段，不应在板块数据中使用
            for mom in ["momentum_5d", "momentum_10d", "momentum_20d"]:
                if mom in current_day:
                    features[mom] = current_day[mom]

            # 板块相对变化率（如果存在）
            if "change_pct" in current_day:
                features["sector_change_pct"] = current_day["change_pct"]  # 重命名避免与个股字段冲突
                    
            # # 添加RSI指标
            # for rsi in ["rsi6", "rsi14", "rsi_overbought", "rsi_oversold", "rsi_neutral"]:
            #     if rsi in current_day:
            #         features[rsi] = current_day[rsi]
                    
            # # 添加MACD指标
            # for macd in ["macd_line", "macd_signal", "macd_histogram", "macd_trend"]:
            #     if macd in current_day:
            #         features[macd] = current_day[macd]
                    
            # 添加波动率指标
            for vol in ["volatility", "volatility_5d", "volatility_10d", 
                       "volatility_20d", "volatility_60d", "volatility_ratio"]:
                if vol in current_day:
                    features[vol] = current_day[vol]
                    
            # 添加成交量指标
            for vol in ["volume_ratio_5", "volume_ratio_10", "volume_ratio_20", 
                       "volume_ratio_60", "volume_change_ratio", "volume_trend"]:
                if vol in current_day:
                    features[vol] = current_day[vol]
            
            # ===== 新增：生命周期指标 =====
        
            
            # 热度持续性指标
            if "consecutive_up_days" in current_day and "indicator_consensus" in features:
                features["heat_persistence"] = current_day["consecutive_up_days"] * (features["indicator_consensus"] + 1) / 2
            
            # 板块活跃度
            if "turnover_rate" in current_day and "limit_up_ratio" in features:
                features["sector_vitality"] = (current_day["turnover_rate"] * features["limit_up_ratio"]) ** 0.5
            
            
            # ===== 增强技术指标特征 =====
            # RSI和MACD组合增强特征
            if all(key in current_day for key in ["rsi14", "macd_line", "macd_signal"]):
                # RSI和MACD金叉/死叉协同信号
                features["rsi_macd_bull_signal"] = int(current_day["rsi14"] > 50 and current_day["macd_line"] > current_day["macd_signal"] and current_day["macd_line"] > 0)
                features["rsi_macd_bear_signal"] = int(current_day["rsi14"] < 50 and current_day["macd_line"] < current_day["macd_signal"] and current_day["macd_line"] < 0)
                
                # RSI超买/超卖区间的MACD信号(更可靠的反转信号)
                if "rsi_overbought" in current_day and current_day["rsi_overbought"] == 1:
                    features["overbought_macd_bearish"] = int(current_day["macd_line"] < current_day["macd_signal"])
                if "rsi_oversold" in current_day and current_day["rsi_oversold"] == 1:
                    features["oversold_macd_bullish"] = int(current_day["macd_line"] > current_day["macd_signal"])
            
            # 动量与波动率组合特征
            if all(key in current_day for key in ["momentum_20d", "volatility_20d"]) and current_day["volatility_20d"] > 0:
                # 风险调整动量比率(类似夏普比率思想)
                features["momentum_volatility_ratio"] = current_day["momentum_20d"] / current_day["volatility_20d"]
                
            # 成交量与价格/动量关系
            if all(key in current_day for key in ["volume_ratio_5", "momentum_5d"]):
                # 量价协同指标
                features["volume_momentum_confirm"] = int(
                    (current_day["volume_ratio_5"] > 1.2 and current_day["momentum_5d"] > 0) or 
                    (current_day["volume_ratio_5"] < 0.8 and current_day["momentum_5d"] < 0)
                )
                # 量价背离指标(可能的反转信号)
                features["volume_momentum_divergence"] = int(
                    (current_day["volume_ratio_5"] > 1.2 and current_day["momentum_5d"] < 0) or 
                    (current_day["volume_ratio_5"] < 0.8 and current_day["momentum_5d"] > 0)
                )
            
            # 技术指标一致性强度
            trend_signals = []
            if "macd_line" in current_day and "macd_signal" in current_day:
                trend_signals.append(1 if current_day["macd_line"] > current_day["macd_signal"] else -1)
            if "rsi14" in current_day:
                trend_signals.append(1 if current_day["rsi14"] > 50 else -1)
            if "momentum_10d" in current_day:
                trend_signals.append(1 if current_day["momentum_10d"] > 0 else -1)
            
            if trend_signals:
                # 技术指标一致程度(-1到1之间，1表示所有指标一致看多)
                features["indicator_consensus"] = sum(trend_signals) / len(trend_signals)
                # 强烈看多/看空信号(所有指标一致)
                features["strong_bull_consensus"] = int(sum(trend_signals) == len(trend_signals))
                features["strong_bear_consensus"] = int(sum(trend_signals) == -len(trend_signals))
                
            # 战无不克指标(MACD与RSI完美结合)
            if all(key in features for key in ["rsi6", "rsi14", "macd_line", "macd_signal"]):
                # RSI短线突破长线 + MACD金叉组合信号
                features["winning_bull_signal"] = int(current_day["rsi6"] > current_day["rsi14"] 
                                                     and current_day["macd_line"] > current_day["macd_signal"]
                                                     and current_day["rsi6"] > 50)
                # RSI短线跌破长线 + MACD死叉组合信号
                features["winning_bear_signal"] = int(current_day["rsi6"] < current_day["rsi14"] 
                                                     and current_day["macd_line"] < current_day["macd_signal"]
                                                     and current_day["rsi6"] < 50)
            
                    
            # 添加趋势强度和连续天数指标
            for field in ["trend_strength", "consecutive_up_days", "consecutive_down_days", 
                         "swing_pattern"]:
                if field in current_day:
                    features[field] = current_day[field]
                    
            # 添加支撑阻力位指标
            for field in ["support_distance_pct", 
                         "resistance_distance_pct"]:
                if field in current_day:
                    features[field] = current_day[field]
                    
            # 添加技术评分和市场情绪指标
            for field in ["tech_score", "market_sentiment", "signal_strength"]:
                if field in current_day:
                    features[field] = current_day[field]
                    
            # ===== 新增：添加涨跌停板结构特征 =====
            # ------------------------------------------------------------
            # 调用新方法添加板块涨跌停结构特征，传递board_structure参数
            features = self._add_board_structure_features(features, current_day, sector_code, board_structure)
                    
            # 添加涨跌停相关指标
            for field in ["limit_up_ratio", "limit_down_ratio", "near_limit_up_ratio", 
                         "limit_open_ratio"]:
                if field in current_day:
                    features[field] = current_day[field]
                    
            # 添加排名相关指标
            for field in ["ten_day_rise_count", "ten_day_rise_ratio", "daily_rise_count", 
                         "daily_rise_ratio", "daily_fall_count", "daily_fall_ratio", 
                         "daily_amount_count", "daily_amount_ratio", "ten_day_rise_weighted"]:
                if field in current_day:
                    features[field] = current_day[field]
            
            # 3. 市场环境层 - 直接使用提供的市场状态数据
            # ------------------------------------------------------------
            # 显式设置market_group特征，无论是否有市场状态数据
            if market_status:
                # 添加市场类型和周期指标
                features["market_type"] = market_status.get("market_type", 0)
                features["market_cycle"] = market_status.get("market_cycle", 0)
                features["market_trend_strength"] = market_status.get("trend_strength", 0)
                
                # 显式设置market_group特征，将market_type映射到bull/bear/neutral/extreme
                market_type = market_status.get("market_type", 0)
                trend_strength = market_status.get("trend_strength", 0)
                
                if isinstance(market_type, (int, float)):
                    # 考虑市场类型和趋势强度的综合映射
                    # 1. 明确的上涨趋势 (强多头市场和温和多头市场)
                    if market_type in [11, 12, 13, 21, 22, 23]:
                        features["market_group"] = 1  # 牛市
                    # 2. 明确的下跌趋势 (强空头市场和温和空头市场)
                    elif market_type in [41, 42, 43, 51, 52, 53]:
                        features["market_group"] = 2  # 熊市
                    # 3. 极端市场状态 (市场恐慌或狂热)
                    elif market_type in [54, 55, 56, 57, 58, 59] or trend_strength > 85:
                        features["market_group"] = 4  # 极端市场
                    # 4. 蓄势结构或温和趋势转换
                    elif market_type in [31, 32, 33, 34, 35, 36]:
                        features["market_group"] = 3  # 震荡市场
                    # 5. 其他不明确状态
                    else:
                        features["market_group"] = 3  # 默认为震荡市场
                else:
                    # 默认为neutral市场
                    features["market_group"] = 3
            else:
                # 如果没有市场状态数据，使用默认值(震荡市场)
                features["market_type"] = 33  # 33表示震荡市场类型
                features["market_cycle"] = 0
                features["market_trend_strength"] = 0
                features["market_group"] = 3  # 3表示震荡市场
            
            # 添加创业板指数数据(399006.SZ)
            if "individual_status" in market_status and "399006.SZ" in market_status["individual_status"]:
                cyb_data = market_status["individual_status"]["399006.SZ"]
                
                # 获取原始的相对涨跌幅特征
                if "change_percent" in cyb_data:
                    features["relative_cyb_change_percent"] = features["change_percent"] - cyb_data["change_percent"]
                
                # ===== 市场与板块的相对特征增强 =====
                # 提取市场技术指标用于相对特征计算
                if "technical_data" in cyb_data:
                    tech_data = cyb_data["technical_data"]
                    
                    # 1. 动量与市场相对特征
                    if "momentum_10d" in current_day and "rsi6" in tech_data:
                        features["momentum_vs_market_rsi"] = current_day["momentum_10d"] / (tech_data["rsi6"] + 1e-8)
                    
                    # 2. 波动率与市场相对特征
                    if "volatility_20d" in current_day and "bb_width" in tech_data:
                        features["volatility_vs_market"] = current_day["volatility_20d"] / (tech_data["bb_width"] + 1e-8)
                    
                    # 3. RSI与市场相对特征
                    if "rsi14" in current_day and "rsi12" in tech_data:
                        features["rsi_vs_market"] = current_day["rsi14"] / (tech_data["rsi12"] + 1e-8)
                        # RSI差值特征(相对强弱)
                        features["rsi_diff_market"] = current_day["rsi14"] - tech_data["rsi12"]
                    
                    # 4. MACD与市场相对特征
                    if "macd_line" in current_day and "macd" in tech_data:
                        features["macd_vs_market"] = current_day["macd_line"] / (tech_data["macd"] + 1e-8)
                        # MACD差值特征
                        features["macd_diff_market"] = current_day["macd_line"] - tech_data["macd"]
                    
                    # 5. 均线位置相对特征
                    if "ma5_10_ratio" in current_day and "ma5" in tech_data and "ma10" in tech_data:
                        market_ma_ratio = tech_data["ma5"] / (tech_data["ma10"] + 1e-8)
                        features["ma_ratio_vs_market"] = current_day["ma5_10_ratio"] / (market_ma_ratio + 1e-8)
                
                # 6. 盈亏比相对特征
                if "profit_loss_ratio_14" in cyb_data and "support_distance_pct" in current_day and "resistance_distance_pct" in current_day:
                    if current_day["resistance_distance_pct"] != 0:
                        sector_pl_ratio = current_day["support_distance_pct"] / (current_day["resistance_distance_pct"] + 1e-8)
                        features["profit_loss_ratio_vs_market"] = sector_pl_ratio / (cyb_data["profit_loss_ratio_14"] + 1e-8)
                
                # ===== 市场状态条件特征 =====
                # 根据不同市场类型创建条件特征
                market_group = features["market_group"]
                
                # 1. 动量条件特征
                for mom_period in ["5d", "10d", "20d"]:
                    mom_key = f"momentum_{mom_period}"
                    if mom_key in current_day:
                        # 牛市动量更重要
                        if market_group == 1:  # 牛市
                            features[f"{mom_key}_in_bull"] = current_day[mom_key] * 1.2
                        # 熊市负动量更重要
                        elif market_group == 2:  # 熊市
                            if current_day[mom_key] < 0:
                                features[f"{mom_key}_in_bear"] = current_day[mom_key] * 1.3
                            else:
                                features[f"{mom_key}_in_bear"] = current_day[mom_key] * 0.8
                        # 极端市场动量更敏感
                        elif market_group == 4:  # 极端市场
                            if current_day[mom_key] > 0:
                                features[f"{mom_key}_in_extreme"] = current_day[mom_key] * 1.5  # 极端市场放大动量影响
                            else:
                                features[f"{mom_key}_in_extreme"] = current_day[mom_key] * 1.8  # 极端市场放大负动量影响
                        # 震荡市动量适中
                        else:  # 震荡市
                            features[f"{mom_key}_in_neutral"] = current_day[mom_key]
                
                # 2. RSI条件特征
                if "rsi14" in current_day:
                    rsi_val = current_day["rsi14"]
                    # 牛市RSI高位更可靠
                    if market_group == 1:  # 牛市
                        features["rsi_bull_signal"] = 1 if rsi_val > 60 else 0
                        features["rsi_bull_overbought"] = 1 if rsi_val > 80 else 0
                    # 熊市RSI低位更可靠
                    elif market_group == 2:  # 熊市
                        features["rsi_bear_signal"] = 1 if rsi_val < 40 else 0
                        features["rsi_bear_oversold"] = 1 if rsi_val < 20 else 0
                    # 极端市场RSI极值更有意义
                    elif market_group == 4:  # 极端市场
                        features["rsi_extreme_high"] = 1 if rsi_val > 85 else 0
                        features["rsi_extreme_low"] = 1 if rsi_val < 15 else 0
                        # 极端市场中的反转信号
                        features["rsi_extreme_reversal"] = 1 if (20 <= rsi_val <= 30) or (70 <= rsi_val <= 80) else 0
                    # 震荡市中值区间更重要
                    else:  # 震荡市
                        features["rsi_neutral_middle"] = 1 if 40 <= rsi_val <= 60 else 0
                
                # 3. MACD条件特征
                if all(k in current_day for k in ["macd_line", "macd_signal"]):
                    macd_line = current_day["macd_line"]
                    macd_signal = current_day["macd_signal"]
                    
                    # 牛市关注金叉
                    if market_group == 1:  # 牛市
                        features["macd_bull_cross"] = 1 if macd_line > macd_signal and macd_line > 0 else 0
                    # 熊市关注死叉
                    elif market_group == 2:  # 熊市
                        features["macd_bear_cross"] = 1 if macd_line < macd_signal and macd_line < 0 else 0
                    # 极端市场关注偏离程度
                    elif market_group == 4:  # 极端市场
                        # 计算MACD偏离程度
                        deviation = abs(macd_line - macd_signal) / (abs(macd_signal) + 1e-8)
                        features["macd_extreme_deviation"] = deviation  # 偏离程度
                        # 极端市场中的背离信号(可能的反转)
                        if "momentum_5d" in current_day:
                            # MACD与短期动量方向相反是种背离
                            features["macd_extreme_divergence"] = 1 if (macd_line > 0 and current_day["momentum_5d"] < 0) or (macd_line < 0 and current_day["momentum_5d"] > 0) else 0
                    # 震荡市关注零轴穿越
                    # else:  # 震荡市
                    #     zero_cross = 1 if (macd_line > 0 and macd_signal < 0) or (macd_line < 0 and macd_signal > 0) else 0
                    #     features["macd_neutral_zero_cross"] = zero_cross
                    #     # 添加调试日志
                    #     if zero_cross == 1:
                    #         self.logger.debug(f"检测到MACD零轴穿越: 板块={sector_code}, 日期={current_day.get('date_str', 'unknown')}, MACD={macd_line:.4f}, Signal={macd_signal:.4f}")
                
                # 4. 成交量条件特征
                if "volume_ratio_5" in current_day:
                    vol_ratio = current_day["volume_ratio_5"]
                    # 牛市放量上涨更好
                    if market_group == 1:  # 牛市
                        features["volume_bull_signal"] = 1 if vol_ratio > 1.2 else 0
                    # 熊市缩量更好
                    elif market_group == 2:  # 熊市
                        features["volume_bear_signal"] = 1 if vol_ratio < 0.8 else 0
                    # 极端市场关注成交量异常
                    elif market_group == 4:  # 极端市场 
                        # 极端放量或极端缩量都是重要信号
                        features["volume_extreme_high"] = 1 if vol_ratio > 1.8 else 0
                        features["volume_extreme_low"] = 1 if vol_ratio < 0.5 else 0
                    # 震荡市稳定成交量更好
                    else:  # 震荡市
                        features["volume_neutral_signal"] = 1 if 0.8 <= vol_ratio <= 1.2 else 0
                
                # 5. 复合交互条件特征
                # 牛市条件下的量价配合
                if market_group == 1 and "momentum_5d" in current_day and "volume_ratio_5" in current_day:
                    features["bull_momentum_volume"] = current_day["momentum_5d"] * current_day["volume_ratio_5"] if current_day["momentum_5d"] > 0 else 0
                
                # 熊市条件下的反弹机会
                elif market_group == 2 and "rsi14" in current_day and "consecutive_down_days" in current_day:
                    # 熊市中超跌反弹信号
                    features["bear_oversold_bounce"] = 1 if current_day["rsi14"] < 30 and current_day["consecutive_down_days"] >= 3 else 0
                
                # 极端市场条件下的反转信号
                elif market_group == 4:
                    # 1. 极端市场下的RSI与波动率组合特征
                    if all(k in current_day for k in ["rsi14", "volatility_20d"]):
                        # 高波动率下的RSI反转信号更可靠
                        if current_day["volatility_20d"] > 0.03:  # 高波动率环境
                            if current_day["rsi14"] < 20:
                                features["extreme_oversold_high_vol"] = 1
                            elif current_day["rsi14"] > 80:
                                features["extreme_overbought_high_vol"] = 1
                    
                    # 2. 极端市场下的量价背离信号
                    if all(k in current_day for k in ["volume_ratio_5", "momentum_5d"]):
                        # 价升量缩或价跌量增都是潜在的反转信号
                        is_divergent = ((current_day["momentum_5d"] > 0 and current_day["volume_ratio_5"] < 0.8) or 
                                       (current_day["momentum_5d"] < 0 and current_day["volume_ratio_5"] > 1.2))
                        features["extreme_volume_price_divergence"] = 1 if is_divergent else 0
                
                # 震荡市条件下的波动信号
                elif market_group == 3 and "volatility_5d" in current_day:
                    features["neutral_volatility_signal"] = current_day["volatility_5d"] / 0.02  # 标准化
                
                # ===== 新增：板块间关系特征 =====
                # ------------------------------------------------------------
                # 板块相关性特征（与创业板指数的相关性）
                if "technical_data" in cyb_data and "close" in base_data.columns:
                    try:
                        # 计算板块与创业板的滚动相关性
                        date_idx = base_data.index.get_loc(date)
                        if date_idx >= 19:  # 确保有足够的历史数据(至少20天)
                            historical_dates = base_data.index[date_idx-19:date_idx+1]
                            
                            if all(d in base_data.index for d in historical_dates):
                                # 获取板块历史价格
                                sector_prices = base_data.loc[historical_dates, "close"]
                                
                                # 获取创业板历史价格 (假设创业板数据按日期索引)
                                cyb_prices = []
                                for d in historical_dates:
                                    d_str = d.strftime('%Y%m%d')
                                    if "individual_status" in market_status and "399006.SZ" in market_status["individual_status"]:
                                        cyb_data_for_date = market_status["individual_status"]["399006.SZ"]
                                        if "close" in cyb_data_for_date:
                                            cyb_prices.append(cyb_data_for_date["close"])
                                
                                # 如果有足够的数据点，计算相关性
                                if len(cyb_prices) >= 10:
                                    # 计算收益率
                                    sector_returns = sector_prices.pct_change().dropna()
                                    cyb_returns = pd.Series(cyb_prices).pct_change().dropna()
                                    
                                    # 确保长度一致
                                    min_len = min(len(sector_returns), len(cyb_returns))
                                    if min_len >= 10:
                                        corr = sector_returns[-min_len:].corr(cyb_returns[-min_len:])
                                        features["correlation_with_cyb"] = corr
                    except Exception as e:
                        self.logger.debug(f"计算板块与创业板相关性时出错: {e}")
                
                # 资金流转特征（板块资金占比变化）
                if "amount" in current_day and "total_market_amount" in market_status:
                    try:
                        market_total_amount = market_status["total_market_amount"]
                        if market_total_amount > 0:
                            # 当前板块成交额占市场总成交额的比例
                            current_share = current_day["amount"] / market_total_amount
                            
                            # 尝试获取5天前的占比
                            date_idx = base_data.index.get_loc(date)
                            if date_idx >= 5 and "total_market_amount_history" in market_status:
                                past_date = base_data.index[date_idx - 5]
                                past_amount = base_data.loc[past_date, "amount"]
                                past_market_amount = market_status["total_market_amount_history"].get(past_date.strftime('%Y%m%d'), 0)
                                
                                if past_market_amount > 0:
                                    past_share = past_amount / past_market_amount
                                    features["fund_flow_share_change"] = current_share - past_share
                    except Exception as e:
                        self.logger.debug(f"计算资金流转特征时出错: {e}")
        

            # 删除已知的无效特征
            features_to_remove = ['ma120', 'macd_neutral_zero_cross']  # 直接列出无效特征
            for feature_to_remove in features_to_remove:
                if feature_to_remove in features:
                    del features[feature_to_remove]

            # 创建数据帧并返回
            return pd.DataFrame([features])
        except Exception as e:
            print(f"发生错误: {e}")
            self.logger.error(f"处理板块 {sector_code} 日期 {date} 特征时错误: {str(e)}")
            traceback.print_exc()
            return None

    def _get_all_sectors(self):
        """获取所有可用的板块列表
        
        返回:
            板块代码列表
        """
        try:
            # # 首先尝试获取本地存储的板块列表
            # sector_dirs = [d for d in os.listdir(self.sector_features_dir) 
            #              if os.path.isdir(os.path.join(self.sector_features_dir, d))]
            
            # if sector_dirs:
            #     self.logger.info(f"从本地目录获取到 {len(sector_dirs)} 个板块")
            #     return sector_dirs
                
            # 尝试从stock_sectors_mapping模块获取
            try:
                # 将当前工作目录添加到sys.path
                sys.path.append(os.getcwd())
                # 尝试导入
                try:
                    from to.data_loaders import get_sector_list
                    sector_list = get_sector_list()
                    self.logger.info(f"从get_tng_sectors获取 {len(sector_list)} 个板块")
                    return sector_list
                except ImportError:
                    # 如果导入失败，尝试从to目录导入
                    try:
                        sys.path.append(str(get_project_root()))
                        from to.stock_sectors_mapping import get_tng_sectors
                        sector_list = get_tng_sectors()
                        self.logger.info(f"从to.stock_sectors_mapping获取 {len(sector_list)} 个板块")
                        return sector_list
                    except ImportError:
                        self.logger.warning("无法导入get_tng_sectors函数")
            except Exception as e:
                self.logger.error(f"尝试获取板块列表时出错: {e}")
                
            # 如果都失败了，返回空列表
            return []
            
        except Exception as e:
            self.logger.error(f"获取所有板块列表时出错: {e}")
            return []

    def _detect_single_stock_change_points(self, stock_code, stock_df, indicators=None):
        """检测单个股票的变化点
        
        Args:
            stock_code: 股票代码
            stock_df: 股票数据DataFrame
            indicators: 指标列表(可选)，默认仅使用future_return_2d
        
        Returns:
            dict: 变化点检测结果
        """
        from ruptures import Pelt, Binseg, Window
        import numpy as np
        from sklearn.preprocessing import StandardScaler
        
        self.logger.info(f"开始检测股票 {stock_code} 的变化点...")
        
        try:
            # 确保按日期排序
            stock_df = stock_df.sort_values('date_str')
            
            # 检查是否有足够的数据
            if len(stock_df) < 20:  # 需要至少20个数据点
                self.logger.warning(f"股票 {stock_code} 数据点不足 ({len(stock_df)}个)，跳过变化点检测")
                return None
            
            # 检查future_return_2d是否存在（必须指标）
            if 'future_return_2d' not in stock_df.columns:
                self.logger.warning(f"股票 {stock_code} 缺少必要指标future_return_2d，跳过变化点检测")
                return None
            
            # 保存日期序列用于结果记录
            dates = stock_df['date_str'].values
            
            # 获取future_return_2d序列
            future_returns = stock_df['future_return_2d'].values
            
            # 确保future_returns是有效的数值数组
            if not isinstance(future_returns, np.ndarray):
                self.logger.warning(f"股票 {stock_code} 的future_return_2d不是numpy数组，尝试转换")
                try:
                    future_returns = np.array(future_returns, dtype=float)
                except Exception as e:
                    self.logger.error(f"转换future_return_2d到numpy数组失败: {e}")
                    return None
            
            # 处理缺失值
            try:
                # 检查是否有NaN值
                nan_mask = np.isnan(future_returns)
                if np.any(nan_mask):
                    # 使用均值填充缺失值
                    valid_returns = future_returns[~nan_mask]
                    if len(valid_returns) == 0:
                        self.logger.warning(f"股票 {stock_code} 的future_return_2d全为NaN，跳过变化点检测")
                        return None
                    
                    mean_return = np.mean(valid_returns)
                    future_returns = np.nan_to_num(future_returns, nan=mean_return)
            except Exception as e:
                self.logger.error(f"处理股票 {stock_code} 的future_return_2d时出错: {e}")
                # 尝试一种更安全的方式处理数据
                try:
                    # 手动处理NaN和无效值
                    clean_returns = []
                    for val in future_returns:
                        try:
                            fval = float(val)
                            if np.isfinite(fval):
                                clean_returns.append(fval)
                            else:
                                clean_returns.append(0.0)  # 用0替换无效值
                        except (ValueError, TypeError):
                            clean_returns.append(0.0)
                    
                    if not clean_returns:
                        return None
                    
                    future_returns = np.array(clean_returns, dtype=float)
                except Exception as e2:
                    self.logger.error(f"尝试修复future_returns失败: {e2}")
                    return None
            
            # 输出future_return_2d统计信息
            returns_mean = np.mean(future_returns)
            returns_std = np.std(future_returns)
            returns_min = np.min(future_returns)
            returns_max = np.max(future_returns)
            self.logger.info(f"股票 {stock_code} future_return_2d统计: 均值={returns_mean:.4f}, 标准差={returns_std:.4f}, 最小值={returns_min:.4f}, 最大值={returns_max:.4f}")
            
            # 将数据转换为二维数组（为了兼容ruptures库的输入格式）
            indicator_matrix = future_returns.reshape(-1, 1)
            
            # 标准化数据
            scaler = StandardScaler()
            indicator_matrix_scaled = scaler.fit_transform(indicator_matrix)
            
            # 计算信号长度和最小分段长度
            n_samples = len(indicator_matrix_scaled)
            
            # 根据数据长度动态调整参数
            if n_samples > 100:
                min_size = min(10, n_samples // 20)  # 减小最小分段长度，最小为10或数据长度的5%
                max_points = min(20, n_samples // 10)  # 最多检测点数不超过20或数据长度的10%
            else:
                min_size = min(5, n_samples // 10)  # 对于短序列，使用更小的分段长度
                max_points = min(10, n_samples // 5)  # 对于短序列，限制变化点数量
            
            # 降低惩罚值 - 使用自适应惩罚值
            standard_penalty = 0.5 * np.log(n_samples)  # 大幅降低惩罚，使算法更敏感
            
            # 变化点检测结果容器
            change_points_results = []
            
            # === 方法1: Pelt算法 ===
            try:
                pelt_model = Pelt(model="l2", min_size=min_size)
                pelt_points = pelt_model.fit_predict(indicator_matrix_scaled, pen=standard_penalty)
                # 过滤掉序列末尾点
                pelt_points = [p for p in pelt_points if p < n_samples]
                
                if len(pelt_points) > 0:
                    self.logger.info(f"Pelt-L2 检测到 {len(pelt_points)} 个变化点")
                    change_points_results.append(('pelt_l2', pelt_points))
                
                # 如果检测不到点，进一步降低惩罚值
                if len(pelt_points) < 2:
                    lower_penalty = standard_penalty * 0.5
                    pelt_points = pelt_model.fit_predict(indicator_matrix_scaled, pen=lower_penalty)
                    pelt_points = [p for p in pelt_points if p < n_samples]
                    
                    if len(pelt_points) > 0:
                        self.logger.info(f"Pelt-L2(低惩罚) 检测到 {len(pelt_points)} 个变化点")
                        change_points_results.append(('pelt_l2_low', pelt_points))
            except Exception as e:
                self.logger.warning(f"Pelt-L2 算法失败: {e}")
            
            # === 方法2: Binseg算法 ===
            try:
                # 二分割分割法，不需要惩罚，直接指定分割数量
                binseg_model = Binseg(model="l2", min_size=min_size)
                binseg_points = binseg_model.fit_predict(indicator_matrix_scaled, n_bkps=max_points)
                binseg_points = [p for p in binseg_points if p < n_samples]
                
                if len(binseg_points) > 0:
                    self.logger.info(f"Binseg 检测到 {len(binseg_points)} 个变化点")
                    change_points_results.append(('binseg', binseg_points))
            except Exception as e:
                self.logger.warning(f"Binseg 算法失败: {e}")
            
            # === 方法3: Window算法 ===
            try:
                # 窗口宽度为数据长度的10%，最小10个点
                window_width = max(10, n_samples // 10)
                window_model = Window(width=window_width, model="l2", min_size=min_size)
                window_points = window_model.fit_predict(indicator_matrix_scaled, n_bkps=max_points)
                window_points = [p for p in window_points if p < n_samples]
                
                if len(window_points) > 0:
                    self.logger.info(f"Window 检测到 {len(window_points)} 个变化点")
                    change_points_results.append(('window', window_points))
            except Exception as e:
                self.logger.warning(f"Window 算法失败: {e}")
            
            # 选择变化点结果
            if not change_points_results:
                self.logger.warning(f"股票 {stock_code} 未能使用任何方法检测到变化点")
                return None
            
            # 评估每种算法结果的质量
            best_algorithm = None
            final_change_points = []
            
            # 偏好基于最大收益率变化的检测点
            best_score = -1
            
            for alg_name, points in change_points_results:
                if len(points) < 2:  # 至少需要两个变化点
                    continue
                
                # 计算变化点前后的收益率变化幅度
                return_changes = []
                for i in range(len(points)-1):
                    start, end = points[i], points[i+1]
                    if start < len(future_returns) and end < len(future_returns):
                        segment1 = future_returns[start:end]
                        mean1 = np.mean(segment1)
                        
                        # 计算相邻段的均值变化
                        if i+2 < len(points):
                            next_end = points[i+2]
                            if next_end < len(future_returns):
                                segment2 = future_returns[end:next_end]
                                mean2 = np.mean(segment2)
                                return_changes.append(abs(mean2 - mean1))
                
                # 如果有足够的变化，计算平均变化幅度
                if return_changes:
                    avg_change = np.mean(return_changes)
                    # 变化幅度越大越好，同时考虑变化点数量适中
                    score = avg_change * min(1.0, 10.0/len(points))
                    
                    if score > best_score:
                        best_score = score
                        best_algorithm = alg_name
                        final_change_points = points
            
            # 如果未能选出最佳算法，选择检测到变化点最多的算法
            if not best_algorithm:
                sorted_results = sorted(change_points_results, key=lambda x: len(x[1]), reverse=True)
                best_algorithm, final_change_points = sorted_results[0]
            
            # 记录变化点
            if final_change_points:
                # 确保变化点是整数
                final_change_points = [int(cp) for cp in final_change_points]
                
                # 按顺序排序
                final_change_points.sort()
                
                # 获取变化点对应的日期
                cp_dates = dates[final_change_points]
                
                # 获取变化点对应的future_return_2d值
                cp_returns = future_returns[final_change_points]
                
                # 创建结果字典
                result = {
                    'change_points': final_change_points,
                    'dates': cp_dates,
                    'returns': cp_returns,
                    'algorithm': best_algorithm
                }
                
                self.logger.info(f"股票 {stock_code} 最终使用 {best_algorithm} 检测到 {len(final_change_points)} 个变化点")
                return result
            else:
                self.logger.warning(f"股票 {stock_code} 未能检测到任何变化点")
                return None
            
        except Exception as e:
            self.logger.error(f"检测股票 {stock_code} 变化点时出错: {e}")
            self.logger.error(traceback.format_exc())
            return None

    def _analyze_stock_change_point_patterns(self, train_features, change_point_results, relations_df=None):
        """
        分析个股变化点的特征模式，基于机器学习和量化金融的先进方法

        参考论文方法：
        1. "Regime Detection in Financial Markets" - 使用多元统计方法识别市场状态
        2. "Feature Engineering for Financial Time Series" - 特征工程最佳实践
        3. "Pattern Recognition in Stock Market" - 模式识别在股市中的应用

        Args:
            train_features: 训练期股票特征DataFrame (包含股票特征和龙头特征)
            change_point_results: 变化点检测结果
            relations_df: 股票板块关联结果DataFrame (包含板块特征)

        Returns:
            dict: 包含多层次特征模式的字典
        """
        import multiprocessing as mp
        from functools import partial
        from tqdm import tqdm
        from concurrent.futures import ThreadPoolExecutor
        import hashlib
        import pickle
        import os
        
        # 创建缓存目录
        cache_dir = os.path.join(self.model_dir, "pattern_cache")
        os.makedirs(cache_dir, exist_ok=True)
        
        # 生成缓存键 - 基于输入数据的哈希值
        cache_key = ""
        try:
            # 使用输入数据的关键特征创建唯一哈希
            features_hash = hashlib.md5(str(train_features.shape).encode()).hexdigest()
            change_points_hash = hashlib.md5(str(len(change_point_results)).encode()).hexdigest()
            relations_hash = "" if relations_df is None else hashlib.md5(str(relations_df.shape).encode()).hexdigest()
            cache_key = f"{features_hash}_{change_points_hash}_{relations_hash}"
            cache_file = os.path.join(cache_dir, f"pattern_{cache_key}.pkl")

            # 检查缓存是否存在
            if os.path.exists(cache_file):
                self.logger.info(f"从缓存加载变化点模式分析结果: {cache_file}")
                with open(cache_file, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            self.logger.warning(f"缓存检查失败: {e}")

        self.logger.info("开始分析个股变化点特征模式，使用先进的机器学习方法...")

        # 第一步：数据预处理和特征提取
        self.logger.info("步骤1: 提取变化点的股票特征和板块特征...")
        change_point_features = self._extract_change_point_features(
            train_features, change_point_results, relations_df
        )

        if not change_point_features:
            self.logger.warning("未能提取到有效的变化点特征")
            return {
                'positive_patterns': [],
                'negative_patterns': [],
                'feature_patterns': {},
                'pattern_clusters': {},
                'predictive_models': {}
            }

        # 第二步：特征工程和模式识别
        self.logger.info("步骤2: 进行特征工程和模式识别...")
        pattern_analysis = self._perform_pattern_analysis(change_point_features)

        # 第三步：构建预测模型
        self.logger.info("步骤3: 构建基于模式的预测模型...")
        predictive_models = self._build_pattern_predictive_models(change_point_features)

        # 第四步：模式聚类和分类
        self.logger.info("步骤4: 进行模式聚类和分类...")
        pattern_clusters = self._cluster_change_point_patterns(change_point_features)

        # 优化数据结构：只保留预测阶段需要的数据，减少内存使用
        # 从pattern_analysis中获取已计算的特征重要性，避免重复计算
        overall_feature_importance = pattern_analysis.get('feature_importance', {})
        if not overall_feature_importance:
            # 如果pattern_analysis中没有特征重要性，则计算一次
            overall_feature_importance = self._calculate_change_point_feature_importance(change_point_features, 'future_return_2d')

        # 优化后的数据结构：只保留预测必需的数据
        final_patterns = {
            # 核心预测数据
            'pattern_analysis': pattern_analysis,  # 包含positive_patterns和negative_patterns
            'predictive_models': predictive_models,  # 训练好的ML模型

            # 可选的分析数据（仅在需要时保留）
            'feature_importance': overall_feature_importance,  # 特征重要性分析

            # 元数据
            'data_summary': {
                'total_change_points': len(change_point_features),
                'positive_patterns_count': len(pattern_analysis.get('positive_patterns', [])),
                'negative_patterns_count': len(pattern_analysis.get('negative_patterns', [])),
                'models_trained': len(predictive_models) if isinstance(predictive_models, dict) else 0,
                'analysis_timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }

        # 可选：如果需要完整的调试信息，可以添加详细数据（但会增加内存使用）
        if self.logger.level <= 10:  # DEBUG级别
            final_patterns['debug_info'] = {
                'change_point_features_sample': change_point_features[:5] if change_point_features else [],
                'pattern_clusters': pattern_clusters,
                'regime_transitions': self._analyze_regime_transitions(change_point_features) if change_point_features else {}
            }

        self.logger.info(f"模式分析完成，共识别出 {len(change_point_features)} 个变化点特征")

        # 保存缓存
        try:
            if cache_key:
                self.logger.info(f"保存变化点模式分析结果到缓存: {cache_file}")
                with open(cache_file, 'wb') as f:
                    pickle.dump(final_patterns, f)
        except Exception as e:
            self.logger.warning(f"保存缓存文件失败: {e}")

        return final_patterns

    def _extract_change_point_features(self, train_features, change_point_results, relations_df):
        """
        提取变化点的股票特征和板块特征 - 高性能优化版本

        优化策略：
        1. 使用索引加速查询，避免重复的DataFrame扫描
        2. 批量处理变化点，减少循环开销
        3. 预先构建查找表，避免重复计算
        4. 使用向量化操作替代逐行处理
        """
        import time
        start_time = time.time()

        self.logger.info("开始提取变化点特征（优化版本）...")

        if not change_point_results:
            return []

        # 步骤1：创建高效索引
        self.logger.info("创建索引以加速查询...")

        # 为train_features创建多级索引
        train_features_idx = train_features.set_index(['stock_code', 'date_str'])

        # 为relations_df创建多级索引（如果存在）
        relations_idx = None
        if relations_df is not None and not relations_df.empty:
            # 确保relations_df有正确的列名
            if 'date' in relations_df.columns:
                relations_idx = relations_df.set_index(['stock_code', 'date'])
            elif 'date_str' in relations_df.columns:
                relations_idx = relations_df.set_index(['stock_code', 'date_str'])

        # 步骤2：批量收集所有需要查询的(stock_code, date)组合
        query_pairs = []
        change_point_metadata = []  # 存储变化点元数据

        for stock_code, stock_data in change_point_results.items():
            if 'change_points' not in stock_data or not stock_data['change_points']:
                continue

            dates = stock_data['dates']
            returns = stock_data.get('returns', [])  # 获取收益率数据

            for cp_idx, cp_date in enumerate(dates):
                query_pairs.append((stock_code, cp_date))
                # 修复：使用returns而不是change_points来获取收益率
                future_return = returns[cp_idx] if cp_idx < len(returns) else None
                change_point_metadata.append({
                    'stock_code': stock_code,
                    'date': cp_date,
                    'cp_idx': cp_idx,
                    'future_return': future_return
                })

        if not query_pairs:
            self.logger.warning("没有有效的变化点数据")
            return []

        self.logger.info(f"需要处理 {len(query_pairs)} 个变化点")

        # 步骤3：批量查询股票特征
        self.logger.info("批量查询股票特征...")
        stock_features_batch = {}

        # 使用索引批量查询，避免逐个查询
        valid_pairs = []
        for i, (stock_code, date) in enumerate(query_pairs):
            try:
                stock_row = train_features_idx.loc[(stock_code, date)]
                stock_features_batch[(stock_code, date)] = stock_row.to_dict()
                valid_pairs.append(i)
            except KeyError:
                # 跳过不存在的数据
                continue

        self.logger.info(f"成功查询到 {len(stock_features_batch)} 个股票特征")

        # 步骤4：批量查询板块特征
        self.logger.info("批量查询板块特征...")
        sector_features_batch = {}

        if relations_idx is not None:
            for stock_code, date in stock_features_batch.keys():
                try:
                    sector_row = relations_idx.loc[(stock_code, date)]
                    sector_features_batch[(stock_code, date)] = sector_row.to_dict()
                except KeyError:
                    # 如果没有板块特征，使用空字典
                    sector_features_batch[(stock_code, date)] = {}
        else:
            # 如果没有relations_df，所有板块特征都为空
            for key in stock_features_batch.keys():
                sector_features_batch[key] = {}

        # 步骤5：批量组装特征
        self.logger.info("批量组装特征...")
        change_point_features = []

        for i in valid_pairs:
            metadata = change_point_metadata[i]
            stock_code = metadata['stock_code']
            date = metadata['date']
            key = (stock_code, date)

            if key in stock_features_batch:
                # 组合特征（避免重复的字典复制）
                feature_dict = stock_features_batch[key].copy()
                feature_dict.update(sector_features_batch.get(key, {}))
                feature_dict['future_return_2d'] = metadata['future_return']
                feature_dict['change_point_idx'] = metadata['cp_idx']

                change_point_features.append(feature_dict)

        elapsed_time = time.time() - start_time
        self.logger.info(f"特征提取完成，共提取 {len(change_point_features)} 个变化点特征，用时 {elapsed_time:.2f} 秒")

        return change_point_features

    def _perform_pattern_analysis(self, change_point_features):
        """
        进行板块分层的模式分析，基于机器学习方法识别有效模式

        优化策略：
        1. 按板块分层进行模式分析
        2. 使用向量化操作替代循环
        3. 实现板块特定的模式识别
        4. 提供全市场回退机制
        """
        if not change_point_features:
            return {}

        self.logger.info(f"开始板块分层模式分析，共 {len(change_point_features)} 个变化点特征")

        # 第一步：按板块分组变化点
        sector_patterns = {}
        for feature_dict in change_point_features:
            sector = feature_dict.get('most_relevant_sector', 'unknown')
            if sector not in sector_patterns:
                sector_patterns[sector] = []
            sector_patterns[sector].append(feature_dict)

        self.logger.info(f"发现 {len(sector_patterns)} 个板块的变化点数据")

        # 第二步：对每个板块进行独立的模式分析
        sector_analysis = {}
        global_positive_patterns = []
        global_negative_patterns = []
        min_samples_per_sector = 100  # 每个板块最少样本量

        for sector, patterns in sector_patterns.items():
            if len(patterns) < min_samples_per_sector:
                self.logger.info(f"板块 {sector} 样本量不足({len(patterns)} < {min_samples_per_sector})，将合并到全市场模式")
                # 样本量不足的板块合并到全市场模式
                for pattern in patterns:
                    future_return = pattern.get('future_return_2d')
                    if future_return is not None:
                        try:
                            future_return = float(future_return)
                            if future_return > 0.02:
                                global_positive_patterns.append(pattern)
                            elif future_return < -0.02:
                                global_negative_patterns.append(pattern)
                        except (ValueError, TypeError):
                            continue
                continue

            # 对样本量充足的板块进行独立分析
            self.logger.info(f"分析板块 {sector}，样本量: {len(patterns)}")
            sector_result = self._analyze_single_sector_patterns(patterns, sector)
            if sector_result:
                sector_analysis[sector] = sector_result

        # 第三步：创建全市场回退模式（用于样本量不足的板块）
        if global_positive_patterns or global_negative_patterns:
            global_patterns = global_positive_patterns + global_negative_patterns
            global_result = self._analyze_single_sector_patterns(global_patterns, '_global_fallback')
            if global_result:
                sector_analysis['_global_fallback'] = global_result
                self.logger.info(f"全市场回退模式: 正向模式 {len(global_positive_patterns)}, 负向模式 {len(global_negative_patterns)}")

        # 第四步：计算整体统计信息
        total_positive = sum(analysis['positive_count'] for analysis in sector_analysis.values())
        total_negative = sum(analysis['negative_count'] for analysis in sector_analysis.values())

        # 计算全局特征重要性
        global_feature_importance = self._calculate_change_point_feature_importance(change_point_features)

        return {
            'sector_analysis': sector_analysis,
            'global_feature_importance': global_feature_importance,
            'total_positive_patterns': total_positive,
            'total_negative_patterns': total_negative,
            'total_patterns': len(change_point_features),
            'sectors_with_sufficient_samples': len([s for s in sector_analysis.keys() if s != '_global_fallback']),
            'min_samples_threshold': min_samples_per_sector,
            # 保持向后兼容性
            'positive_patterns': global_positive_patterns,
            'negative_patterns': global_negative_patterns,
            'feature_importance': global_feature_importance,
            'pattern_count': {
                'positive': total_positive,
                'negative': total_negative,
                'total': len(change_point_features)
            }
        }

    def _analyze_single_sector_patterns(self, patterns, sector_name):
        """分析单个板块的模式，确保特征质量"""
        if not patterns:
            return None

        # 提取有效的future_return数据
        future_returns = []
        valid_indices = []

        for i, feature_dict in enumerate(patterns):
            future_return = feature_dict.get('future_return_2d')
            if future_return is not None:
                try:
                    future_return_float = float(future_return)
                    future_returns.append(future_return_float)
                    valid_indices.append(i)
                except (ValueError, TypeError):
                    continue

        if not future_returns:
            return None

        # 转换为numpy数组进行向量化操作
        future_returns = np.array(future_returns)

        # 分析阈值分布
        positive_count = np.sum(future_returns > 0.02)
        negative_count = np.sum(future_returns < -0.02)

        # 动态调整阈值（如果需要）
        if negative_count == 0 and len(future_returns) > 20:
            # 尝试更小的阈值
            for threshold in [0.015, 0.01, 0.005]:
                neg_count_test = np.sum(future_returns < -threshold)
                if neg_count_test > 0:
                    positive_mask = future_returns > threshold
                    negative_mask = future_returns < -threshold
                    break
            else:
                # 使用中位数分割
                median_return = np.median(future_returns)
                positive_mask = future_returns > median_return
                negative_mask = future_returns <= median_return
        else:
            # 使用原始阈值
            positive_mask = future_returns > 0.02
            negative_mask = future_returns < -0.02

        # 构建模式列表
        positive_patterns = [patterns[valid_indices[i]]
                           for i in range(len(valid_indices)) if positive_mask[i]]
        negative_patterns = [patterns[valid_indices[i]]
                           for i in range(len(valid_indices)) if negative_mask[i]]

        # 对板块内的模式进行特征质量检查
        if positive_patterns:
            positive_patterns = self._filter_sector_patterns(positive_patterns, f"{sector_name}_positive")
        if negative_patterns:
            negative_patterns = self._filter_sector_patterns(negative_patterns, f"{sector_name}_negative")

        # 计算板块特定的特征重要性
        sector_feature_importance = self._calculate_pattern_feature_importance(
            positive_patterns, negative_patterns
        )

        self.logger.info(f"板块 {sector_name}: 正向模式 {len(positive_patterns)}, 负向模式 {len(negative_patterns)}, 总样本 {len(patterns)}")

        return {
            'positive_patterns': positive_patterns,
            'negative_patterns': negative_patterns,
            'positive_count': len(positive_patterns),
            'negative_count': len(negative_patterns),
            'total_samples': len(patterns),
            'feature_importance': sector_feature_importance,
            'sector_name': sector_name
        }

    def _filter_sector_patterns(self, patterns, pattern_type):
        """过滤板块内模式，移除在该子集中全零的特征"""
        if not patterns:
            return patterns

        # 获取特征列表（排除标识字段）
        sample_pattern = patterns[0]
        exclude_fields = [
            'stock_code', 'date_str', 'date', 'future_return_2d',
            'most_relevant_sector', 'sector_correlation_score',
            'is_sector_leader', 'change_point_idx'
        ]
        all_features = [k for k in sample_pattern.keys()
                       if k not in exclude_fields and not k.startswith('_')]

        # 检查每个特征在这个子集中的变化性
        valid_features = []
        for feature in all_features:
            values = []
            for pattern in patterns:
                value = pattern.get(feature, 0)
                try:
                    values.append(float(value))
                except (ValueError, TypeError):
                    values.append(0.0)

            # 检查特征是否有变化
            if len(set(values)) > 1 and np.std(values) > 1e-8:
                valid_features.append(feature)
            else:
                self.logger.debug(f"{pattern_type} 移除无变化特征: {feature}")

        # 重构模式，只保留有效特征
        filtered_patterns = []
        for pattern in patterns:
            filtered_pattern = {}
            # 保留标识字段
            for field in ['stock_code', 'date_str', 'date', 'future_return_2d']:
                if field in pattern:
                    filtered_pattern[field] = pattern[field]
            # 保留有效特征
            for feature in valid_features:
                if feature in pattern:
                    filtered_pattern[feature] = pattern[feature]
            filtered_patterns.append(filtered_pattern)

        self.logger.info(f"{pattern_type} 特征过滤: {len(all_features)} -> {len(valid_features)} 个有效特征")
        return filtered_patterns

    def _build_pattern_predictive_models(self, change_point_features):
        """构建基于模式的预测模型"""
        try:
            from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
            from sklearn.model_selection import train_test_split
            from sklearn.preprocessing import StandardScaler
            from sklearn.metrics import classification_report

            if len(change_point_features) < 50:  # 样本太少
                self.logger.warning("样本太少，无法构建预测模型")
                return {'status': 'insufficient_data', 'min_required': 50}

            # 准备训练数据
            X, y, key_features = self._prepare_model_training_data(change_point_features)

            if len(X) < 50:
                self.logger.warning("有效样本不足，无法构建预测模型")
                return {'status': 'insufficient_valid_data'}

            # 分割训练和测试数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )

            # 标准化特征
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)

            # 训练多个模型
            models = {
                'random_forest': RandomForestClassifier(n_estimators=100, random_state=42),
                'gradient_boosting': GradientBoostingClassifier(n_estimators=100, random_state=42)
            }

            model_results = {}
            for name, model in models.items():
                model.fit(X_train_scaled, y_train)
                score = model.score(X_test_scaled, y_test)
                model_results[name] = {
                    'model': model,
                    'scaler': scaler,  # 每个模型使用相同的scaler
                    'accuracy': score,
                    'feature_importance': model.feature_importances_.tolist() if hasattr(model, 'feature_importances_') else None,
                    'feature_names': key_features,  # 保存特征名称列表
                    'n_features': len(key_features)  # 保存特征数量
                }
                self.logger.info(f"模型 {name} 训练完成，准确率: {score:.3f}, 特征数量: {len(key_features)}")

            # 添加成功状态标识和全局信息
            model_results['status'] = 'success'
            model_results['total_models'] = len(models)
            model_results['training_samples'] = len(X_train)
            model_results['test_samples'] = len(X_test)
            model_results['global_feature_names'] = key_features  # 全局特征名称
            model_results['global_scaler'] = scaler  # 全局scaler

            self.logger.info(f"预测模型构建成功，共训练 {len(models)} 个模型")
            return model_results

        except ImportError:
            self.logger.warning("sklearn未安装，跳过预测模型构建")
            return {'status': 'sklearn_not_available'}
        except Exception as e:
            self.logger.error(f"构建预测模型时出错: {e}")
            return {'status': 'error', 'message': str(e)}

    def _cluster_change_point_patterns(self, change_point_features):
        """对变化点模式进行聚类分析"""
        try:
            from sklearn.cluster import KMeans
            from sklearn.preprocessing import StandardScaler

            if len(change_point_features) < 10:
                return {'status': 'insufficient_data'}

            # 准备聚类数据
            X, feature_names = self._prepare_clustering_data(change_point_features)

            if len(X) < 10:
                return {'status': 'insufficient_valid_data'}

            # 标准化
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)

            # 确定最佳聚类数
            optimal_clusters = min(5, len(X) // 10 + 1)

            # 聚类
            kmeans = KMeans(n_clusters=optimal_clusters, random_state=42)
            cluster_labels = kmeans.fit_predict(X_scaled)

            # 分析每个聚类
            cluster_analysis = {}
            for i in range(optimal_clusters):
                cluster_mask = cluster_labels == i
                cluster_features = [change_point_features[j] for j in range(len(change_point_features)) if cluster_mask[j]]

                # 计算聚类统计
                returns = [f.get('future_return_2d', 0) for f in cluster_features if f.get('future_return_2d') is not None]
                avg_return = np.mean(returns) if returns else 0

                cluster_analysis[i] = {
                    'size': int(np.sum(cluster_mask)),
                    'avg_return': float(avg_return),
                    'return_std': float(np.std(returns)) if returns else 0,
                    'center': kmeans.cluster_centers_[i].tolist()
                }

            return {
                'status': 'success',
                'num_clusters': optimal_clusters,
                'cluster_labels': cluster_labels.tolist(),
                'cluster_analysis': cluster_analysis,
                'feature_names': feature_names
            }

        except ImportError:
            return {'status': 'sklearn_not_available'}
        except Exception as e:
            self.logger.error(f"聚类分析时出错: {e}")
            return {'status': 'error', 'message': str(e)}

    def _calculate_change_point_feature_importance(self, change_point_features, target_column='future_return_2d'):
        """
        计算特征重要性 - 高性能优化版本

        优化策略：
        1. 使用pandas DataFrame进行向量化操作
        2. 批量计算相关系数
        3. 避免重复的类型转换和检查

        Args:
            change_point_features: 变化点特征列表
            target_column: 目标列名，默认为'future_return_2d'
        """
        if not change_point_features:
            return {}

        self.logger.info(f"开始计算特征重要性，共 {len(change_point_features)} 个样本，目标列: {target_column}")

        try:
            # 检查输入数据类型
            if not isinstance(change_point_features, list):
                self.logger.error(f"change_point_features 应该是list类型，实际类型: {type(change_point_features)}")
                return {}

            if len(change_point_features) == 0:
                self.logger.warning("change_point_features 为空列表")
                return {}

            # 检查第一个元素是否为字典
            if not isinstance(change_point_features[0], dict):
                self.logger.error(f"change_point_features[0] 应该是dict类型，实际类型: {type(change_point_features[0])}")
                return {}

            # 转换为DataFrame进行向量化操作
            df = pd.DataFrame(change_point_features)
            self.logger.info(f"成功创建DataFrame，形状: {df.shape}，列: {list(df.columns)}")

            # 过滤掉不需要的列 - 使用与模式分析一致的排除列表
            exclude_cols = [
                target_column, 'stock_code', 'date', 'date_str', 'change_point_idx',
                'most_relevant_sector',  # 板块标识，不应作为特征
                'sector_correlation_score',  # 相关性得分，不应作为特征
                'is_sector_leader',  # 领导地位，可能在子集中全零
                'future_return_2d'  # 目标变量
            ]

            # 进一步过滤：移除可能全零的技术形态特征
            pattern_features_to_exclude = [
                'pattern_morning_star', 'pattern_evening_star',
                'pattern_three_white_soldiers', 'pattern_three_black_crows',
                'pattern_hanging_man', 'pattern_shooting_star',
                'pattern_engulfing_bullish', 'pattern_engulfing_bearish',
                'trend_state', 'stock_state'
            ]
            exclude_cols.extend(pattern_features_to_exclude)

            feature_cols = [col for col in df.columns if col not in exclude_cols and not col.startswith('_')]

            # 确保目标列存在且为数值型
            if target_column not in df.columns:
                self.logger.warning(f"目标列 {target_column} 不存在")
                return {}

            # 转换为数值型，无效值设为NaN
            df[target_column] = pd.to_numeric(df[target_column], errors='coerce')

            # 过滤掉目标列为空的行
            valid_mask = df[target_column].notna()
            if valid_mask.sum() < 10:  # 至少需要10个有效样本
                self.logger.warning("有效样本数量不足，无法计算特征重要性")
                return {}

            df_valid = df[valid_mask]

            # 批量转换特征列为数值型
            correlations = {}
            for feature_name in feature_cols:
                if feature_name not in df_valid.columns:
                    continue

                # 转换为数值型
                feature_values = pd.to_numeric(df_valid[feature_name], errors='coerce')

                # 检查有效样本数量
                valid_feature_mask = feature_values.notna()
                combined_valid = valid_feature_mask & df_valid[target_column].notna()

                if combined_valid.sum() >= 10:  # 至少10个有效样本
                    try:
                        # 计算相关系数，添加边界情况处理
                        feature_valid = feature_values[combined_valid]
                        target_valid = df_valid[target_column][combined_valid]

                        # 检查数据有效性
                        if len(feature_valid) == 0 or len(target_valid) == 0:
                            continue

                        # 检查是否所有值都相同（标准差为0）
                        if feature_valid.std() == 0 or target_valid.std() == 0:
                            # 如果标准差为0，相关系数无意义，跳过
                            continue

                        # 检查是否包含无穷大或NaN
                        if not (np.isfinite(feature_valid).all() and np.isfinite(target_valid).all()):
                            continue

                        # 使用numpy计算相关系数，更好地处理边界情况
                        with np.errstate(divide='ignore', invalid='ignore'):
                            corr_matrix = np.corrcoef(feature_valid, target_valid)
                            if corr_matrix.shape == (2, 2):
                                corr = corr_matrix[0, 1]
                                if pd.notna(corr) and np.isfinite(corr) and not np.isnan(corr):
                                    correlations[feature_name] = abs(corr)
                    except Exception as e:
                        # 跳过计算失败的特征
                        continue

            # 按重要性排序
            sorted_correlations = dict(sorted(correlations.items(), key=lambda x: x[1], reverse=True))

            self.logger.info(f"特征重要性计算完成，共计算 {len(sorted_correlations)} 个特征")

            return sorted_correlations

        except Exception as e:
            self.logger.error(f"计算特征重要性时出错: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return {}

    def _analyze_regime_transitions(self, change_point_features):
        """分析市场状态转换"""
        if not change_point_features:
            return {}

        # 按时间排序
        sorted_features = sorted(change_point_features,
                               key=lambda x: (x.get('stock_code', ''), x.get('date', '')))

        transitions = {
            'bull_to_bear': 0,
            'bear_to_bull': 0,
            'neutral_to_bull': 0,
            'neutral_to_bear': 0,
            'total_transitions': len(sorted_features)
        }

        # 简单的状态转换分析
        for feature_dict in sorted_features:
            future_return = feature_dict.get('future_return_2d', 0)
            try:
                future_return = float(future_return)
                if future_return > 0.05:
                    transitions['neutral_to_bull'] += 1
                elif future_return < -0.05:
                    transitions['neutral_to_bear'] += 1
            except (ValueError, TypeError):
                continue

        return transitions

    def _calculate_pattern_feature_importance(self, positive_patterns, negative_patterns):
        """计算模式特征重要性"""
        all_patterns = positive_patterns + negative_patterns
        if not all_patterns:
            return {}

        # 为每个模式添加标签
        labeled_patterns = []
        for pattern in positive_patterns:
            pattern_copy = pattern.copy()
            pattern_copy['label'] = 1
            labeled_patterns.append(pattern_copy)

        for pattern in negative_patterns:
            pattern_copy = pattern.copy()
            pattern_copy['label'] = 0
            labeled_patterns.append(pattern_copy)

        return self._calculate_change_point_feature_importance(labeled_patterns, 'label')

    def _prepare_model_training_data(self, change_point_features):
        """
        准备模型训练数据 - 优化版本

        优化策略：
        1. 使用pandas DataFrame进行向量化操作
        2. 批量处理数据转换
        3. 减少循环和类型检查
        """
        if not change_point_features:
            return np.array([]), np.array([]), []

        # 使用一致的特征提取逻辑
        if change_point_features:
            # 创建临时DataFrame来使用一致的特征提取方法
            temp_df = pd.DataFrame(change_point_features)
            key_features = self._get_consistent_feature_list(temp_df)
        else:
            # 如果没有数据，使用基本特征集
            key_features = ['pct_chg', 'vol', 'turnover_rate', 'amplitude']

        try:
            # 转换为DataFrame
            df = pd.DataFrame(change_point_features)
            self.logger.info(f"=== 训练数据分析 ===")
            self.logger.info(f"原始变化点特征数量: {len(change_point_features)}")

            # 过滤有效的future_return数据
            df['future_return_2d'] = pd.to_numeric(df['future_return_2d'], errors='coerce')
            valid_mask = df['future_return_2d'].notna()
            df_valid = df[valid_mask]

            self.logger.info(f"有效future_return样本数: {len(df_valid)}")
            if len(df_valid) == 0:
                self.logger.error("没有有效的future_return数据")
                return np.array([]), np.array([]), []

            # 分析future_return分布
            future_returns = df_valid['future_return_2d'].values
            self.logger.info(f"Future return分布: 范围={future_returns.min():.6f} - {future_returns.max():.6f}")
            self.logger.info(f"Future return标准差: {future_returns.std():.6f}")

            # 创建标签：1为正向，0为负向
            y = (df_valid['future_return_2d'] > 0.02).astype(int).values
            positive_count = np.sum(y)
            negative_count = len(y) - positive_count
            self.logger.info(f"标签分布: 正样本={positive_count}, 负样本={negative_count}, 正样本比例={positive_count/len(y):.3f}")

            # 批量处理特征并分析
            X_list = []
            self.logger.info("=== 训练特征分析 ===")
            for feature_name in key_features:
                if feature_name in df_valid.columns:
                    # 转换为数值型，无效值填充为0
                    feature_values = pd.to_numeric(df_valid[feature_name], errors='coerce').fillna(0)
                    X_list.append(feature_values.values)

                    # 分析特征分布
                    unique_count = len(feature_values.unique())
                    std_dev = feature_values.std()
                    value_range = f"{feature_values.min():.6f} - {feature_values.max():.6f}"
                    self.logger.info(f"训练特征 {feature_name}: 唯一值={unique_count}, 标准差={std_dev:.6f}, 范围={value_range}")
                else:
                    # 如果特征不存在，填充为0
                    X_list.append(np.zeros(len(df_valid)))
                    self.logger.warning(f"训练特征 {feature_name} 不存在，填充为0")

            # 转置得到正确的形状
            X = np.column_stack(X_list)

            # 分析特征矩阵的差异性
            unique_training_rows = len(np.unique(X, axis=0))
            self.logger.info(f"训练特征矩阵: 总样本={len(X)}, 唯一特征向量数={unique_training_rows}")
            if unique_training_rows < len(X) * 0.1:
                self.logger.warning(f"训练数据特征向量重复率很高: {(1 - unique_training_rows/len(X))*100:.1f}%")
                # 显示重复最多的特征向量
                unique_vectors, counts = np.unique(X, axis=0, return_counts=True)
                most_common_idx = np.argmax(counts)
                self.logger.warning(f"最常见的特征向量出现 {counts[most_common_idx]} 次: {unique_vectors[most_common_idx]}")

            return X, y, key_features

        except Exception as e:
            self.logger.error(f"准备训练数据时出错: {e}")
            return np.array([]), np.array([]), []

    def _prepare_clustering_data(self, change_point_features):
        """
        准备聚类数据 - 优化版本

        优化策略：
        1. 使用pandas DataFrame进行向量化操作
        2. 批量处理数据转换
        """
        if not change_point_features:
            return np.array([]), []

        # 动态获取特征列表
        if change_point_features:
            sample_feature = change_point_features[0]
            exclude_fields = ['stock_code', 'date_str', 'date']
            feature_names = [k for k in sample_feature.keys()
                           if k not in exclude_fields and not k.startswith('_')]
        else:
            feature_names = ['pct_chg', 'vol', 'turnover_rate', 'amplitude', 'future_return_2d']

        try:
            # 转换为DataFrame
            df = pd.DataFrame(change_point_features)

            # 批量处理特征
            X_list = []
            for feature_name in feature_names:
                if feature_name in df.columns:
                    # 转换为数值型，无效值填充为0
                    feature_values = pd.to_numeric(df[feature_name], errors='coerce').fillna(0)
                    X_list.append(feature_values.values)
                else:
                    # 如果特征不存在，填充为0
                    X_list.append(np.zeros(len(df)))

            # 转置得到正确的形状
            X = np.column_stack(X_list)

            return X, feature_names

        except Exception as e:
            self.logger.error(f"准备聚类数据时出错: {e}")
            return np.array([]), []

    def _predict_stocks_with_change_points(self, predict_features, feature_patterns, relations_df=None):
        """
        基于变化点模式预测股票，使用重构后的先进方法

        Args:
            predict_features: 预测期股票特征DataFrame
            feature_patterns: 历史特征模式数据
            relations_df: 股票板块关联结果DataFrame，可选参数

        Returns:
            dict: 预测结果
        """
        self.logger.info("开始基于变化点模式进行股票预测...")

        if not feature_patterns or 'pattern_analysis' not in feature_patterns:
            self.logger.warning("模式分析结果为空或缺少pattern_analysis，无法进行预测")
            return {}

        # 获取预测模型
        predictive_models = feature_patterns.get('predictive_models', {})

        # 同时使用相似度匹配和训练模型进行预测，提供对比
        self.logger.info("使用基于变化点历史模式匹配的预测方法")

        # 首先尝试使用相似度匹配（这是核心的变化点模式匹配逻辑）
        similarity_predictions = self._predict_with_similarity_matching(predict_features, relations_df, feature_patterns)

        # 同时尝试使用训练模型进行预测
        model_predictions = {}
        if predictive_models and predictive_models.get('status') == 'success':
            self.logger.info("使用训练好的模型进行预测")
            model_predictions = self._predict_with_trained_models(predict_features, relations_df, predictive_models)

        # 分别处理两种预测方法，不合并
        prediction_results = {
            'similarity_predictions': similarity_predictions if similarity_predictions else {},
            'model_predictions': model_predictions if model_predictions else {},
            'comparison_analysis': {}
        }

        # 生成对比分析
        if similarity_predictions and model_predictions:
            comparison_analysis = self._analyze_prediction_comparison(similarity_predictions, model_predictions)
            prediction_results['comparison_analysis'] = comparison_analysis

        # 保存分析结果
        self._save_prediction_analysis(prediction_results)

        # 返回两种预测结果，供后续分别处理
        return {
            'similarity_predictions': similarity_predictions if similarity_predictions else {},
            'model_predictions': model_predictions if model_predictions else {}
        }

    def _predict_with_trained_models(self, predict_features, relations_df, predictive_models):
        """使用训练好的机器学习模型进行预测"""
        predictions = {}

        try:
            # 过滤出模型键（排除元数据键）
            model_keys = [k for k in predictive_models.keys()
                         if isinstance(predictive_models[k], dict) and 'model' in predictive_models[k]]

            if not model_keys:
                self.logger.error("没有找到有效的训练模型")
                return {}

            # 选择最佳模型
            best_model_name = max(model_keys,
                                key=lambda k: predictive_models[k].get('accuracy', 0))
            best_model_info = predictive_models[best_model_name]

            model = best_model_info['model']
            scaler = best_model_info['scaler']
            expected_features = best_model_info.get('feature_names', [])
            expected_n_features = best_model_info.get('n_features', 0)

            self.logger.info(f"使用最佳模型 {best_model_name} (准确率: {best_model_info['accuracy']:.3f}) 进行预测")
            self.logger.info(f"模型期望特征数量: {expected_n_features}, 特征列表: {expected_features[:10]}{'...' if len(expected_features) > 10 else ''}")

            # 准备测试数据，使用与训练时一致的特征
            X_test, stock_info = self._prepare_test_data_for_model_with_features(predict_features, relations_df, expected_features)

            if len(X_test) == 0:
                self.logger.warning("测试数据准备失败，没有有效样本")
                return {}

            # 检查特征数量是否匹配
            if X_test.shape[1] != expected_n_features:
                self.logger.error(f"特征数量不匹配: 测试数据有 {X_test.shape[1]} 个特征，模型期望 {expected_n_features} 个特征")
                self.logger.error(f"测试数据特征形状: {X_test.shape}")

                # 分析缺失的特征
                available_features = list(predict_features.columns)
                missing_features = [f for f in expected_features if f not in available_features]
                extra_features = [f for f in available_features if f not in expected_features]

                self.logger.error(f"缺失的特征 ({len(missing_features)}): {missing_features[:10]}{'...' if len(missing_features) > 10 else ''}")
                self.logger.error(f"额外的特征 ({len(extra_features)}): {extra_features[:10]}{'...' if len(extra_features) > 10 else ''}")

                # 尝试使用可用特征的子集
                common_features = [f for f in expected_features if f in available_features]
                if len(common_features) >= expected_n_features * 0.5:  # 如果有一半以上的特征可用
                    self.logger.warning(f"尝试使用可用特征子集 ({len(common_features)} 个特征)")
                    X_test, stock_info = self._prepare_test_data_for_model_with_features(predict_features, relations_df, common_features)
                    if X_test.shape[1] != len(common_features):
                        return {}
                    # 更新期望特征数量
                    expected_n_features = len(common_features)
                else:
                    return {}

            # 标准化特征
            try:
                X_test_scaled = scaler.transform(X_test)
            except ValueError as e:
                self.logger.error(f"特征标准化失败: {e}")
                self.logger.error(f"X_test形状: {X_test.shape}, scaler期望特征数: {scaler.n_features_in_ if hasattr(scaler, 'n_features_in_') else '未知'}")
                return {}

            # 预测
            predictions_proba = model.predict_proba(X_test_scaled)
            predictions_binary = model.predict(X_test_scaled)

            # 添加调试信息：检查预测结果的分布
            proba_scores = predictions_proba[:, 1]  # 正向概率
            unique_scores = len(set(proba_scores))
            self.logger.info(f"模型预测结果分析: 总样本 {len(proba_scores)}, 唯一得分数 {unique_scores}")
            self.logger.info(f"预测得分范围: {proba_scores.min():.6f} - {proba_scores.max():.6f}")
            self.logger.info(f"预测得分标准差: {proba_scores.std():.6f}")

            # 如果所有预测得分都相同，添加随机扰动
            if unique_scores == 1:
                self.logger.warning("所有预测得分相同，添加小幅随机扰动以区分股票")
                np.random.seed(42)  # 确保可重复性
                random_noise = np.random.normal(0, 0.001, len(proba_scores))  # 小幅随机噪声
                proba_scores = proba_scores + random_noise
                # 重新构建predictions_proba
                predictions_proba = np.column_stack([1 - proba_scores, proba_scores])

            # 整理预测结果 - 修复82%异常值问题
            for i, stock_info_item in enumerate(stock_info):
                if i < len(predictions_proba):
                    stock_code = stock_info_item['stock_code']
                    date = stock_info_item['date']
                    # 使用 (stock_code, date) 作为键，避免同一股票不同日期的数据被覆盖
                    key = f"{stock_code}_{date}"

                    # 修复预测得分处理逻辑
                    raw_prediction_score = float(predictions_proba[i][1])  # 正向概率

                    # 将概率转换为合理的收益率预期
                    # 概率值在[0,1]范围内，需要转换为收益率预期
                    if 0 <= raw_prediction_score <= 1:
                        # 使用概率到收益率的合理映射
                        # 概率0.5对应0%收益率，概率越高收益率越高
                        if raw_prediction_score > 0.6:
                            # 高概率对应正收益率，最高不超过8%
                            prediction_score = (raw_prediction_score - 0.5) * 0.16  # 0.5的概率差对应8%收益率
                        elif raw_prediction_score < 0.4:
                            # 低概率对应负收益率，最低不超过-8%
                            prediction_score = (raw_prediction_score - 0.5) * 0.16
                        else:
                            # 中等概率对应较小收益率
                            prediction_score = (raw_prediction_score - 0.5) * 0.08
                    else:
                        # 如果概率值异常，使用默认值
                        prediction_score = 0.0
                        self.logger.warning(f"股票 {stock_code} 的预测概率异常: {raw_prediction_score}")

                    # 确保预测得分在合理范围内
                    prediction_score = np.clip(prediction_score, -0.08, 0.08)  # 限制在-8%到+8%

                    predictions[key] = {
                        'stock_code': stock_code,
                        'date': date,
                        'prediction_score': prediction_score,  # 修复后的预测得分（小数形式）
                        'raw_probability': raw_prediction_score,  # 保留原始概率用于调试
                        'prediction_binary': int(predictions_binary[i]),
                        'model_used': best_model_name
                    }

            self.logger.info(f"完成模型预测，共预测 {len(predictions)} 只股票")

        except Exception as e:
            self.logger.error(f"模型预测失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            self.logger.error(f"predictive_models 结构: {list(predictive_models.keys())}")
            self.logger.error(f"model_keys: {model_keys if 'model_keys' in locals() else 'undefined'}")
            return {}

        return predictions

    def _predict_with_similarity_matching(self, predict_features, relations_df, feature_patterns):
        """
        使用相似度匹配进行预测 - 高性能优化版本

        优化策略：
        1. 预处理历史模式特征矩阵，避免重复计算
        2. 使用向量化操作批量计算相似度
        3. 减少DataFrame操作次数
        4. 优化内存使用和数据结构
        """
        import time
        start_time = time.time()

        predictions = {}

        try:
            # 获取历史模式 - 修复数据结构问题
            # 首先检查feature_patterns的结构
            self.logger.info(f"feature_patterns类型: {type(feature_patterns)}")
            self.logger.info(f"feature_patterns键: {list(feature_patterns.keys()) if isinstance(feature_patterns, dict) else 'Not a dict'}")

            # 实现渐进式内存管理策略
            total_samples = len(predict_features)
            self.logger.info(f"开始处理 {total_samples} 个预测样本")

            # 检查是否有板块分层的模式分析结果
            sector_analysis = feature_patterns.get('sector_analysis', {})

            if sector_analysis:
                # 优先使用板块特定的相似度匹配（支持大规模数据）
                self.logger.info("使用板块分层的相似度匹配（支持大规模数据处理）")
                return self._predict_with_sector_specific_matching(predict_features, feature_patterns)

            # 如果没有板块分层结果，根据数据规模选择处理策略
            if total_samples <= 100000:  # 10万以下使用传统方法
                self.logger.info(f"使用传统的全市场模式匹配（{total_samples} 样本）")
            elif total_samples <= 500000:  # 10-50万使用分批处理
                self.logger.info(f"数据规模较大（{total_samples} 样本），使用分批处理策略")
                return self._predict_with_batch_processing(predict_features, feature_patterns)
            else:  # 超过50万才拒绝处理
                self.logger.error(f"数据规模过大({total_samples})，超过系统处理能力，建议使用板块分层模式")
                return {}

            # 尝试多种可能的数据结构
            positive_patterns = []
            negative_patterns = []

            # 方法1: 从pattern_analysis获取
            if 'pattern_analysis' in feature_patterns:
                pattern_analysis = feature_patterns['pattern_analysis']
                positive_patterns = pattern_analysis.get('positive_patterns', [])
                negative_patterns = pattern_analysis.get('negative_patterns', [])
                self.logger.info(f"从pattern_analysis获取: 正向={len(positive_patterns)}, 负向={len(negative_patterns)}")

            # 方法2: 如果上面没有数据，尝试直接从feature_patterns获取
            if not positive_patterns and not negative_patterns:
                positive_patterns = feature_patterns.get('positive_patterns', [])
                negative_patterns = feature_patterns.get('negative_patterns', [])
                self.logger.info(f"直接获取: 正向={len(positive_patterns)}, 负向={len(negative_patterns)}")

            if not positive_patterns and not negative_patterns:
                self.logger.warning("没有历史模式可用于相似度匹配")
                return {}

            # 分析历史模式的特征分布
            if positive_patterns:
                self.logger.info("=== 正向模式特征分析 ===")
                self._analyze_pattern_features(positive_patterns[:5], "正向")  # 只分析前5个

            if negative_patterns:
                self.logger.info("=== 负向模式特征分析 ===")
                self._analyze_pattern_features(negative_patterns[:5], "负向")  # 只分析前5个

            # 关键特征用于相似度计算 - 动态获取所有可用特征，排除标识字段
            if positive_patterns or negative_patterns:
                sample_pattern = positive_patterns[0] if positive_patterns else negative_patterns[0]
                # 扩展排除字段列表，包含所有标识和元数据字段
                exclude_fields = [
                    'stock_code', 'date_str', 'date', 'future_return_2d',
                    'most_relevant_sector',  # 板块标识，不应作为特征
                    'sector_correlation_score',  # 相关性得分，不应作为特征
                    'is_sector_leader',  # 领导地位，可能在子集中全零
                    'change_point_idx'  # 变化点索引
                ]
                key_features = [k for k in sample_pattern.keys()
                              if k not in exclude_fields and not k.startswith('_')]

                # 进一步过滤：移除可能在子集中全零的技术形态特征
                pattern_features_to_exclude = [
                    'pattern_morning_star', 'pattern_evening_star',
                    'pattern_three_white_soldiers', 'pattern_three_black_crows',
                    'pattern_hanging_man', 'pattern_shooting_star',
                    'pattern_engulfing_bullish', 'pattern_engulfing_bearish',
                    'trend_state', 'stock_state'
                ]
                key_features = [k for k in key_features if k not in pattern_features_to_exclude]

                self.logger.info(f"过滤后的关键特征数量: {len(key_features)}")
                self.logger.info(f"关键特征列表: {key_features[:10]}{'...' if len(key_features) > 10 else ''}")
            else:
                key_features = ['pct_chg', 'vol', 'turnover_rate', 'amplitude']

            # 预处理：将历史模式转换为特征矩阵（避免重复计算）
            self.logger.info("预处理历史模式特征矩阵...")
            positive_matrix, positive_features = self._patterns_to_matrix(positive_patterns, key_features)
            negative_matrix, negative_features = self._patterns_to_matrix(negative_patterns, key_features)

            # 使用相同的特征集（取交集）
            if len(positive_features) > 0 and len(negative_features) > 0:
                common_features = list(set(positive_features) & set(negative_features))
                if len(common_features) < len(key_features) * 0.5:  # 如果交集太小，使用原始特征
                    self.logger.warning(f"正负模式特征交集太小({len(common_features)})，使用原始特征集")
                    # 重新提取特征矩阵，不过滤特征
                    positive_matrix = self._patterns_to_matrix_no_filter(positive_patterns, key_features)
                    negative_matrix = self._patterns_to_matrix_no_filter(negative_patterns, key_features)
                else:
                    self.logger.info(f"使用共同特征集: {len(common_features)} 个特征")
                    key_features = common_features

            # 添加调试信息：检查转换后的矩阵
            self.logger.info(f"正向模式矩阵形状: {positive_matrix.shape}")
            self.logger.info(f"负向模式矩阵形状: {negative_matrix.shape}")

            if len(positive_matrix) > 0:
                self.logger.info(f"正向模式矩阵示例（前3行）: {positive_matrix[:3]}")
                # 检查是否有全零特征
                zero_features = []
                for i, feature_name in enumerate(key_features):
                    if np.all(positive_matrix[:, i] == 0):
                        zero_features.append(feature_name)
                if zero_features:
                    self.logger.warning(f"正向模式中全零特征: {zero_features}")

            if len(negative_matrix) > 0:
                self.logger.info(f"负向模式矩阵示例（前3行）: {negative_matrix[:3]}")
                # 检查是否有全零特征
                zero_features = []
                for i, feature_name in enumerate(key_features):
                    if np.all(negative_matrix[:, i] == 0):
                        zero_features.append(feature_name)
                if zero_features:
                    self.logger.warning(f"负向模式中全零特征: {zero_features}")

            # 批量提取测试样本特征矩阵
            self.logger.info("批量提取测试样本特征...")
            test_matrix, stock_codes, dates = self._extract_test_features_matrix(predict_features, key_features)

            if len(test_matrix) == 0:
                self.logger.warning("没有有效的测试样本")
                return {}

            # 检查内存需求并决定是否使用分批处理
            memory_needed_gb = (len(test_matrix) * max(len(positive_matrix), len(negative_matrix)) * 8) / (1024**3)  # 8字节per float64
            max_memory_gb = 8.0  # 最大使用8GB内存

            self.logger.info(f"内存需求评估: {memory_needed_gb:.1f} GB")

            if memory_needed_gb > max_memory_gb:
                self.logger.info(f"内存需求过大，使用分批处理...")
                positive_similarities = self._batch_cosine_similarity_chunked(test_matrix, positive_matrix) if len(positive_matrix) > 0 else np.zeros((len(test_matrix), 1))
                negative_similarities = self._batch_cosine_similarity_chunked(test_matrix, negative_matrix) if len(negative_matrix) > 0 else np.zeros((len(test_matrix), 1))
            else:
                self.logger.info(f"批量计算相似度，测试样本: {len(test_matrix)}, 正向模式: {len(positive_matrix)}, 负向模式: {len(negative_matrix)}")
                positive_similarities = self._batch_cosine_similarity(test_matrix, positive_matrix) if len(positive_matrix) > 0 else np.zeros((len(test_matrix), 1))
                negative_similarities = self._batch_cosine_similarity(test_matrix, negative_matrix) if len(negative_matrix) > 0 else np.zeros((len(test_matrix), 1))

            # 计算平均相似度
            avg_positive_sim = np.mean(positive_similarities, axis=1) if positive_similarities.shape[1] > 0 else np.zeros(len(test_matrix))
            avg_negative_sim = np.mean(negative_similarities, axis=1) if negative_similarities.shape[1] > 0 else np.zeros(len(test_matrix))

            # 计算预测得分
            prediction_scores = avg_positive_sim - avg_negative_sim

            # 批量构建预测结果
            for i in range(len(test_matrix)):
                stock_code = stock_codes[i]
                date = dates[i]

                # 使用 (stock_code, date) 作为键，避免同一股票不同日期的数据被覆盖
                key = f"{stock_code}_{date}"
                predictions[key] = {
                    'stock_code': stock_code,
                    'date': date,
                    'prediction_score': float(prediction_scores[i]),
                    'positive_similarity': float(avg_positive_sim[i]),
                    'negative_similarity': float(avg_negative_sim[i]),
                    'prediction_binary': 1 if prediction_scores[i] > 0 else 0,
                    'method': 'similarity_matching_optimized'
                }

            elapsed_time = time.time() - start_time
            self.logger.info(f"完成相似度匹配预测，共预测 {len(predictions)} 只股票，用时 {elapsed_time:.2f} 秒")

        except Exception as e:
            self.logger.error(f"相似度匹配预测失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return {}

        return predictions

    def _predict_with_batch_processing(self, predict_features, feature_patterns):
        """
        使用分批处理策略进行大规模数据预测

        Args:
            predict_features: 预测特征DataFrame
            feature_patterns: 特征模式字典

        Returns:
            dict: 预测结果字典
        """
        import time
        start_time = time.time()

        total_samples = len(predict_features)
        batch_size = 50000  # 每批处理5万样本
        all_predictions = {}

        self.logger.info(f"开始分批处理 {total_samples} 个样本，批大小: {batch_size}")

        try:
            # 分批处理
            for i in range(0, total_samples, batch_size):
                batch_end = min(i + batch_size, total_samples)
                batch_features = predict_features.iloc[i:batch_end].copy()

                self.logger.info(f"处理第 {i//batch_size + 1} 批，样本范围: {i} - {batch_end}")

                # 对每批使用传统的相似度匹配
                batch_predictions = self._predict_single_batch(batch_features, feature_patterns)

                # 合并结果
                all_predictions.update(batch_predictions)

                # 内存清理
                del batch_features
                import gc
                gc.collect()

                self.logger.info(f"第 {i//batch_size + 1} 批处理完成，预测 {len(batch_predictions)} 只股票")

            elapsed_time = time.time() - start_time
            self.logger.info(f"分批处理完成，共预测 {len(all_predictions)} 只股票，用时 {elapsed_time:.2f} 秒")

        except Exception as e:
            self.logger.error(f"分批处理失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return {}

        return all_predictions

    def _predict_single_batch(self, batch_features, feature_patterns):
        """处理单个批次的预测"""
        # 获取历史模式 - 修复数据结构问题
        positive_patterns = []
        negative_patterns = []

        # 尝试从pattern_analysis获取
        if 'pattern_analysis' in feature_patterns:
            pattern_analysis = feature_patterns['pattern_analysis']
            positive_patterns = pattern_analysis.get('positive_patterns', [])
            negative_patterns = pattern_analysis.get('negative_patterns', [])

        # 如果没有数据，尝试直接获取
        if not positive_patterns and not negative_patterns:
            positive_patterns = feature_patterns.get('positive_patterns', [])
            negative_patterns = feature_patterns.get('negative_patterns', [])

        if not positive_patterns and not negative_patterns:
            self.logger.warning("批次处理：没有历史模式可用")
            return {}

        self.logger.info(f"批次处理：获取到正向模式 {len(positive_patterns)}, 负向模式 {len(negative_patterns)}")

        # 使用现有的相似度匹配逻辑，但针对小批量优化
        return self._calculate_batch_similarity(batch_features, positive_patterns, negative_patterns)

    def _calculate_batch_similarity(self, predict_features, positive_patterns, negative_patterns):
        """计算批次相似度，内存优化版本"""
        predictions = {}

        if predict_features.empty:
            return predictions

        try:
            # 获取特征列表（使用一致的过滤逻辑）
            if positive_patterns or negative_patterns:
                sample_pattern = positive_patterns[0] if positive_patterns else negative_patterns[0]
                exclude_fields = [
                    'stock_code', 'date_str', 'date', 'future_return_2d',
                    'most_relevant_sector', 'sector_correlation_score',
                    'is_sector_leader', 'change_point_idx'
                ]
                key_features = [k for k in sample_pattern.keys()
                              if k not in exclude_fields and not k.startswith('_')]

                pattern_features_to_exclude = [
                    'pattern_morning_star', 'pattern_evening_star',
                    'pattern_three_white_soldiers', 'pattern_three_black_crows',
                    'pattern_hanging_man', 'pattern_shooting_star',
                    'pattern_engulfing_bullish', 'pattern_engulfing_bearish',
                    'trend_state', 'stock_state'
                ]
                key_features = [k for k in key_features if k not in pattern_features_to_exclude]
            else:
                key_features = ['pct_chg', 'vol', 'turnover_rate', 'amplitude']

            # 转换历史模式为特征矩阵
            positive_matrix, positive_features = self._patterns_to_matrix(positive_patterns, key_features)
            negative_matrix, negative_features = self._patterns_to_matrix(negative_patterns, key_features)

            # 确定最终使用的特征集（确保一致性）
            final_features = key_features
            if len(positive_features) > 0 and len(negative_features) > 0:
                common_features = list(set(positive_features) & set(negative_features))
                if len(common_features) >= len(key_features) * 0.5:
                    final_features = common_features
                    self.logger.info(f"批次处理使用共同特征集: {len(final_features)} 个特征")
                    # 重新提取特征矩阵以确保一致性
                    positive_matrix, _ = self._patterns_to_matrix(positive_patterns, final_features)
                    negative_matrix, _ = self._patterns_to_matrix(negative_patterns, final_features)

            # 提取测试样本特征矩阵（使用相同的特征集）
            test_matrix, stock_codes, dates = self._extract_test_features_matrix(predict_features, final_features)

            # 验证特征维度一致性
            if len(test_matrix) > 0:
                test_features = test_matrix.shape[1]
                pos_features = positive_matrix.shape[1] if len(positive_matrix) > 0 else 0
                neg_features = negative_matrix.shape[1] if len(negative_matrix) > 0 else 0

                self.logger.info(f"批次特征维度验证: 测试={test_features}, 正向={pos_features}, 负向={neg_features}")

                if pos_features > 0 and test_features != pos_features:
                    self.logger.error(f"批次正向模式特征维度不匹配: {test_features} != {pos_features}")
                    return {}
                if neg_features > 0 and test_features != neg_features:
                    self.logger.error(f"批次负向模式特征维度不匹配: {test_features} != {neg_features}")
                    return {}

            if len(test_matrix) == 0:
                return predictions

            # 强制使用分块计算避免内存问题
            chunk_size = min(1000, len(test_matrix))  # 更小的块大小
            positive_similarities = self._batch_cosine_similarity_chunked(test_matrix, positive_matrix, chunk_size=chunk_size) if len(positive_matrix) > 0 else np.zeros((len(test_matrix), 1))
            negative_similarities = self._batch_cosine_similarity_chunked(test_matrix, negative_matrix, chunk_size=chunk_size) if len(negative_matrix) > 0 else np.zeros((len(test_matrix), 1))

            # 计算平均相似度
            avg_positive_sim = np.mean(positive_similarities, axis=1) if positive_similarities.shape[1] > 0 else np.zeros(len(test_matrix))
            avg_negative_sim = np.mean(negative_similarities, axis=1) if negative_similarities.shape[1] > 0 else np.zeros(len(test_matrix))

            # 修复预测得分计算 - 使用实际历史收益率而非硬编码值
            prediction_scores = np.zeros(len(avg_positive_sim))

            # 提取历史模式的收益率数据
            positive_returns_list = []
            negative_returns_list = []

            # 从正向模式中提取收益率
            for pattern in positive_patterns:
                future_return = pattern.get('future_return_2d', 0)
                if isinstance(future_return, (int, float)) and not pd.isna(future_return):
                    positive_returns_list.append(future_return)
                else:
                    positive_returns_list.append(0)

            # 从负向模式中提取收益率
            for pattern in negative_patterns:
                future_return = pattern.get('future_return_2d', 0)
                if isinstance(future_return, (int, float)) and not pd.isna(future_return):
                    negative_returns_list.append(future_return)
                else:
                    negative_returns_list.append(0)

            for i in range(len(prediction_scores)):
                pos_sim = avg_positive_sim[i]
                neg_sim = avg_negative_sim[i]

                # 降低相似度阈值，增加有效预测的数量
                pos_threshold = 0.3  # 从0.5降低到0.3
                neg_threshold = 0.3

                expected_return = 0.0
                total_weight = 0.0

                # 处理正向相似度
                if pos_sim > pos_threshold and len(positive_similarities) > 0 and len(positive_returns_list) > 0:
                    # 使用平均收益率加权，并放大相似度的影响
                    avg_positive_return = np.mean(positive_returns_list)
                    # 放大相似度权重，使其更有区分度
                    amplified_pos_sim = pos_sim ** 0.5  # 开平方根，减少过度压缩
                    expected_return += avg_positive_return * amplified_pos_sim
                    total_weight += amplified_pos_sim

                # 处理负向相似度
                if neg_sim > neg_threshold and len(negative_similarities) > 0 and len(negative_returns_list) > 0:
                    # 使用平均收益率加权，并放大相似度的影响
                    avg_negative_return = np.mean(negative_returns_list)
                    # 放大相似度权重，使其更有区分度
                    amplified_neg_sim = neg_sim ** 0.5  # 开平方根，减少过度压缩
                    expected_return += avg_negative_return * amplified_neg_sim
                    total_weight += amplified_neg_sim

                # 计算最终预测得分
                if total_weight > 0:
                    prediction_scores[i] = expected_return / total_weight
                else:
                    # 如果没有足够的相似模式，使用简单的相对优势
                    if pos_sim + neg_sim > 0:
                        relative_advantage = (pos_sim - neg_sim) / (pos_sim + neg_sim + 0.01)  # 添加小值避免除零
                        # 使用更大的基础收益率，确保有区分度
                        base_return = 0.03 if len(positive_returns_list) > 0 or len(negative_returns_list) > 0 else 0.02
                        prediction_scores[i] = relative_advantage * base_return
                    else:
                        prediction_scores[i] = 0

            # 添加最终放大因子，确保有足够的区分度
            amplification_factor = 3.0  # 放大3倍
            prediction_scores = prediction_scores * amplification_factor

            # 确保预测得分在合理范围内 [-8%, +8%]
            prediction_scores = np.clip(prediction_scores, -0.08, 0.08)

            # 详细的调试信息
            self.logger.info(f"历史收益率统计:")
            if len(positive_returns_list) > 0:
                self.logger.info(f"  - 正向收益率: 平均={np.mean(positive_returns_list):.6f}, 范围=[{min(positive_returns_list):.6f}, {max(positive_returns_list):.6f}]")
            if len(negative_returns_list) > 0:
                self.logger.info(f"  - 负向收益率: 平均={np.mean(negative_returns_list):.6f}, 范围=[{min(negative_returns_list):.6f}, {max(negative_returns_list):.6f}]")

            self.logger.info(f"相似度得分统计: 正向={avg_positive_sim.mean():.6f}, 负向={avg_negative_sim.mean():.6f}")
            self.logger.info(f"相似度阈值: 正向={pos_threshold}, 负向={neg_threshold}")
            self.logger.info(f"有效相似度数量: 正向={sum(avg_positive_sim > pos_threshold)}, 负向={sum(avg_negative_sim > neg_threshold)}")
            self.logger.info(f"预测得分统计: 范围=[{prediction_scores.min():.6f}, {prediction_scores.max():.6f}], 平均={prediction_scores.mean():.6f}")
            self.logger.info(f"预测得分标准差: {prediction_scores.std():.6f}")
            self.logger.info(f"预测为正向的比例: {(prediction_scores > 0).mean():.2%}")
            self.logger.info(f"非零预测得分数量: {sum(abs(prediction_scores) > 0.001)}/{len(prediction_scores)}")

            # 构建预测结果
            for i in range(len(test_matrix)):
                stock_code = stock_codes[i]
                date = dates[i]

                key = f"{stock_code}_{date}"
                predictions[key] = {
                    'stock_code': stock_code,
                    'date': date,
                    'prediction_score': float(prediction_scores[i]),
                    'positive_similarity': float(avg_positive_sim[i]),
                    'negative_similarity': float(avg_negative_sim[i]),
                    'prediction_binary': 1 if prediction_scores[i] > 0 else 0,
                    'method': 'batch_similarity_matching'
                }

        except Exception as e:
            self.logger.error(f"批次相似度计算失败: {e}")
            return {}

        return predictions

    def _predict_with_sector_specific_matching(self, predict_features, feature_patterns):
        """
        使用板块特定的相似度匹配进行预测

        Args:
            predict_features: 预测特征DataFrame
            feature_patterns: 包含板块分层模式分析结果的字典

        Returns:
            dict: 预测结果字典
        """
        import time
        start_time = time.time()

        predictions = {}
        sector_analysis = feature_patterns.get('sector_analysis', {})

        if not sector_analysis:
            self.logger.warning("没有板块分层的模式分析结果")
            return {}

        self.logger.info(f"开始板块特定的相似度匹配，共 {len(predict_features)} 个预测样本")

        try:
            # 按板块分组预测样本
            predict_by_sector = {}
            for _, row in predict_features.iterrows():
                sector = row.get('most_relevant_sector', 'unknown')
                if sector not in predict_by_sector:
                    predict_by_sector[sector] = []
                predict_by_sector[sector].append(row)

            self.logger.info(f"预测样本分布在 {len(predict_by_sector)} 个板块中")

            # 对每个板块进行预测
            for sector, sector_samples in predict_by_sector.items():
                self.logger.info(f"处理板块 {sector}，样本数: {len(sector_samples)}")

                # 获取该板块的历史模式
                sector_patterns = sector_analysis.get(sector)

                # 如果该板块没有足够的历史模式，使用全市场回退模式
                if not sector_patterns or sector_patterns.get('total_samples', 0) < 50:
                    self.logger.info(f"板块 {sector} 历史模式不足，使用全市场回退模式")
                    sector_patterns = sector_analysis.get('_global_fallback')

                    if not sector_patterns:
                        self.logger.warning(f"板块 {sector} 无可用模式，跳过")
                        continue

                # 获取该板块的正向和负向模式
                positive_patterns = sector_patterns.get('positive_patterns', [])
                negative_patterns = sector_patterns.get('negative_patterns', [])

                if not positive_patterns and not negative_patterns:
                    self.logger.warning(f"板块 {sector} 没有有效模式")
                    continue

                # 转换为DataFrame进行批量处理
                sector_df = pd.DataFrame(sector_samples)

                # 使用现有的相似度匹配逻辑
                sector_predictions = self._calculate_sector_similarity(
                    sector_df, positive_patterns, negative_patterns, sector
                )

                # 合并预测结果
                predictions.update(sector_predictions)

            elapsed_time = time.time() - start_time
            self.logger.info(f"板块特定相似度匹配完成，共预测 {len(predictions)} 只股票，用时 {elapsed_time:.2f} 秒")

        except Exception as e:
            self.logger.error(f"板块特定相似度匹配失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return {}

        return predictions

    def _calculate_sector_similarity(self, predict_features, positive_patterns, negative_patterns, sector_name):
        """计算单个板块的相似度匹配"""
        predictions = {}

        if predict_features.empty:
            return predictions

        try:
            # 获取特征列表，使用与主方法一致的过滤逻辑
            if positive_patterns or negative_patterns:
                sample_pattern = positive_patterns[0] if positive_patterns else negative_patterns[0]
                # 使用与主方法一致的排除字段列表
                exclude_fields = [
                    'stock_code', 'date_str', 'date', 'future_return_2d',
                    'most_relevant_sector',  # 板块标识，不应作为特征
                    'sector_correlation_score',  # 相关性得分，不应作为特征
                    'is_sector_leader',  # 领导地位，可能在子集中全零
                    'change_point_idx'  # 变化点索引
                ]
                key_features = [k for k in sample_pattern.keys()
                              if k not in exclude_fields and not k.startswith('_')]

                # 进一步过滤：移除可能在子集中全零的技术形态特征
                pattern_features_to_exclude = [
                    'pattern_morning_star', 'pattern_evening_star',
                    'pattern_three_white_soldiers', 'pattern_three_black_crows',
                    'pattern_hanging_man', 'pattern_shooting_star',
                    'pattern_engulfing_bullish', 'pattern_engulfing_bearish',
                    'trend_state', 'stock_state'
                ]
                key_features = [k for k in key_features if k not in pattern_features_to_exclude]

                self.logger.info(f"板块 {sector_name} 过滤后的特征数量: {len(key_features)}")
            else:
                key_features = ['pct_chg', 'vol', 'turnover_rate', 'amplitude']

            # 转换历史模式为特征矩阵
            positive_matrix, positive_features = self._patterns_to_matrix(positive_patterns, key_features)
            negative_matrix, negative_features = self._patterns_to_matrix(negative_patterns, key_features)

            # 确定最终使用的特征集（确保一致性）
            final_features = key_features
            if len(positive_features) > 0 and len(negative_features) > 0:
                common_features = list(set(positive_features) & set(negative_features))
                if len(common_features) >= len(key_features) * 0.5:
                    final_features = common_features
                    self.logger.info(f"使用共同特征集: {len(final_features)} 个特征")
                    # 重新提取特征矩阵以确保一致性
                    positive_matrix, _ = self._patterns_to_matrix(positive_patterns, final_features)
                    negative_matrix, _ = self._patterns_to_matrix(negative_patterns, final_features)

            # 提取测试样本特征矩阵（使用相同的特征集）
            test_matrix, stock_codes, dates = self._extract_test_features_matrix(predict_features, final_features)

            # 验证特征维度一致性
            if len(test_matrix) > 0:
                test_features = test_matrix.shape[1]
                pos_features = positive_matrix.shape[1] if len(positive_matrix) > 0 else 0
                neg_features = negative_matrix.shape[1] if len(negative_matrix) > 0 else 0

                self.logger.info(f"特征维度验证: 测试={test_features}, 正向={pos_features}, 负向={neg_features}")

                if pos_features > 0 and test_features != pos_features:
                    self.logger.error(f"正向模式特征维度不匹配: {test_features} != {pos_features}")
                    return {}
                if neg_features > 0 and test_features != neg_features:
                    self.logger.error(f"负向模式特征维度不匹配: {test_features} != {neg_features}")
                    return {}

            if len(test_matrix) == 0:
                return predictions

            # 计算相似度（使用内存优化的方法）
            memory_needed_gb = (len(test_matrix) * max(len(positive_matrix), len(negative_matrix)) * 8) / (1024**3)

            if memory_needed_gb > 2.0:  # 板块级别使用更小的内存阈值
                positive_similarities = self._batch_cosine_similarity_chunked(test_matrix, positive_matrix, chunk_size=500) if len(positive_matrix) > 0 else np.zeros((len(test_matrix), 1))
                negative_similarities = self._batch_cosine_similarity_chunked(test_matrix, negative_matrix, chunk_size=500) if len(negative_matrix) > 0 else np.zeros((len(test_matrix), 1))
            else:
                positive_similarities = self._batch_cosine_similarity(test_matrix, positive_matrix) if len(positive_matrix) > 0 else np.zeros((len(test_matrix), 1))
                negative_similarities = self._batch_cosine_similarity(test_matrix, negative_matrix) if len(negative_matrix) > 0 else np.zeros((len(test_matrix), 1))

            # 计算平均相似度
            avg_positive_sim = np.mean(positive_similarities, axis=1) if positive_similarities.shape[1] > 0 else np.zeros(len(test_matrix))
            avg_negative_sim = np.mean(negative_similarities, axis=1) if negative_similarities.shape[1] > 0 else np.zeros(len(test_matrix))

            # 修复预测得分计算 - 使用相对差值并放大到合理范围
            # 计算相对优势：(正向-负向)/(正向+负向) * 历史变化点平均收益
            denominator = avg_positive_sim + avg_negative_sim
            relative_advantage = np.where(denominator > 0,
                                        (avg_positive_sim - avg_negative_sim) / denominator,
                                        0)

            # 基于历史变化点的平均收益来缩放预测得分
            # 假设历史变化点平均收益约为5%，将相对优势映射到合理的预测收益范围
            historical_avg_return = 0.05  # 5%的平均变化点收益
            prediction_scores = relative_advantage * historical_avg_return

            # 确保预测得分在合理范围内 [-10%, +10%]
            prediction_scores = np.clip(prediction_scores, -0.1, 0.1)

            self.logger.info(f"板块 {sector_name} 相似度得分统计: 正向={avg_positive_sim.mean():.6f}, 负向={avg_negative_sim.mean():.6f}")
            self.logger.info(f"板块 {sector_name} 预测得分统计: 范围=[{prediction_scores.min():.4f}, {prediction_scores.max():.4f}], 平均={prediction_scores.mean():.4f}")
            self.logger.info(f"板块 {sector_name} 预测为正向的比例: {(prediction_scores > 0).mean():.2%}")

            # 构建预测结果
            for i in range(len(test_matrix)):
                stock_code = stock_codes[i]
                date = dates[i]

                key = f"{stock_code}_{date}"
                predictions[key] = {
                    'stock_code': stock_code,
                    'date': date,
                    'prediction_score': float(prediction_scores[i]),
                    'positive_similarity': float(avg_positive_sim[i]),
                    'negative_similarity': float(avg_negative_sim[i]),
                    'prediction_binary': 1 if prediction_scores[i] > 0 else 0,
                    'method': f'sector_specific_matching_{sector_name}',
                    'sector': sector_name
                }

            self.logger.info(f"板块 {sector_name} 完成相似度计算，预测 {len(predictions)} 只股票")

        except Exception as e:
            self.logger.error(f"板块 {sector_name} 相似度计算失败: {e}")
            return {}

        return predictions

    def _get_consistent_feature_list(self, data_df):
        """
        获取与训练阶段一致的特征列表

        Args:
            data_df: 数据DataFrame

        Returns:
            list: 特征列表
        """
        # 定义统一的排除字段列表
        exclude_fields = [
            'stock_code', 'date_str', 'date', 'future_return_2d',
            'most_relevant_sector',  # 板块标识，不应作为特征
            'sector_correlation_score',  # 相关性得分，不应作为特征
            'is_sector_leader',  # 领导地位，可能在子集中全零
            'change_point_idx'  # 变化点索引
        ]

        # 定义需要过滤的技术形态特征
        pattern_features_to_exclude = [
            'pattern_morning_star', 'pattern_evening_star',
            'pattern_three_white_soldiers', 'pattern_three_black_crows',
            'pattern_hanging_man', 'pattern_shooting_star',
            'pattern_engulfing_bullish', 'pattern_engulfing_bearish',
            'trend_state', 'stock_state'
        ]

        # 获取所有可用的数值特征
        available_features = []
        for col in data_df.columns:
            if col not in exclude_fields and not col.startswith('_'):
                # 检查是否为数值型特征
                try:
                    # 测试前100行是否可以转换为数值
                    test_data = data_df[col].iloc[:min(100, len(data_df))]
                    pd.to_numeric(test_data, errors='coerce')
                    available_features.append(col)
                except:
                    continue

        # 进一步过滤技术形态特征
        key_features = [f for f in available_features if f not in pattern_features_to_exclude]

        # 按字母顺序排序，确保一致性
        key_features.sort()

        self.logger.info(f"一致性特征提取: 总列数={len(data_df.columns)}, 可用特征={len(available_features)}, 最终特征={len(key_features)}")

        return key_features

    def _patterns_to_matrix(self, patterns, key_features):
        """将模式列表转换为特征矩阵，并过滤全零特征，正确处理数值编码"""
        if not patterns:
            return np.array([]), []

        # 创建板块编码映射
        sector_encoder = {}
        sector_counter = 1

        # 首先构建完整矩阵，正确处理不同类型的特征
        matrix = []
        for pattern in patterns:
            row = []
            for feature in key_features:
                value = pattern.get(feature, 0)

                # 特殊处理不同类型的特征
                if feature == 'most_relevant_sector':
                    # 板块代码编码为数值
                    if isinstance(value, str) and value:
                        if value not in sector_encoder:
                            sector_encoder[value] = sector_counter
                            sector_counter += 1
                        row.append(float(sector_encoder[value]))
                    else:
                        row.append(0.0)
                elif feature == 'is_sector_leader':
                    # 布尔值转换为0/1
                    if isinstance(value, bool):
                        row.append(1.0 if value else 0.0)
                    elif isinstance(value, (int, float)):
                        row.append(1.0 if value > 0.5 else 0.0)
                    else:
                        row.append(0.0)
                else:
                    # 其他数值特征
                    try:
                        numeric_value = float(value)
                        # 检查是否为有效数值
                        if np.isfinite(numeric_value):
                            row.append(numeric_value)
                        else:
                            row.append(0.0)
                    except (ValueError, TypeError):
                        row.append(0.0)
            matrix.append(row)

        matrix = np.array(matrix)

        # 识别并移除全零特征列
        if len(matrix) > 0:
            # 计算每列的标准差，标准差为0的列是全零或全相同的列
            col_stds = np.std(matrix, axis=0)
            valid_cols = col_stds > 1e-8  # 保留有变化的特征

            # 记录被移除的特征
            removed_features = [key_features[i] for i, valid in enumerate(valid_cols) if not valid]
            if removed_features:
                self.logger.info(f"移除全零或无变化特征: {removed_features[:10]}{'...' if len(removed_features) > 10 else ''} (共{len(removed_features)}个)")

            # 过滤矩阵和特征名
            filtered_matrix = matrix[:, valid_cols]
            filtered_features = [key_features[i] for i, valid in enumerate(valid_cols) if valid]

            self.logger.info(f"特征过滤结果: {len(key_features)} -> {len(filtered_features)} 个有效特征")

            # 保存编码映射以供后续使用
            if hasattr(self, '_sector_encoder'):
                self._sector_encoder.update(sector_encoder)
            else:
                self._sector_encoder = sector_encoder

            return filtered_matrix, filtered_features

        return matrix, key_features

    def _patterns_to_matrix_no_filter(self, patterns, key_features):
        """将模式列表转换为特征矩阵，不过滤特征，但正确处理数值编码"""
        if not patterns:
            return np.array([])

        # 获取或创建板块编码映射
        if not hasattr(self, '_sector_encoder'):
            self._sector_encoder = {}

        sector_counter = len(self._sector_encoder) + 1

        matrix = []
        for pattern in patterns:
            row = []
            for feature in key_features:
                value = pattern.get(feature, 0)

                # 特殊处理不同类型的特征
                if feature == 'most_relevant_sector':
                    # 板块代码编码为数值
                    if isinstance(value, str) and value:
                        if value not in self._sector_encoder:
                            self._sector_encoder[value] = sector_counter
                            sector_counter += 1
                        row.append(float(self._sector_encoder[value]))
                    else:
                        row.append(0.0)
                elif feature == 'is_sector_leader':
                    # 布尔值转换为0/1
                    if isinstance(value, bool):
                        row.append(1.0 if value else 0.0)
                    elif isinstance(value, (int, float)):
                        row.append(1.0 if value > 0.5 else 0.0)
                    else:
                        row.append(0.0)
                else:
                    # 其他数值特征
                    try:
                        numeric_value = float(value)
                        # 检查是否为有效数值
                        if np.isfinite(numeric_value):
                            row.append(numeric_value)
                        else:
                            row.append(0.0)
                    except (ValueError, TypeError):
                        row.append(0.0)
            matrix.append(row)

        return np.array(matrix)

    def _extract_test_features_matrix(self, predict_features, key_features):
        """批量提取测试样本特征矩阵，正确处理特殊特征"""
        if predict_features.empty:
            return np.array([]), [], []

        # 获取或创建板块编码映射
        if not hasattr(self, '_sector_encoder'):
            self._sector_encoder = {}

        # 批量提取特征
        matrix = []
        stock_codes = predict_features['stock_code'].values
        dates = predict_features['date_str'].values

        for feature in key_features:
            if feature in predict_features.columns:
                if feature == 'most_relevant_sector':
                    # 板块代码编码为数值
                    feature_values = []
                    for value in predict_features[feature]:
                        if isinstance(value, str) and value:
                            if value not in self._sector_encoder:
                                # 如果是新的板块代码，分配新的编号
                                self._sector_encoder[value] = len(self._sector_encoder) + 1
                            feature_values.append(float(self._sector_encoder[value]))
                        else:
                            feature_values.append(0.0)
                    matrix.append(np.array(feature_values))
                elif feature == 'is_sector_leader':
                    # 布尔值转换为0/1
                    feature_values = []
                    for value in predict_features[feature]:
                        if isinstance(value, bool):
                            feature_values.append(1.0 if value else 0.0)
                        elif isinstance(value, (int, float)):
                            feature_values.append(1.0 if value > 0.5 else 0.0)
                        else:
                            feature_values.append(0.0)
                    matrix.append(np.array(feature_values))
                else:
                    # 其他数值特征
                    feature_values = pd.to_numeric(predict_features[feature], errors='coerce').fillna(0)
                    matrix.append(feature_values.values)
            else:
                # 如果特征不存在，填充为0
                matrix.append(np.zeros(len(predict_features)))

        # 转置得到正确的形状 (n_samples, n_features)
        test_matrix = np.column_stack(matrix)

        return test_matrix, stock_codes, dates

    def _batch_cosine_similarity(self, test_matrix, pattern_matrix):
        """批量计算余弦相似度"""
        if len(pattern_matrix) == 0 or len(test_matrix) == 0:
            return np.array([])

        # 计算范数
        test_norms = np.linalg.norm(test_matrix, axis=1, keepdims=True)
        pattern_norms = np.linalg.norm(pattern_matrix, axis=1, keepdims=True)

        # 避免除零
        test_norms = np.where(test_norms == 0, 1e-8, test_norms)
        pattern_norms = np.where(pattern_norms == 0, 1e-8, pattern_norms)

        # 标准化 - 修复广播错误
        test_normalized = test_matrix / test_norms  # (n_test, n_features)
        pattern_normalized = pattern_matrix / pattern_norms  # (n_patterns, n_features)

        # 计算余弦相似度矩阵
        # test_normalized: (n_test, n_features) @ pattern_normalized.T: (n_features, n_patterns)
        # 结果: (n_test, n_patterns)
        similarities = np.dot(test_normalized, pattern_normalized.T)

        # 确保相似度在[-1, 1]范围内，然后转换到[0, 1]
        similarities = np.clip(similarities, -1, 1)
        # 将[-1, 1]映射到[0, 1]：(x + 1) / 2
        similarities = (similarities + 1) / 2

        return similarities

    def _batch_cosine_similarity_chunked(self, test_matrix, pattern_matrix, chunk_size=1000):
        """分批计算余弦相似度，避免内存溢出"""
        if len(pattern_matrix) == 0 or len(test_matrix) == 0:
            return np.array([])

        self.logger.info(f"使用分批处理，测试样本: {len(test_matrix)}, 模式: {len(pattern_matrix)}, 批次大小: {chunk_size}")

        # 预计算模式矩阵的标准化
        pattern_norms = np.linalg.norm(pattern_matrix, axis=1, keepdims=True)
        pattern_norms = np.where(pattern_norms == 0, 1e-8, pattern_norms)
        pattern_normalized = pattern_matrix / pattern_norms

        # 分批处理测试样本
        all_similarities = []
        num_chunks = (len(test_matrix) + chunk_size - 1) // chunk_size

        for i in range(num_chunks):
            start_idx = i * chunk_size
            end_idx = min((i + 1) * chunk_size, len(test_matrix))
            test_chunk = test_matrix[start_idx:end_idx]

            # 计算当前批次的相似度
            test_norms = np.linalg.norm(test_chunk, axis=1, keepdims=True)
            test_norms = np.where(test_norms == 0, 1e-8, test_norms)
            test_normalized = test_chunk / test_norms

            # 计算余弦相似度
            similarities = np.dot(test_normalized, pattern_normalized.T)
            similarities = np.clip(similarities, -1, 1)
            similarities = (similarities + 1) / 2  # 映射到[0, 1]

            all_similarities.append(similarities)

            if (i + 1) % 10 == 0:  # 每10个批次报告一次进度
                self.logger.info(f"已处理 {i + 1}/{num_chunks} 个批次")

        # 合并所有批次的结果
        final_similarities = np.vstack(all_similarities)
        self.logger.info(f"分批处理完成，结果形状: {final_similarities.shape}")

        return final_similarities

    def _analyze_pattern_features(self, patterns, pattern_type):
        """分析历史模式的特征分布"""
        if not patterns:
            return

        # 动态获取特征列表
        if patterns:
            sample_pattern = patterns[0]
            exclude_fields = ['stock_code', 'date_str', 'date', 'future_return_2d']
            key_features = [k for k in sample_pattern.keys()
                          if k not in exclude_fields and not k.startswith('_')]
        else:
            key_features = ['pct_chg', 'vol', 'turnover_rate', 'amplitude']

        for feature_name in key_features:
            values = []
            for pattern in patterns:
                value = pattern.get(feature_name, 0)
                try:
                    values.append(float(value))
                except (ValueError, TypeError):
                    values.append(0)

            if values:
                unique_count = len(set(values))
                std_dev = np.std(values)
                value_range = f"{min(values):.6f} - {max(values):.6f}"
                avg_value = np.mean(values)
                self.logger.info(f"{pattern_type}模式特征 {feature_name}: 唯一值={unique_count}, 均值={avg_value:.6f}, 标准差={std_dev:.6f}, 范围={value_range}")

        # 分析模式中的板块和股票分布
        sectors = [p.get('most_relevant_sector', 'unknown') for p in patterns]
        stocks = [p.get('stock_code', 'unknown') for p in patterns]
        future_returns = [p.get('future_return_2d', 0) for p in patterns]

        sector_counts = {}
        for sector in sectors:
            sector_counts[sector] = sector_counts.get(sector, 0) + 1

        self.logger.info(f"{pattern_type}模式板块分布: {dict(list(sector_counts.items())[:5])}")  # 显示前5个板块
        self.logger.info(f"{pattern_type}模式future_return范围: {min(future_returns):.6f} - {max(future_returns):.6f}")

    def _prepare_test_data_for_model(self, predict_features, relations_df):
        """
        为模型预测准备测试数据 - 高性能优化版本

        优化策略：
        1. 使用向量化操作替代逐行处理
        2. 避免昂贵的 row.to_dict() 操作
        3. 批量处理数据转换
        4. 减少内存分配
        """
        import time
        start_time = time.time()

        # 使用与训练阶段完全一致的特征提取逻辑
        if predict_features.empty:
            return np.array([]), []

        # 获取训练时保存的特征列表
        key_features = self._get_consistent_feature_list(predict_features)

        self.logger.info(f"使用一致的特征数量: {len(key_features)}")
        self.logger.info(f"特征列表: {key_features[:10]}{'...' if len(key_features) > 10 else ''}")

        if predict_features.empty:
            return np.array([]), []

        try:
            # 添加详细的特征分析日志
            self.logger.info("=== 特征数据分析 ===")
            for feature_name in key_features:
                if feature_name in predict_features.columns:
                    feature_values = pd.to_numeric(predict_features[feature_name], errors='coerce').fillna(0)
                    unique_values = len(feature_values.unique())
                    value_range = f"{feature_values.min():.6f} - {feature_values.max():.6f}"
                    std_dev = feature_values.std()
                    self.logger.info(f"特征 {feature_name}: 唯一值数={unique_values}, 范围={value_range}, 标准差={std_dev:.6f}")
                else:
                    self.logger.warning(f"特征 {feature_name} 不存在于数据中")

            # 批量处理特征提取
            X_list = []
            for feature_name in key_features:
                if feature_name in predict_features.columns:
                    # 向量化转换为数值型，无效值填充为0
                    feature_values = pd.to_numeric(predict_features[feature_name], errors='coerce').fillna(0)
                    X_list.append(feature_values.values)
                else:
                    # 如果特征不存在，填充为0
                    X_list.append(np.zeros(len(predict_features)))

            # 转置得到正确的形状 (n_samples, n_features)
            X_test = np.column_stack(X_list)

            # 分析特征矩阵的差异性
            self.logger.info("=== 特征矩阵分析 ===")
            for i, feature_name in enumerate(key_features):
                feature_column = X_test[:, i]
                unique_values = len(np.unique(feature_column))
                self.logger.info(f"特征矩阵列 {i} ({feature_name}): 唯一值数={unique_values}, 标准差={np.std(feature_column):.6f}")

            # 检查是否所有样本的特征向量都相同
            unique_rows = len(np.unique(X_test, axis=0))
            self.logger.info(f"特征矩阵: 总样本={len(X_test)}, 唯一特征向量数={unique_rows}")
            if unique_rows == 1:
                self.logger.error("严重问题：所有样本的特征向量完全相同！")
                self.logger.info(f"示例特征向量: {X_test[0]}")
            elif unique_rows < 10:
                self.logger.warning(f"特征向量差异性很低，只有 {unique_rows} 种不同的特征向量")

            # 批量构建stock_info，避免row.to_dict()
            stock_codes = predict_features['stock_code'].values
            dates = predict_features['date_str'].values

            # 只提取必要的特征字典，避免完整的to_dict()
            essential_features = ['stock_code', 'date_str'] + key_features
            available_features = [col for col in essential_features if col in predict_features.columns]

            # 使用向量化方式构建特征字典
            stock_info = []
            for i in range(len(predict_features)):
                # 只构建必要的特征字典
                features_dict = {}
                for col in available_features:
                    features_dict[col] = predict_features.iloc[i][col]

                stock_info.append((stock_codes[i], dates[i], features_dict))

            elapsed_time = time.time() - start_time
            self.logger.info(f"测试数据准备完成，共处理 {len(X_test)} 个样本，用时 {elapsed_time:.2f} 秒")

            return X_test, stock_info

        except Exception as e:
            self.logger.error(f"准备测试数据时出错: {e}")
            return np.array([]), []

    def _calculate_cosine_similarity(self, vec1, vec2):
        """计算两个向量的余弦相似度"""
        try:
            vec1 = np.array(vec1, dtype=float)
            vec2 = np.array(vec2, dtype=float)

            # 检查向量是否有效
            if len(vec1) != len(vec2) or len(vec1) == 0:
                return None

            # 计算余弦相似度
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)

            if norm1 == 0 or norm2 == 0:
                return 0

            similarity = dot_product / (norm1 * norm2)
            return float(similarity)

        except Exception:
            return None

    def _prepare_test_data_for_model_with_features(self, predict_features, relations_df, expected_features):
        """
        为模型预测准备测试数据，使用指定的特征列表确保一致性

        Args:
            predict_features: 预测特征DataFrame
            relations_df: 关联数据（未使用）
            expected_features: 期望的特征列表

        Returns:
            tuple: (特征矩阵, 股票信息)
        """
        if predict_features.empty:
            return np.array([]), []

        self.logger.info(f"使用指定特征准备测试数据，期望特征数量: {len(expected_features)}")

        try:
            # 批量提取特征
            matrix = []
            stock_codes = predict_features['stock_code'].values
            dates = predict_features['date_str'].values

            self.logger.info("=== 预测特征分析 ===")
            for feature_name in expected_features:
                if feature_name in predict_features.columns:
                    # 向量化转换为数值型，无效值填充为0
                    feature_values = pd.to_numeric(predict_features[feature_name], errors='coerce').fillna(0)
                    matrix.append(feature_values.values)

                    # 分析特征分布
                    unique_count = len(feature_values.unique())
                    std_dev = feature_values.std()
                    value_range = f"{feature_values.min():.6f} - {feature_values.max():.6f}"
                    self.logger.info(f"预测特征 {feature_name}: 唯一值数={unique_count}, 范围={value_range}, 标准差={std_dev:.6f}")
                else:
                    # 如果特征不存在，填充为0
                    matrix.append(np.zeros(len(predict_features)))
                    self.logger.warning(f"预测特征 {feature_name} 不存在于数据中，填充为0")

            # 转置得到正确的形状 (n_samples, n_features)
            test_matrix = np.column_stack(matrix)

            # 构建股票信息
            stock_info = []
            for i in range(len(stock_codes)):
                stock_info.append({
                    'stock_code': stock_codes[i],
                    'date': dates[i]
                })

            self.logger.info(f"测试数据准备完成，共处理 {len(stock_info)} 个样本，特征维度: {test_matrix.shape}")

            return test_matrix, stock_info

        except Exception as e:
            self.logger.error(f"准备测试数据时出错: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return np.array([]), []

    def _rank_and_select_stocks(self, predictions, top_n=50, stock_features_df=None):
        """
        基于预测结果对股票进行排名和筛选 - 高性能优化版本

        Args:
            predictions: 预测结果字典
            top_n: 选择的股票数量
            stock_features_df: 股票特征数据，用于停牌检查

        Returns:
            dict: 排名和筛选结果，兼容旧格式
        """
        import time
        start_time = time.time()

        # 设置当前股票特征数据用于停牌检查
        if stock_features_df is not None:
            self._current_stock_features = stock_features_df
        self.logger.info(f"开始高性能排名和筛选 {len(predictions)} 只股票...")

        if not predictions:
            return {
                'selected_stocks': [],
                'selected_stocks_by_date': {},
                'dates': [],
                'total_stocks': 0,
                'date': datetime.now().strftime('%Y%m%d')
            }

        # 1. 向量化数据预处理
        processed_data = self._vectorized_data_preprocessing(predictions)

        # 2. 批量停牌检查
        valid_data = self._batch_suspended_stock_filtering(processed_data)

        # 3. 高性能排名和选择
        selected_results = self._high_performance_ranking(valid_data, top_n)

        processing_time = time.time() - start_time
        self.logger.info(f"高性能排名完成，耗时: {processing_time:.3f}秒，"
                        f"处理效率: {len(predictions)/processing_time:.0f} 股票/秒")

        return selected_results

    def _vectorized_data_preprocessing(self, predictions):
        """
        向量化数据预处理 - 批量转换预测数据格式

        Args:
            predictions: 原始预测结果字典

        Returns:
            DataFrame: 预处理后的数据
        """
        # 将字典转换为DataFrame以便向量化操作
        data_list = []

        for key, pred_data in predictions.items():
            stock_code = pred_data.get('stock_code')
            date = pred_data.get('date')

            if not date or not stock_code:
                continue

            # 提取关键字段
            prediction_method = pred_data.get('prediction_method', 'unknown')

            # 选择主要预测得分（向量化逻辑）
            if prediction_method == 'both_methods':
                ranking_score = pred_data.get('prediction_score', 0)
                backup_score = pred_data.get('model_prediction_score', 0)
            else:
                ranking_score = pred_data.get('prediction_score', 0)
                backup_score = 0

            # 计算兼容字段
            positive_similarity = pred_data.get('positive_similarity', 0.5)
            prediction_binary = pred_data.get('prediction_binary', 0)

            expected_return = ranking_score * 100  # 转换为百分比
            probability = positive_similarity
            confidence = 0.8 if prediction_binary == 1 else 0.3
            score = expected_return * probability * confidence

            data_list.append({
                'key': key,
                'stock_code': stock_code,
                'date': date,
                'ranking_score': ranking_score,
                'backup_score': backup_score,
                'prediction_method': prediction_method,
                'expected_return': expected_return,
                'probability': probability,
                'confidence': confidence,
                'score': score,
                'prediction_binary': prediction_binary,
                'positive_similarity': positive_similarity
            })

        df = pd.DataFrame(data_list)
        self.logger.info(f"向量化预处理完成，处理 {len(df)} 条记录")
        return df

    def _batch_suspended_stock_filtering(self, df):
        """
        批量停牌股票过滤 - 高性能版本

        Args:
            df: 预处理后的数据DataFrame

        Returns:
            DataFrame: 过滤后的数据
        """
        if df.empty:
            return df

        # 如果没有股票特征数据，跳过停牌检查
        if not hasattr(self, '_current_stock_features') or self._current_stock_features is None:
            self.logger.info("没有股票特征数据，跳过停牌检查")
            return df

        # 创建股票-日期的查找索引
        features_df = self._current_stock_features
        features_lookup = features_df.set_index(['stock_code', 'date_str'])

        # 向量化停牌检查
        def is_valid_stock_vectorized(row):
            try:
                key = (row['stock_code'], row['date'])
                if key not in features_lookup.index:
                    return False

                stock_data = features_lookup.loc[key]

                # 检查关键指标
                close_price = stock_data.get('close', 0)
                volume = stock_data.get('volume', 0)

                if pd.isna(close_price) or close_price <= 0:
                    return False
                if pd.isna(volume) or volume <= 0:
                    return False

                return True
            except:
                return False

        # 应用向量化检查
        valid_mask = df.apply(is_valid_stock_vectorized, axis=1)
        filtered_df = df[valid_mask].copy()

        filtered_count = len(df) - len(filtered_df)
        if filtered_count > 0:
            self.logger.info(f"批量过滤停牌股票: {filtered_count} 只")

        return filtered_df

    def _high_performance_ranking(self, df, top_n):
        """
        高性能排名和选择 - 使用pandas向量化操作

        Args:
            df: 有效的股票数据DataFrame
            top_n: 每日选择数量

        Returns:
            dict: 选择结果
        """
        if df.empty:
            return {
                'selected_stocks': [],
                'selected_stocks_by_date': {},
                'dates': [],
                'total_stocks': 0,
                'date': datetime.now().strftime('%Y%m%d')
            }

        # 按日期分组并排序（向量化操作）
        grouped = df.groupby('date')
        selected_stocks_by_date = {}
        total_selected = 0

        # 每天选择的股票数量
        daily_top_n = min(3, top_n)  # 每天最多选3只

        for date, group in grouped:
            # 高性能排序：使用pandas的sort_values
            # 多级排序：score降序，ranking_score降序，probability降序
            sorted_group = group.sort_values(
                ['score', 'ranking_score', 'probability'],
                ascending=[False, False, False]
            ).head(daily_top_n)

            # 添加排名
            sorted_group = sorted_group.copy()
            sorted_group['rank'] = range(1, len(sorted_group) + 1)

            # 转换为字典格式（保持兼容性）
            date_selected = []
            for _, row in sorted_group.iterrows():
                stock_dict = {
                    'stock_code': row['stock_code'],
                    'date': row['date'],
                    'ranking_score': row['ranking_score'],
                    'backup_score': row['backup_score'],
                    'prediction_method': row['prediction_method'],
                    'expected_return': row['expected_return'],
                    'probability': row['probability'],
                    'confidence': row['confidence'],
                    'score': row['score'],
                    'rank': row['rank'],
                    'prediction_binary': row['prediction_binary'],
                    'positive_similarity': row['positive_similarity']
                }
                date_selected.append(stock_dict)

            selected_stocks_by_date[date] = date_selected
            total_selected += len(date_selected)

            # 输出选择结果
            if date_selected:
                stock_codes = [s['stock_code'] for s in date_selected]
                scores = [f"{s['score']:.6f}" for s in date_selected]
                self.logger.info(f"日期 {date} 选中: {stock_codes}, 得分: {scores}")

        # 获取最新日期的选股
        dates = sorted(selected_stocks_by_date.keys())
        latest_date = dates[-1] if dates else datetime.now().strftime('%Y%m%d')
        selected_stocks = selected_stocks_by_date.get(latest_date, [])

        # 组织结果
        result = {
            'selected_stocks': selected_stocks,
            'selected_stocks_by_date': selected_stocks_by_date,
            'dates': dates,
            'total_stocks': len(df),
            'selected_count': total_selected,
            'selection_criteria': '高性能变化点检测+相似度匹配',
            'selection_timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'date': latest_date
        }

        self.logger.info(f"高性能选股完成: 处理 {len(dates)} 个交易日，选出 {total_selected} 只股票")

        return result

    def _rank_and_select_stocks_by_method(self, predictions, top_n, stock_features_df, method_name):
        """
        按指定方法对股票进行排名和选择

        Args:
            predictions: 预测结果字典
            top_n: 每日选择的股票数量
            stock_features_df: 股票特征数据
            method_name: 方法名称（用于标识）

        Returns:
            dict: 选股结果
        """
        self.logger.info(f"开始 {method_name} 方法的股票排名和选择...")

        # 设置当前股票特征数据用于停牌检查
        if stock_features_df is not None:
            self._current_stock_features = stock_features_df

        if not predictions:
            return {
                'method_name': method_name,
                'selected_stocks': [],
                'selected_stocks_by_date': {},
                'dates': [],
                'total_stocks': 0,
                'date': datetime.now().strftime('%Y%m%d')
            }

        # 1. 向量化数据预处理
        processed_data = self._vectorized_data_preprocessing_for_method(predictions, method_name)

        # 2. 批量停牌检查
        valid_data = self._batch_suspended_stock_filtering(processed_data)

        # 3. 高性能排名和选择
        selected_results = self._high_performance_ranking_for_method(valid_data, top_n, method_name)

        self.logger.info(f"{method_name} 方法选股完成")
        return selected_results

    def _vectorized_data_preprocessing_for_method(self, predictions, method_name):
        """
        为特定方法进行向量化数据预处理

        Args:
            predictions: 预测结果字典
            method_name: 方法名称

        Returns:
            DataFrame: 预处理后的数据
        """
        data_list = []

        for key, pred_data in predictions.items():
            stock_code = pred_data.get('stock_code')
            date = pred_data.get('date')

            if not date or not stock_code:
                continue

            # 统一预测得分的计量单位（转换为百分比）
            prediction_score = pred_data.get('prediction_score', 0)
            if abs(prediction_score) < 1:  # 如果是小数形式（绝对值小于1），转换为百分比
                prediction_score_pct = prediction_score * 100
            else:
                prediction_score_pct = prediction_score

            # 计算其他字段
            positive_similarity = pred_data.get('positive_similarity', 0.5)
            prediction_binary = pred_data.get('prediction_binary', 0)

            # 计算综合得分
            confidence = 0.8 if prediction_binary == 1 else 0.3
            score = prediction_score_pct * positive_similarity * confidence

            data_list.append({
                'key': key,
                'stock_code': stock_code,
                'date': date,
                'prediction_score_pct': prediction_score_pct,  # 百分比形式的预测得分
                'prediction_score_original': prediction_score,  # 原始预测得分
                'positive_similarity': positive_similarity,
                'prediction_binary': prediction_binary,
                'confidence': confidence,
                'score': score,
                'method_name': method_name
            })

        df = pd.DataFrame(data_list)
        self.logger.info(f"{method_name} 方法向量化预处理完成，处理 {len(df)} 条记录")
        return df

    def _high_performance_ranking_for_method(self, df, top_n, method_name):
        """
        为特定方法进行高性能排名和选择

        Args:
            df: 有效的股票数据DataFrame
            top_n: 每日选择数量
            method_name: 方法名称

        Returns:
            dict: 选择结果
        """
        if df.empty:
            return {
                'method_name': method_name,
                'selected_stocks': [],
                'selected_stocks_by_date': {},
                'dates': [],
                'total_stocks': 0,
                'date': datetime.now().strftime('%Y%m%d')
            }

        # 按日期分组并排序
        grouped = df.groupby('date')
        selected_stocks_by_date = {}
        total_selected = 0

        # 每天选择的股票数量
        daily_top_n = min(top_n, 10)  # 每天最多选10只

        for date, group in grouped:
            # 按预测得分排序
            sorted_group = group.sort_values(
                ['prediction_score_pct', 'score', 'positive_similarity'],
                ascending=[False, False, False]
            ).head(daily_top_n)

            # 添加排名
            sorted_group = sorted_group.copy()
            sorted_group['rank'] = range(1, len(sorted_group) + 1)

            # 转换为字典格式
            date_selected = []
            for _, row in sorted_group.iterrows():
                stock_dict = {
                    'stock_code': row['stock_code'],
                    'date': row['date'],
                    'predicted_return': row['prediction_score_pct'],  # 使用百分比形式
                    'prediction_score': row['prediction_score_original'],  # 保留原始得分用于兼容
                    'positive_similarity': row['positive_similarity'],
                    'prediction_binary': row['prediction_binary'],
                    'confidence': row['confidence'],
                    'score': row['score'],
                    'rank': row['rank'],
                    'method_name': method_name
                }
                date_selected.append(stock_dict)

            selected_stocks_by_date[date] = date_selected
            total_selected += len(date_selected)

            # 输出选择结果
            if date_selected:
                stock_codes = [s['stock_code'] for s in date_selected]
                scores = [f"{s['predicted_return']:.4f}%" for s in date_selected]
                self.logger.info(f"{method_name} 日期 {date} 选中: {stock_codes}, 预测收益: {scores}")

        # 获取最新日期的选股
        dates = sorted(selected_stocks_by_date.keys())
        latest_date = dates[-1] if dates else datetime.now().strftime('%Y%m%d')
        selected_stocks = selected_stocks_by_date.get(latest_date, [])

        # 组织结果
        result = {
            'method_name': method_name,
            'selected_stocks': selected_stocks,
            'selected_stocks_by_date': selected_stocks_by_date,
            'dates': dates,
            'total_stocks': len(df),
            'selected_count': total_selected,
            'selection_criteria': f'{method_name}_变化点检测',
            'selection_timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'date': latest_date
        }

        self.logger.info(f"{method_name} 选股完成: 处理 {len(dates)} 个交易日，选出 {total_selected} 只股票")

        return result

    def _combine_selection_results(self, similarity_selected, model_selected):
        """
        合并两种选股结果，用于兼容性

        Args:
            similarity_selected: 相似度匹配选股结果
            model_selected: 模型预测选股结果

        Returns:
            dict: 合并后的选股结果
        """
        # 优先使用相似度匹配结果，如果没有则使用模型预测结果
        if similarity_selected and similarity_selected.get('selected_stocks'):
            primary_result = similarity_selected
            secondary_result = model_selected
            primary_method = "similarity_matching"
        elif model_selected and model_selected.get('selected_stocks'):
            primary_result = model_selected
            secondary_result = similarity_selected
            primary_method = "trained_model"
        else:
            # 都没有结果，返回空结果
            return {
                'selected_stocks': [],
                'selected_stocks_by_date': {},
                'dates': [],
                'total_stocks': 0,
                'date': datetime.now().strftime('%Y%m%d'),
                'method_comparison': {
                    'similarity_selected': similarity_selected,
                    'model_selected': model_selected
                }
            }

        # 创建合并结果
        combined_result = primary_result.copy()
        combined_result['primary_method'] = primary_method
        combined_result['method_comparison'] = {
            'similarity_selected': similarity_selected,
            'model_selected': model_selected
        }

        self.logger.info(f"选股结果合并完成，主要方法: {primary_method}")
        return combined_result

    def _is_stock_suspended(self, stock_code, date):
        """
        检查股票在指定日期是否停牌

        Args:
            stock_code: 股票代码
            date: 日期字符串，格式为YYYYMMDD

        Returns:
            bool: True表示停牌，False表示正常交易
        """
        try:
            # 这里可以接入实际的停牌数据源
            # 目前使用简单的启发式方法：检查股票在该日期是否有基础数据

            # 如果有股票特征数据，可以检查该股票在该日期的数据
            if hasattr(self, '_current_stock_features') and self._current_stock_features is not None:
                stock_data = self._current_stock_features[
                    (self._current_stock_features['stock_code'] == stock_code) &
                    (self._current_stock_features['date_str'] == date)
                ]

                if stock_data.empty:
                    return True  # 没有数据，可能停牌

                # 检查关键字段是否为空或异常
                row = stock_data.iloc[0]
                if pd.isna(row.get('close', 0)) or row.get('close', 0) <= 0:
                    return True  # 收盘价异常，可能停牌

                if pd.isna(row.get('volume', 0)) or row.get('volume', 0) <= 0:
                    return True  # 成交量为0，可能停牌

            return False  # 默认认为正常交易

        except Exception as e:
            self.logger.warning(f"检查股票 {stock_code} 在 {date} 的停牌状态时出错: {e}")
            return False  # 出错时默认认为正常交易

    def _evaluate_selected_stocks_with_method(self, selected_stocks, stock_features_df, method_name, evaluation_days=2):
        """
        评估特定方法选出的股票的实际表现

        Args:
            selected_stocks: 选股结果
            stock_features_df: 股票特征数据
            method_name: 方法名称
            evaluation_days: 评估天数

        Returns:
            dict: 评估结果
        """
        self.logger.info(f"开始评估 {method_name} 方法的选股表现...")

        if not selected_stocks or not selected_stocks.get('selected_stocks_by_date'):
            return {
                'method_name': method_name,
                'evaluation_summary': {
                    'total_stocks': 0,
                    'evaluated_stocks': 0,
                    'avg_actual_return': 0.0,
                    'direction_accuracy': 0.0,
                    'win_rate': 0.0,
                    'performance_rating': '无数据'
                },
                'stock_evaluations': []
            }

        all_evaluations = []
        evaluation_by_date = {}

        for date, stocks in selected_stocks['selected_stocks_by_date'].items():
            date_evaluations = []

            for stock in stocks:
                stock_code = stock['stock_code']
                predicted_return = stock.get('predicted_return', 0)  # 已经是百分比形式

                # 获取实际收益率（统一为百分比）
                actual_return = self._get_actual_return_unified(
                    stock_code, date, stock_features_df, evaluation_days
                )

                if actual_return is not None:
                    # 计算方向准确性
                    direction_correct = (predicted_return > 0) == (actual_return > 0)

                    # 计算预测准确性（误差）
                    prediction_error = abs(predicted_return - actual_return)
                    prediction_accuracy = max(0, 1 - prediction_error / 10)  # 10%为满分误差

                    evaluation = {
                        'stock_code': stock_code,
                        'date': date,
                        'predicted_return': predicted_return,
                        'actual_return': actual_return,
                        'direction_correct': direction_correct,
                        'prediction_accuracy': prediction_accuracy,
                        'prediction_error': prediction_error,
                        'method_name': method_name,
                        'rank': stock.get('rank', 0)
                    }

                    date_evaluations.append(evaluation)
                    all_evaluations.append(evaluation)

            # 计算该日期的评估汇总
            if date_evaluations:
                date_summary = self._calculate_evaluation_summary(date_evaluations, method_name)
                date_summary['date'] = date
                evaluation_by_date[date] = date_summary

        # 计算整体评估汇总
        overall_summary = self._calculate_evaluation_summary(all_evaluations, method_name)

        result = {
            'method_name': method_name,
            'evaluation_summary': overall_summary,
            'evaluation_by_date': evaluation_by_date,
            'stock_evaluations': all_evaluations,
            'total_evaluated_dates': len(evaluation_by_date),
            'total_evaluated_stocks': len(all_evaluations)
        }

        self.logger.info(f"{method_name} 方法评估完成: 评估了 {len(all_evaluations)} 只股票")
        return result

    def _get_actual_return_unified(self, stock_code, prediction_date, stock_features_df, evaluation_days=2):
        """
        获取统一计量单位的实际收益率（百分比）

        Args:
            stock_code: 股票代码
            prediction_date: 预测日期
            stock_features_df: 股票特征数据
            evaluation_days: 评估天数

        Returns:
            float: 实际收益率（百分比），如果无法获取则返回None
        """
        try:
            # 计算评估日期
            prediction_dt = pd.to_datetime(prediction_date)
            evaluation_date = prediction_dt + pd.Timedelta(days=evaluation_days)
            evaluation_date_str = evaluation_date.strftime('%Y%m%d')

            # 获取预测日期的股票数据
            prediction_data = stock_features_df[
                (stock_features_df['stock_code'] == stock_code) &
                (stock_features_df['date_str'] == prediction_date)
            ]

            if prediction_data.empty:
                return None

            # 检查是否已有future_return_2d字段
            if 'future_return_2d' in prediction_data.columns:
                future_return = prediction_data['future_return_2d'].iloc[0]
                if pd.notna(future_return):
                    # future_return_2d通常是小数形式，转换为百分比
                    if abs(future_return) < 1:  # 如果是小数形式（如0.05）
                        return future_return * 100  # 转换为百分比（如5.0）
                    else:  # 如果已经是百分比形式
                        return future_return

            # 如果没有future_return_2d，手动计算
            # 获取评估日期的股票数据
            evaluation_data = stock_features_df[
                (stock_features_df['stock_code'] == stock_code) &
                (stock_features_df['date_str'] == evaluation_date_str)
            ]

            if evaluation_data.empty:
                return None

            # 计算收益率
            prediction_close = prediction_data['close'].iloc[0]
            evaluation_close = evaluation_data['close'].iloc[0]

            if pd.isna(prediction_close) or pd.isna(evaluation_close) or prediction_close <= 0:
                return None

            # 计算收益率并转换为百分比
            actual_return = ((evaluation_close - prediction_close) / prediction_close) * 100
            return actual_return

        except Exception as e:
            self.logger.warning(f"获取股票 {stock_code} 在 {prediction_date} 的实际收益率时出错: {e}")
            return None

    def _calculate_evaluation_summary(self, evaluations, method_name):
        """
        计算评估汇总统计

        Args:
            evaluations: 评估结果列表
            method_name: 方法名称

        Returns:
            dict: 汇总统计
        """
        if not evaluations:
            return {
                'method_name': method_name,
                'total_stocks': 0,
                'evaluated_stocks': 0,
                'avg_actual_return': 0.0,
                'direction_accuracy': 0.0,
                'win_rate': 0.0,
                'avg_prediction_error': 0.0,
                'performance_rating': '无数据'
            }

        # 过滤有效评估
        valid_evaluations = [e for e in evaluations if e['actual_return'] is not None]

        if not valid_evaluations:
            return {
                'method_name': method_name,
                'total_stocks': len(evaluations),
                'evaluated_stocks': 0,
                'avg_actual_return': 0.0,
                'direction_accuracy': 0.0,
                'win_rate': 0.0,
                'avg_prediction_error': 0.0,
                'performance_rating': '无有效数据'
            }

        # 计算统计指标
        actual_returns = [e['actual_return'] for e in valid_evaluations]
        direction_correct_count = sum(1 for e in valid_evaluations if e['direction_correct'])
        win_count = sum(1 for return_val in actual_returns if return_val > 0)
        prediction_errors = [e['prediction_error'] for e in valid_evaluations]

        avg_actual_return = np.mean(actual_returns)
        direction_accuracy = direction_correct_count / len(valid_evaluations)
        win_rate = win_count / len(valid_evaluations)
        avg_prediction_error = np.mean(prediction_errors)

        # 计算风险指标
        if len(actual_returns) > 1:
            returns_std = np.std(actual_returns, ddof=1)
            sharpe_ratio = avg_actual_return / returns_std if returns_std > 0 else 0.0
        else:
            sharpe_ratio = 0.0

        # 计算盈亏比
        profit_returns = [r for r in actual_returns if r > 0]
        loss_returns = [abs(r) for r in actual_returns if r < 0]

        avg_profit = np.mean(profit_returns) if profit_returns else 0.0
        avg_loss = np.mean(loss_returns) if loss_returns else 0.0
        profit_loss_ratio = avg_profit / avg_loss if avg_loss > 0 else float('inf') if avg_profit > 0 else 0.0

        # 性能评级
        performance_rating = self._calculate_performance_rating(
            avg_actual_return, direction_accuracy, win_rate, sharpe_ratio
        )

        return {
            'method_name': method_name,
            'total_stocks': len(evaluations),
            'evaluated_stocks': len(valid_evaluations),
            'avg_actual_return': avg_actual_return,
            'direction_accuracy': direction_accuracy,
            'win_rate': win_rate,
            'avg_prediction_error': avg_prediction_error,
            'sharpe_ratio': sharpe_ratio,
            'profit_loss_ratio': profit_loss_ratio,
            'avg_profit': avg_profit,
            'avg_loss': avg_loss,
            'performance_rating': performance_rating
        }

    def _compare_method_evaluations(self, similarity_evaluation, model_evaluation):
        """
        对比两种方法的评估结果

        Args:
            similarity_evaluation: 相似度匹配评估结果
            model_evaluation: 模型预测评估结果

        Returns:
            dict: 对比分析结果
        """
        comparison = {
            'comparison_summary': {},
            'winner_analysis': {},
            'detailed_comparison': {}
        }

        # 如果只有一种方法有结果
        if not similarity_evaluation and not model_evaluation:
            comparison['comparison_summary'] = {'status': '两种方法都没有评估结果'}
            return comparison
        elif not similarity_evaluation:
            comparison['comparison_summary'] = {'status': '只有模型预测方法有结果', 'winner': 'trained_model'}
            return comparison
        elif not model_evaluation:
            comparison['comparison_summary'] = {'status': '只有相似度匹配方法有结果', 'winner': 'similarity_matching'}
            return comparison

        # 两种方法都有结果，进行对比
        sim_summary = similarity_evaluation.get('evaluation_summary', {})
        model_summary = model_evaluation.get('evaluation_summary', {})

        # 对比关键指标
        metrics_comparison = {}
        key_metrics = ['avg_actual_return', 'direction_accuracy', 'win_rate', 'sharpe_ratio', 'profit_loss_ratio']

        for metric in key_metrics:
            sim_value = sim_summary.get(metric, 0)
            model_value = model_summary.get(metric, 0)

            if metric == 'profit_loss_ratio':
                # 处理无穷大值
                sim_value = sim_value if sim_value != float('inf') else 999
                model_value = model_value if model_value != float('inf') else 999

            metrics_comparison[metric] = {
                'similarity_matching': sim_value,
                'trained_model': model_value,
                'difference': sim_value - model_value,
                'winner': 'similarity_matching' if sim_value > model_value else 'trained_model' if model_value > sim_value else 'tie'
            }

        # 计算综合得分
        sim_score = self._calculate_comprehensive_score(sim_summary)
        model_score = self._calculate_comprehensive_score(model_summary)

        # 确定总体获胜者
        overall_winner = 'similarity_matching' if sim_score > model_score else 'trained_model' if model_score > sim_score else 'tie'

        comparison['comparison_summary'] = {
            'similarity_score': sim_score,
            'model_score': model_score,
            'overall_winner': overall_winner,
            'score_difference': sim_score - model_score
        }

        comparison['detailed_comparison'] = metrics_comparison

        # 获胜者分析
        winner_counts = {}
        for metric, comp in metrics_comparison.items():
            winner = comp['winner']
            winner_counts[winner] = winner_counts.get(winner, 0) + 1

        comparison['winner_analysis'] = {
            'metric_wins': winner_counts,
            'overall_winner': overall_winner,
            'recommendation': self._generate_method_recommendation(comparison)
        }

        return comparison

    def _calculate_comprehensive_score(self, summary):
        """
        计算综合评分

        Args:
            summary: 评估汇总

        Returns:
            float: 综合得分
        """
        if not summary or summary.get('evaluated_stocks', 0) == 0:
            return 0.0

        # 权重设置
        weights = {
            'avg_actual_return': 0.3,    # 平均收益率权重30%
            'direction_accuracy': 0.25,  # 方向准确率权重25%
            'win_rate': 0.25,           # 胜率权重25%
            'sharpe_ratio': 0.2         # 夏普比率权重20%
        }

        score = 0.0

        # 收益率得分（归一化到0-100）
        avg_return = summary.get('avg_actual_return', 0)
        return_score = min(100, max(0, avg_return * 10 + 50))  # 5%收益率得分100分
        score += return_score * weights['avg_actual_return']

        # 方向准确率得分
        direction_acc = summary.get('direction_accuracy', 0)
        score += direction_acc * 100 * weights['direction_accuracy']

        # 胜率得分
        win_rate = summary.get('win_rate', 0)
        score += win_rate * 100 * weights['win_rate']

        # 夏普比率得分（归一化）
        sharpe = summary.get('sharpe_ratio', 0)
        sharpe_score = min(100, max(0, sharpe * 50 + 50))  # 1.0夏普比率得分100分
        score += sharpe_score * weights['sharpe_ratio']

        return score

    def _generate_method_recommendation(self, comparison):
        """
        生成方法推荐

        Args:
            comparison: 对比结果

        Returns:
            str: 推荐说明
        """
        overall_winner = comparison['comparison_summary'].get('overall_winner', 'tie')
        score_diff = abs(comparison['comparison_summary'].get('score_difference', 0))

        if overall_winner == 'tie' or score_diff < 5:
            return "两种方法表现相近，建议结合使用"
        elif overall_winner == 'similarity_matching':
            if score_diff > 20:
                return "相似度匹配方法明显优于模型预测，强烈推荐使用相似度匹配"
            else:
                return "相似度匹配方法略优于模型预测，推荐优先使用相似度匹配"
        else:  # trained_model
            if score_diff > 20:
                return "模型预测方法明显优于相似度匹配，强烈推荐使用模型预测"
            else:
                return "模型预测方法略优于相似度匹配，推荐优先使用模型预测"

    def _calculate_performance_rating(self, avg_return, direction_accuracy, win_rate, sharpe_ratio):
        """
        计算性能评级

        Args:
            avg_return: 平均收益率
            direction_accuracy: 方向准确率
            win_rate: 胜率
            sharpe_ratio: 夏普比率

        Returns:
            str: 性能评级
        """
        # 计算综合得分
        score = 0

        # 收益率得分 (40%)
        if avg_return >= 5:
            score += 40
        elif avg_return >= 3:
            score += 30
        elif avg_return >= 1:
            score += 20
        elif avg_return >= 0:
            score += 10

        # 方向准确率得分 (30%)
        if direction_accuracy >= 0.7:
            score += 30
        elif direction_accuracy >= 0.6:
            score += 20
        elif direction_accuracy >= 0.5:
            score += 10

        # 胜率得分 (20%)
        if win_rate >= 0.7:
            score += 20
        elif win_rate >= 0.6:
            score += 15
        elif win_rate >= 0.5:
            score += 10

        # 夏普比率得分 (10%)
        if sharpe_ratio >= 1.0:
            score += 10
        elif sharpe_ratio >= 0.5:
            score += 5

        # 评级映射
        if score >= 80:
            return "优秀"
        elif score >= 60:
            return "良好"
        elif score >= 40:
            return "一般"
        elif score >= 20:
            return "较差"
        else:
            return "很差"

    def _analyze_prediction_comparison(self, similarity_predictions, model_predictions):
        """
        分析两种预测方法的对比

        Args:
            similarity_predictions: 相似度匹配预测结果
            model_predictions: 模型预测结果

        Returns:
            dict: 对比分析结果
        """
        comparison = {
            'similarity_stats': {},
            'model_stats': {},
            'overlap_analysis': {},
            'score_comparison': {}
        }

        # 统计相似度匹配结果
        if similarity_predictions:
            sim_scores = [pred.get('prediction_score', 0) for pred in similarity_predictions.values()]
            comparison['similarity_stats'] = {
                'total_predictions': len(similarity_predictions),
                'avg_score': np.mean(sim_scores),
                'score_range': [np.min(sim_scores), np.max(sim_scores)],
                'positive_predictions': sum(1 for score in sim_scores if score > 0)
            }

        # 统计模型预测结果
        if model_predictions:
            model_scores = [pred.get('prediction_score', 0) for pred in model_predictions.values()]
            comparison['model_stats'] = {
                'total_predictions': len(model_predictions),
                'avg_score': np.mean(model_scores),
                'score_range': [np.min(model_scores), np.max(model_scores)],
                'positive_predictions': sum(1 for score in model_scores if score > 0)
            }

        # 分析重叠情况
        if similarity_predictions and model_predictions:
            sim_keys = set(similarity_predictions.keys())
            model_keys = set(model_predictions.keys())
            overlap_keys = sim_keys & model_keys

            comparison['overlap_analysis'] = {
                'similarity_only': len(sim_keys - model_keys),
                'model_only': len(model_keys - sim_keys),
                'both_methods': len(overlap_keys),
                'overlap_ratio': len(overlap_keys) / len(sim_keys | model_keys) if sim_keys | model_keys else 0
            }

            # 对重叠股票的得分对比
            if overlap_keys:
                score_correlations = []
                for key in overlap_keys:
                    sim_score = similarity_predictions[key].get('prediction_score', 0)
                    model_score = model_predictions[key].get('prediction_score', 0)
                    score_correlations.append((sim_score, model_score))

                if score_correlations:
                    sim_scores_overlap = [s[0] for s in score_correlations]
                    model_scores_overlap = [s[1] for s in score_correlations]
                    correlation = np.corrcoef(sim_scores_overlap, model_scores_overlap)[0, 1] if len(score_correlations) > 1 else 0

                    comparison['score_comparison'] = {
                        'correlation': correlation,
                        'avg_similarity_score': np.mean(sim_scores_overlap),
                        'avg_model_score': np.mean(model_scores_overlap),
                        'agreement_rate': sum(1 for s, m in score_correlations if (s > 0) == (m > 0)) / len(score_correlations)
                    }

        return comparison

    def _save_prediction_analysis(self, prediction_results):
        """
        保存预测分析结果到文件

        Args:
            prediction_results: 包含所有预测结果的字典
        """
        try:
            # 保存到模型目录
            analysis_path = self.model_dir / "prediction_analysis.json"

            # 转换numpy类型为Python原生类型
            def convert_numpy(obj):
                if isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, dict):
                    return {k: convert_numpy(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_numpy(item) for item in obj]
                else:
                    return obj

            converted_results = convert_numpy(prediction_results)

            with open(analysis_path, "w", encoding="utf-8") as f:
                json.dump(converted_results, f, ensure_ascii=False, indent=2)

            self.logger.info(f"预测分析结果已保存到: {analysis_path}")

            # 输出对比分析摘要
            if 'comparison_analysis' in prediction_results and prediction_results['comparison_analysis']:
                comp = prediction_results['comparison_analysis']
                self.logger.info("=== 预测方法对比分析 ===")

                if 'similarity_stats' in comp:
                    sim_stats = comp['similarity_stats']
                    self.logger.info(f"相似度匹配: {sim_stats.get('total_predictions', 0)} 个预测, "
                                   f"平均得分: {sim_stats.get('avg_score', 0):.4f}, "
                                   f"正向预测: {sim_stats.get('positive_predictions', 0)}")

                if 'model_stats' in comp:
                    model_stats = comp['model_stats']
                    self.logger.info(f"模型预测: {model_stats.get('total_predictions', 0)} 个预测, "
                                   f"平均得分: {model_stats.get('avg_score', 0):.4f}, "
                                   f"正向预测: {model_stats.get('positive_predictions', 0)}")

                if 'overlap_analysis' in comp:
                    overlap = comp['overlap_analysis']
                    self.logger.info(f"重叠分析: 仅相似度={overlap.get('similarity_only', 0)}, "
                                   f"仅模型={overlap.get('model_only', 0)}, "
                                   f"两种方法={overlap.get('both_methods', 0)}, "
                                   f"重叠率={overlap.get('overlap_ratio', 0):.2%}")

                if 'score_comparison' in comp:
                    score_comp = comp['score_comparison']
                    self.logger.info(f"得分对比: 相关性={score_comp.get('correlation', 0):.3f}, "
                                   f"一致率={score_comp.get('agreement_rate', 0):.2%}")

        except Exception as e:
            self.logger.error(f"保存预测分析结果时出错: {e}")

    def _evaluate_selected_stocks(self, selected_stocks, stock_features_df, evaluation_days=2):
        """
        评估选中股票的表现，兼容新旧格式

        Args:
            selected_stocks: 选中的股票结果字典
            stock_features_df: 股票特征DataFrame，包含实际收益率数据
            evaluation_days: 评估天数，默认2天

        Returns:
            dict: 评估结果
        """
        self.logger.info(f"开始评估选中股票的表现...")

        if not selected_stocks:
            return {'evaluation_results': {}, 'summary': {}}

        # 检查是否有新格式的按日期选股结果
        has_daily_results = 'selected_stocks_by_date' in selected_stocks and selected_stocks['selected_stocks_by_date']

        # 如果没有按日期分组的结果，回退到旧的评估方式
        if not has_daily_results:
            return self._evaluate_selected_stocks_old(selected_stocks, stock_features_df, evaluation_days)

        # 获取所有日期的评估结果
        evaluation_by_date = {}
        daily_summaries = []
        overall_valid_results = []

        for date, stocks in selected_stocks['selected_stocks_by_date'].items():
            # 为每个日期创建兼容旧格式的选股结果
            daily_selected = {
                'selected_stocks': stocks,
                'date': date,
                'total_stocks': len(stocks)
            }

            # 调用旧方法评估单日选股结果
            daily_eval = self._evaluate_selected_stocks_old(daily_selected, stock_features_df, evaluation_days)
            evaluation_by_date[date] = daily_eval

            # 记录每日评估摘要
            if 'evaluation_summary' in daily_eval and isinstance(daily_eval['evaluation_summary'], dict):
                summary = daily_eval['evaluation_summary']
                if 'evaluated_stocks' in summary and summary['evaluated_stocks'] > 0:
                    summary['date'] = date  # 添加日期信息
                    daily_summaries.append(summary)

            # 收集所有日期的有效评估结果用于整体统计
            if 'stock_evaluations' in daily_eval:
                valid_results = [eval_item for eval_item in daily_eval['stock_evaluations']
                                if 'actual_return' in eval_item and eval_item['actual_return'] is not None]
                overall_valid_results.extend(valid_results)

        # 计算整体评估指标
        overall_summary = {}
        if overall_valid_results:
            # 平均实际收益率
            avg_actual_return = sum(r['actual_return'] for r in overall_valid_results) / len(overall_valid_results)

            # 方向正确率
            direction_correct_count = sum(1 for r in overall_valid_results if r.get('direction_correct', False) == True)
            direction_accuracy = direction_correct_count / len(overall_valid_results)

            # 计算平均预测准确度
            valid_accuracy = [r['prediction_accuracy'] for r in overall_valid_results if r.get('prediction_accuracy') is not None]
            avg_prediction_accuracy = sum(valid_accuracy) / len(valid_accuracy) if valid_accuracy else 0

            # 胜率（实际收益为正的比例）
            win_count = sum(1 for r in overall_valid_results if r['actual_return'] > 0)
            win_rate = win_count / len(overall_valid_results)

            overall_summary = {
                'total_evaluated_dates': len(evaluation_by_date),
                'total_evaluated_stocks': len(overall_valid_results),
                'avg_actual_return': avg_actual_return,
                'direction_accuracy': direction_accuracy,
                'avg_prediction_accuracy': avg_prediction_accuracy,
                'win_rate': win_rate,
                'performance_rating': self._get_performance_rating(avg_actual_return, win_rate, direction_accuracy)
            }

            # 计算日平均表现
            if daily_summaries:
                daily_returns = [s['avg_actual_return'] for s in daily_summaries if 'avg_actual_return' in s]
                daily_win_rates = [s['win_rate'] for s in daily_summaries if 'win_rate' in s]
                daily_directions = [s['direction_accuracy'] for s in daily_summaries if 'direction_accuracy' in s]

                if daily_returns:
                    overall_summary['avg_daily_return'] = sum(daily_returns) / len(daily_returns)
                if daily_win_rates:
                    overall_summary['avg_daily_win_rate'] = sum(daily_win_rates) / len(daily_win_rates)
                if daily_directions:
                    overall_summary['avg_daily_direction_accuracy'] = sum(daily_directions) / len(daily_directions)

            self.logger.info(f"整体评估结果 - 平均实际收益率: {overall_summary['avg_actual_return']:.2f}%, "
                          f"方向准确率: {overall_summary['direction_accuracy']*100:.1f}%, "
                          f"胜率: {overall_summary['win_rate']*100:.1f}%")
        else:
            overall_summary = {"evaluation_summary": "无法评估"}

        # 返回综合评估结果
        final_evaluation = {
            'overall_summary': overall_summary,
            'evaluation_by_date': evaluation_by_date,
            'daily_summaries': daily_summaries
        }

        return final_evaluation



    
    def _calculate_weighted_similarity(self, vec1_dict, vec2_dict, feature_weights=None):
        """计算两个特征字典的加权相似度
        
        Args:
            vec1_dict: 第一个特征字典
            vec2_dict: 第二个特征字典
            feature_weights: 特征权重字典，如果为None则使用自动计算的权重
            
        Returns:
            tuple: (相似度分数, 匹配的特征列表, 特征贡献度字典)
        """
        # 找出两个字典中共有的特征
        common_features = []
        
        # 如果没有提供权重，则为所有共同特征赋予相同权重
        if feature_weights is None:
            feature_weights = {}
            for feature in set(vec1_dict.keys()).intersection(set(vec2_dict.keys())):
                feature_weights[feature] = 1.0
        
        for feature in feature_weights.keys():
            if feature in vec1_dict and feature in vec2_dict:
                common_features.append(feature)
        
        if not common_features:
            return 0, [], {}
        
        # 提取共有特征的值和权重
        vec1 = []
        vec2 = []
        weights = []
        
        for feature in common_features:
            vec1.append(vec1_dict[feature])
            vec2.append(vec2_dict[feature])
            weights.append(feature_weights[feature])
        
        # 转换为numpy数组
        vec1 = np.array(vec1)
        vec2 = np.array(vec2)
        weights = np.array(weights)
        
        # 标准化权重
        weights = weights / np.sum(weights)
        
        # 计算加权余弦相似度
        numerator = np.sum(weights * vec1 * vec2)
        denominator = np.sqrt(np.sum(weights * vec1 * vec1)) * np.sqrt(np.sum(weights * vec2 * vec2))
        
        if denominator == 0:
            return 0, common_features, {}
        
        similarity = numerator / denominator
        
        # 将相似度限制在0到1之间
        similarity = max(0, min(1, similarity))
        
        # 计算每个特征对相似度的贡献
        feature_contributions = {}
        if similarity > 0 and denominator > 0:  # 确保分母大于0
            for i, feature in enumerate(common_features):
                try:
                    # 安全计算特征贡献度
                    contribution = weights[i] * vec1[i] * vec2[i] / (similarity * denominator)
                    feature_contributions[feature] = contribution
                except (ZeroDivisionError, RuntimeWarning):
                    # 处理可能的除零错误
                    feature_contributions[feature] = 0.0
            
            # 按贡献度排序
            feature_contributions = {k: v for k, v in sorted(
                feature_contributions.items(), key=lambda item: abs(item[1]), reverse=True
            )}
        
        return similarity, common_features, feature_contributions
    


    def select_stocks_by_change_points(self, start_date=None, end_date=None, model_name="stock_selector", top_n=50, use_cache=True):
        """基于变化点方法选择个股
        
        Args:
            start_date: 开始日期，如果为None则使用默认值
            end_date: 结束日期，如果为None则使用默认值
            model_name: 模型名称
            top_n: 返回的股票数量
            use_cache: 是否使用特征缓存，默认为True
            
        Returns:
            dict: {'selected_stocks': [...], 'model_name': model_name, 'date': date}
        """
        # 设置日期范围
        if end_date is None:
            end_date = datetime.now().strftime("%Y%m%d")
        if start_date is None:
            start_date = (datetime.strptime(end_date, "%Y%m%d") - timedelta(days=180)).strftime("%Y%m%d")
            
        # 确保交易日历已加载
        if self.trading_days is None:
            self._load_trading_calendar()
            
        if not self.trading_days:
            self.logger.error("无法加载交易日历，无法继续")
            return None
            
        # 获取日期范围内的实际交易日
        trading_days_in_range = [day for day in self.trading_days 
                               if start_date <= day <= end_date]
        
        if not trading_days_in_range:
            self.logger.error(f"日期范围 {start_date} 至 {end_date} 内没有有效交易日")
            return None
            
        # 使用实际交易日来划分训练和预测日期
        total_trading_days = len(trading_days_in_range)
        train_days_count = int(total_trading_days * 0.9)  # 使用60%的交易日作为训练集
        
        train_end_idx = min(train_days_count, total_trading_days - 1)
        train_end_date = trading_days_in_range[train_end_idx]
        
        predict_start_idx = train_end_idx + 1
        predict_start_date = trading_days_in_range[predict_start_idx] if predict_start_idx < total_trading_days else end_date
        predict_end_date = end_date
        
        self.logger.info(f"日期范围内共有 {total_trading_days} 个交易日")
        self.logger.info(f"训练日期范围: {start_date} 至 {train_end_date} (共{train_days_count}个交易日)")
        self.logger.info(f"预测日期范围: {predict_start_date} 至 {predict_end_date} (共{total_trading_days - train_days_count}个交易日)")
        
        # 步骤1: 加载板块特征
        self.logger.info("加载板块特征数据...")
        sector_features_df = self._batch_load_features(self._get_all_sectors(), start_date, end_date)
        
        # 步骤2: 加载个股特征
        self.logger.info("加载个股特征数据...")
        stock_features_df = self.load_stock_features(start_date=start_date, end_date=end_date)

        self.logger.info(f"个股特征列：{stock_features_df.columns}")

        if stock_features_df is None or stock_features_df.empty:
            self.logger.error("无法获取个股特征数据")
            return None

        # 步骤3: 综合数据过滤 - 在模型训练前清理无效数据
        self.logger.info("开始综合数据过滤...")
        filtered_stock_df, filtered_sector_df, filter_stats = self._comprehensive_data_filtering(
            stock_features_df, sector_features_df
        )

        # 检查过滤后的数据是否足够
        if filtered_stock_df is None or filtered_stock_df.empty:
            self.logger.error("过滤后没有有效的个股数据，无法继续训练")
            return None

        if len(filtered_stock_df) < 1000:  # 至少需要1000条记录
            self.logger.warning(f"过滤后个股数据量较少: {len(filtered_stock_df)} 条，可能影响模型质量")

        # 使用过滤后的数据替换原始数据
        stock_features_df = filtered_stock_df
        sector_features_df = filtered_sector_df

        # 步骤4: 检查过滤后的特征数据质量
        self.logger.info("检查过滤后的特征数据质量...")
        self._check_feature_quality(stock_features_df, sector_features_df)
        
        # 步骤3: 划分训练集和预测集
        # 筛选训练数据 - 使用实际的交易日期划分
        train_mask = ((pd.to_datetime(stock_features_df['date_str']) >= pd.to_datetime(start_date)) & 
                      (pd.to_datetime(stock_features_df['date_str']) <= pd.to_datetime(train_end_date)))
        train_features = stock_features_df[train_mask].copy()
        
        # 筛选预测数据
        predict_mask = ((pd.to_datetime(stock_features_df['date_str']) >= pd.to_datetime(predict_start_date)) & 
                         (pd.to_datetime(stock_features_df['date_str']) <= pd.to_datetime(predict_end_date)))
        predict_features = stock_features_df[predict_mask].copy()
        
        self.logger.info(f"训练数据: {len(train_features)} 条记录")
        self.logger.info(f"预测数据: {len(predict_features)} 条记录")
        
        # 保存预测日期列表，用于进度计算
        predict_dates = sorted(predict_features['date_str'].unique())
        self.logger.info(f"预测数据包含 {len(predict_dates)} 个交易日")
        
        # 确保预测数据中的日期都是有效交易日
        valid_predict_dates = [date for date in predict_dates if date in self.trading_days]
        if len(valid_predict_dates) != len(predict_dates):
            self.logger.warning(f"预测数据中包含 {len(predict_dates) - len(valid_predict_dates)} 个非交易日，将被忽略")
            # 更新预测数据，只保留有效交易日的数据
            predict_features = predict_features[predict_features['date_str'].isin(valid_predict_dates)]
            predict_dates = valid_predict_dates
        
        
        # 步骤4: 检测个股的变化点
        self.logger.info("检测个股变化点...")
        stock_change_points = self._detect_stock_change_points(train_features)
        
        # 步骤5: 计算个股相对板块的特征（仅针对变化点）
        self.logger.info("计算个股相对板块的特征（仅针对变化点）...")
        train_features, train_relations = self.collect_stock_sector_change_point_features(
            train_features, sector_features_df, stock_change_points, is_training=True, use_cache=use_cache)
            
        # 对预测数据也进行相对特征计算（全部日期）
        self.logger.info("计算预测数据的个股相对板块特征...")
        predict_features, predict_relations = self.collect_stock_sector_change_point_features(
            predict_features, sector_features_df, is_training=False, use_cache=use_cache)
        
        # 步骤6: 分析变化点特征模式
        self.logger.info("分析个股变化点特征模式...")
        # 传递训练数据和板块关联结果
        stock_feature_patterns = self._analyze_stock_change_point_patterns(train_features, stock_change_points, relations_df=train_relations)
        
        
        # 步骤7: 预测个股表现
        self.logger.info("预测个股表现...")
        # 传递预测数据和板块关联结果
        prediction_results = self._predict_stocks_with_change_points(predict_features, stock_feature_patterns, relations_df=predict_relations)

        if not prediction_results:
            self.logger.error("股票预测失败")
            return None

        # 步骤8: 分别处理两种预测方法的选股
        self.logger.info(f"分别处理相似度预测和模型预测的选股...")

        # 8.1: 处理相似度预测选股
        similarity_selected = None
        if prediction_results.get('similarity_predictions'):
            self.logger.info(f"处理相似度预测选股，共 {len(prediction_results['similarity_predictions'])} 只股票")
            similarity_selected = self._rank_and_select_stocks_by_method(
                prediction_results['similarity_predictions'],
                top_n,
                stock_features_df,
                method_name="similarity_matching"
            )

        # 8.2: 处理模型预测选股
        model_selected = None
        if prediction_results.get('model_predictions'):
            self.logger.info(f"处理模型预测选股，共 {len(prediction_results['model_predictions'])} 只股票")
            model_selected = self._rank_and_select_stocks_by_method(
                prediction_results['model_predictions'],
                top_n,
                stock_features_df,
                method_name="trained_model"
            )

        # 8.3: 合并选股结果用于兼容性
        selected_stocks = self._combine_selection_results(similarity_selected, model_selected)
        
        # 步骤9: 分别评估两种方法的实际表现
        self.logger.info("分别评估两种方法的实际表现...")

        # 9.1: 评估相似度预测方法
        similarity_evaluation = None
        if similarity_selected:
            self.logger.info("评估相似度预测方法的表现...")
            similarity_evaluation = self._evaluate_selected_stocks_with_method(
                similarity_selected, stock_features_df, "similarity_matching"
            )

        # 9.2: 评估模型预测方法
        model_evaluation = None
        if model_selected:
            self.logger.info("评估模型预测方法的表现...")
            model_evaluation = self._evaluate_selected_stocks_with_method(
                model_selected, stock_features_df, "trained_model"
            )

        # 9.3: 生成对比评估报告
        comparison_evaluation = self._compare_method_evaluations(
            similarity_evaluation, model_evaluation
        )

        # 将评估结果添加到选股结果中
        selected_stocks['evaluation'] = {
            'similarity_evaluation': similarity_evaluation,
            'model_evaluation': model_evaluation,
            'comparison_evaluation': comparison_evaluation
        }
        selected_stocks['model_name'] = model_name
        selected_stocks['prediction_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 为选出的股票添加排名信息（如果尚未添加）
        if 'selected_stocks' in selected_stocks and selected_stocks['selected_stocks']:
            for i, stock in enumerate(selected_stocks['selected_stocks']):
                if 'rank' not in stock:
                    stock['rank'] = i + 1
        
        # 保存结果
        result_path = self.model_dir / f"{model_name}_results.json"
        with open(result_path, "w", encoding="utf-8") as f:
            json.dump(selected_stocks, f, ensure_ascii=False, cls=NumpyEncoder, indent=2)
        
        # 保存每日选股结果到单独文件
        if 'selected_stocks_by_date' in selected_stocks and selected_stocks['selected_stocks_by_date']:
            daily_results_dir = self.model_dir / f"{model_name}_daily_results"
            os.makedirs(daily_results_dir, exist_ok=True)
            
            for date, stocks in selected_stocks['selected_stocks_by_date'].items():
                date_result_path = daily_results_dir / f"{date}_stocks.json"
                
                # 获取该日期的评估结果
                date_evaluation = None
                if 'evaluation' in selected_stocks and 'evaluation_by_date' in selected_stocks['evaluation']:
                    date_evaluation = selected_stocks['evaluation']['evaluation_by_date'].get(date)
                
                date_result = {
                    'selected_stocks': stocks,
                    'date': date,
                    'model_name': model_name,
                    'total_stocks': len(stocks),
                    'evaluation': date_evaluation
                }
                
                with open(date_result_path, "w", encoding="utf-8") as f:
                    json.dump(date_result, f, ensure_ascii=False, cls=NumpyEncoder, indent=2)
            
            self.logger.info(f"每日选股结果已保存到目录: {daily_results_dir}")
        
        # 生成评估报告
        if 'evaluation' in selected_stocks:
            eval_results = selected_stocks['evaluation']
            
            # 处理新的评估结果格式
            if 'overall_summary' in eval_results and isinstance(eval_results['overall_summary'], dict):
                summary = eval_results['overall_summary']
                
                # 输出整体评估结果
                if 'avg_actual_return' in summary and 'win_rate' in summary:
                    self.logger.info(f"整体选股评估结果:")
                    self.logger.info(f"- 评估日期数: {summary.get('total_evaluated_dates', 'N/A')}")
                    self.logger.info(f"- 评估股票数: {summary.get('total_evaluated_stocks', 'N/A')}")
                    self.logger.info(f"- 平均实际收益率: {summary['avg_actual_return']:.2f}%")
                    self.logger.info(f"- 方向准确率: {summary['direction_accuracy']*100:.1f}%") 
                    self.logger.info(f"- 胜率: {summary['win_rate']*100:.1f}%")
                    self.logger.info(f"- 表现评级: {summary['performance_rating']}")
                    
                    # 如果有日平均表现数据
                    if 'avg_daily_return' in summary:
                        self.logger.info(f"- 日均收益率: {summary['avg_daily_return']:.2f}%")
                        self.logger.info(f"- 日均胜率: {summary['avg_daily_win_rate']*100:.1f}%")
            
            # 处理旧的评估结果格式 (向后兼容)
            elif 'evaluation_summary' in eval_results and isinstance(eval_results['evaluation_summary'], dict):
                summary = eval_results['evaluation_summary']
                if 'avg_actual_return' in summary and 'win_rate' in summary and 'direction_accuracy' in summary:
                    self.logger.info(f"选股评估结果:")
                    self.logger.info(f"- 平均实际收益率: {summary['avg_actual_return']:.2f}%")
                    self.logger.info(f"- 方向准确率: {summary['direction_accuracy']*100:.1f}%") 
                    self.logger.info(f"- 胜率: {summary['win_rate']*100:.1f}%")
                    self.logger.info(f"- 表现评级: {summary['performance_rating']}")
        
        self.logger.info(f"选股结果和评估已保存到 {result_path}")
        
        return selected_stocks

    def load_stock_features(self, start_date=None, end_date=None):
        """
        加载个股特征数据，使用缓存机制提高效率
        
        Args:
            start_date: 开始日期，格式为YYYYMMDD
            end_date: 结束日期，格式为YYYYMMDD
            
        Returns:
            DataFrame: 股票特征数据
        """
        self.logger.info(f"加载个股特征数据 {start_date} 至 {end_date}...")
        
        # 创建缓存键
        cache_key = f"stock_features_{start_date}_{end_date}"
        cache_file = self.features_cache_dir / f"{cache_key}.parquet"
        
        # 检查缓存
        if cache_file.exists():
            self.logger.info(f"从缓存加载个股特征数据: {cache_file}")
            cached_data = pd.read_parquet(cache_file)
            
            # 检查缓存是否包含必要的列
            required_columns = ['future_return_2d']
            missing_columns = [col for col in required_columns if col not in cached_data.columns]
            
            if not missing_columns:
                # 检查future_return_2d列是否有足够的有效值
                valid_pct = cached_data['future_return_2d'].notna().sum() / len(cached_data)
                self.logger.info(f"缓存数据中future_return_2d列有效值比例: {valid_pct:.2f}")
                
                if valid_pct >= 0.5:  # 如果有效值比例足够高
                    self.logger.info(f"缓存数据包含所有必要列且有效，直接使用")
                    return cached_data
                else:
                    self.logger.warning(f"缓存数据中future_return_2d列有效值比例过低: {valid_pct:.2f}，需要重新计算")
            else:
                self.logger.warning(f"缓存数据缺少必要列: {missing_columns}，需要重新计算")
        
        # 缓存不存在或者不完整，从原始特征文件加载
        try:
            # 确定日期范围
            if start_date is None or end_date is None:
                raise ValueError("必须指定开始日期和结束日期")
                
            # 获取日期范围内的所有交易日
            calendar = self._load_trading_calendar()
            if calendar is None:
                raise ValueError("无法加载交易日历")
                
            # 筛选日期范围内的交易日
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            trading_dates = [d for d in calendar if start_dt <= pd.to_datetime(d) <= end_dt]
            
            if not trading_dates:
                raise ValueError(f"指定的日期范围 {start_date} 至 {end_date} 内没有交易日")
            
            # 从generate_daily_features.py中获取的保存特征的目录路径
            features_dir = get_data_path("features/daily")
            
            # 从每个交易日的特征文件加载数据
            all_data = []
            for date in trading_dates:
                date_str = date if isinstance(date, str) else date.strftime("%Y%m%d")
                feature_file = features_dir / f"{date_str}.parquet"
                
                if not os.path.exists(feature_file):
                    self.logger.warning(f"日期 {date_str} 的特征文件不存在: {feature_file}")
                    continue
                    
                try:
                    # 加载当日特征
                    daily_features = pd.read_parquet(feature_file)
                    all_data.append(daily_features)
                    self.logger.debug(f"成功加载日期 {date_str} 的特征数据，共 {len(daily_features)} 条记录")
                except Exception as e:
                    self.logger.error(f"加载日期 {date_str} 的特征数据时出错: {e}")
                    continue
            
            if not all_data:
                raise ValueError(f"未能加载任何日期的特征数据")
                
            # 合并所有日期的数据
            combined_features = pd.concat(all_data, ignore_index=True)
            self.logger.info(f"成功加载 {len(trading_dates)} 个交易日的特征数据，共 {len(combined_features)} 条记录")
            
            # 对特征进行高级分析和增强处理
            enhanced_features = self._enhance_stock_features(combined_features)
            
            # 保存到缓存
            ensure_dir(self.features_cache_dir)
            enhanced_features.to_parquet(cache_file)
            self.logger.info(f"增强后的个股特征数据已缓存到: {cache_file}")
            
            return enhanced_features
            
        except Exception as e:
            self.logger.error(f"加载个股特征数据时出错: {e}")
            self.logger.error(traceback.format_exc())
            raise ValueError(f"加载个股特征数据失败: {e}")
    
    def _enhance_stock_features(self, features_df):
        """
        增强股票特征，添加技术分析指标等
        """
        if features_df is None or features_df.empty:
            self.logger.warning("特征数据为空，无法增强特征")
            return features_df
            
        self.logger.info(f"开始增强股票特征，添加技术分析指标...")
        # self.logger.debug(f"原始特征列: {features_df.columns.tolist()}")
            
        # 确保按股票和日期排序
        features_df = features_df.sort_values(['stock_code', 'date_str'])
        
        # 涨跌停相关设置
        # 沪深两市股票的涨跌停比例在不同时期有调整
        # 这里使用通用设置 - 实际应用中应当根据股票类型和交易日期动态设置
        up_limit_ratio = 0.1   # 普通个股涨停比例10%
        down_limit_ratio = -0.1  # 普通个股跌停比例-10%
        
        # 创科板/ST股等可能有不同的涨跌停比例
        # 简化处理: 创业板/科创板等为20%，ST股为5%
        for idx, row in features_df.iterrows():
            # 股票代码首字符为6且第二字符为8的为科创板
            if row['stock_code'].startswith('68'):
                up_limit_ratio = 0.2
                down_limit_ratio = -0.2
            # 股票代码首字符为3的为创业板
            elif row['stock_code'].startswith('3'):
                up_limit_ratio = 0.2  
                down_limit_ratio = -0.2
            elif '.BJ' in row['stock_code']:  # 北交所股票
                up_limit_ratio = 0.3
                down_limit_ratio = -0.3
            # 包含ST的是特别处理股票
            elif 'ST' in str(row.get('stock_name', '')):
                up_limit_ratio = 0.05
                down_limit_ratio = -0.05
            else:
                up_limit_ratio = 0.1
                down_limit_ratio = -0.1
                
            # 判断是否涨停或跌停
            if 'close' in row.index and 'preClose' in row.index:
                # 计算理论涨停价
                theoretical_limit_up = row['preClose'] * (1 + up_limit_ratio)
                # 四舍五入到0.01元
                rounded_limit_up = round(theoretical_limit_up, 2)
                # 判断是否涨停
                features_df.loc[idx, 'is_limit_up'] = abs(row['close'] - rounded_limit_up) < 0.005

                # 计算理论跌停价
                theoretical_limit_down = row['preClose'] * (1 + down_limit_ratio)
                # 四舍五入到0.01元
                rounded_limit_down = round(theoretical_limit_down, 2)
                # 判断是否跌停
                features_df.loc[idx, 'is_limit_down'] = abs(row['close'] - rounded_limit_down) < 0.005
            elif 'change_percent' in row.index:
                # 如果未提供价格数据，则使用涨跌幅判断，允许一定误差
                up_limit_pct = up_limit_ratio * 100
                down_limit_pct = down_limit_ratio * 100
                features_df.loc[idx, 'is_limit_up'] = abs(row['change_percent'] - up_limit_pct) <= 0.15
                features_df.loc[idx, 'is_limit_down'] = abs(row['change_percent'] - down_limit_pct) <= 0.15
        
        # 按股票分组处理
        grouped = features_df.groupby('stock_code')
        stock_groups = list(grouped)
        
        # 使用并行处理，但避免pickling错误
        import pandas as pd
        from tqdm import tqdm
        import multiprocessing
        
        # 确定是否使用并行处理
        use_parallel = len(stock_groups) > 3 and multiprocessing.cpu_count() > 2
        all_enhanced = []
        
        if use_parallel:
            # 优先使用进程池以提高性能，如果出错则退回到线程池
            try:
                from concurrent.futures import ProcessPoolExecutor
                num_cpus = multiprocessing.cpu_count()
                max_workers = min(num_cpus - 1, 8)  # 保留1个CPU核心给系统，最多使用8个进程
                
                self.logger.info(f"使用并行进程处理增强 {len(stock_groups)} 只股票的特征，使用 {max_workers} 个进程...")
                
                # 创建高效处理函数
                def process_stock_wrapper(group_tuple):
                    stock_code, stock_df = group_tuple
                    try:
                        # 快速检查数据有效性
                        if not isinstance(stock_df, pd.DataFrame) or len(stock_df) < 5:
                            return stock_df
                        return self._process_single_stock_features(stock_df)
                    except Exception:
                        # 静默处理错误，避免日志过多
                        return stock_df
                
                # 使用进程池执行
                with ProcessPoolExecutor(max_workers=max_workers) as executor:
                    results = []
                    for stock_code, stock_df in tqdm(stock_groups, desc="处理股票特征"):
                        results.append(executor.submit(process_stock_wrapper, (stock_code, stock_df)))
                    
                    # 收集结果 - 确保正确获取Future的结果
                    for future in tqdm(results, desc="收集处理结果"):
                        result = future.result()
                        if result is not None:
                            all_enhanced.append(result)
            
            except Exception as e:
                self.logger.warning(f"使用进程池处理失败，切换到线程池: {e}")
                
                # 如果进程池失败，退回到线程池
                from concurrent.futures import ThreadPoolExecutor
                
                self.logger.info(f"使用线程池处理 {len(stock_groups)} 只股票的特征...")
                
                # 清空之前的结果
                all_enhanced = []
                
                # 使用线程池
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    for stock_code, stock_df in tqdm(stock_groups, desc="处理股票特征"):
                        try:
                            enhanced_df = self._process_single_stock_features(stock_df)
                            all_enhanced.append(enhanced_df)
                        except Exception as e:
                            self.logger.error(f"线程池处理股票 {stock_code} 时出错: {str(e)}")
                            all_enhanced.append(stock_df)  # 如果处理失败，使用原始数据
        else:
            self.logger.info(f"股票数量较少或CPU核心数不足，使用顺序处理 {len(stock_groups)} 只股票...")
            for stock_code, stock_df in tqdm(stock_groups, desc="处理股票特征"):
                try:
                    enhanced_df = self._process_single_stock_features(stock_df)
                    all_enhanced.append(enhanced_df)
                except Exception as e:
                    self.logger.error(f"处理股票 {stock_code} 时出错: {str(e)}")
                    all_enhanced.append(stock_df)  # 如果处理失败，使用原始数据
        
        # 合并所有增强后的数据
        if all_enhanced:
            result_df = pd.concat(all_enhanced, ignore_index=False)
            self.logger.info(f"特征增强完成，添加了形态识别和技术分析特征")

            # 直接删除无效特征
            invalid_features = ['ma120', 'macd_neutral_zero_cross']  # 直接列出无效特征
            features_to_remove = []
            for feature in invalid_features:
                if feature in result_df.columns:
                    features_to_remove.append(feature)

            if features_to_remove:
                result_df = result_df.drop(columns=features_to_remove)
                self.logger.info(f"删除了无效特征: {features_to_remove}")

            # 检查future_return_2d列是否存在并包含有效值
            if 'future_return_2d' in result_df.columns:
                valid_pct = result_df['future_return_2d'].notna().sum() / len(result_df)
                self.logger.info(f"future_return_2d列有效值比例: {valid_pct:.2f}")
                if valid_pct < 0.5:
                    self.logger.warning(f"future_return_2d列有效值比例过低: {valid_pct:.2f}，可能存在计算问题")
            else:
                self.logger.warning(f"结果中缺少future_return_2d列，这可能会导致后续分析出错")

            self.logger.debug(f"增强后的特征列: {result_df.columns.tolist()}")
            return result_df
        else:
            self.logger.warning("没有成功增强任何股票特征")
            return features_df

    def _process_single_stock_features(self, stock_df):
        """处理单只股票的特征，用于并行处理 - 高性能优化版本"""
        try:
            stock_code = stock_df['stock_code'].iloc[0]

            # 快速检查数据完整性
            if len(stock_df) < 20 or not all(col in stock_df.columns for col in ['open', 'high', 'low', 'close']):
                return stock_df

            # 使用高性能的批量特征计算
            enhanced_df = self._calculate_features_vectorized(stock_df)

            return enhanced_df
        except Exception as e:
            stock_code = stock_df['stock_code'].iloc[0] if 'stock_code' in stock_df.columns else 'unknown'
            self.logger.error(f"处理股票 {stock_code} 特征时出错: {str(e)}")
            return stock_df  # 返回原始数据框

    def _calculate_features_vectorized(self, df):
        """
        高性能向量化特征计算 - 替代原有的多个复杂方法

        Args:
            df: 单只股票的DataFrame

        Returns:
            DataFrame: 添加了高效计算特征的数据
        """
        try:
            # 提取基础价格数据
            open_price = df['open'].values
            high_price = df['high'].values
            low_price = df['low'].values
            close_price = df['close'].values

            # 1. 快速K线形态特征（向量化）
            df = self._add_candlestick_features_fast(df, open_price, high_price, low_price, close_price)

            # 2. 快速趋势特征（向量化）
            df = self._add_trend_features_fast(df, close_price)

            # 3. 快速支撑阻力特征（向量化）
            df = self._add_support_resistance_fast(df, high_price, low_price, close_price)

            # 4. 快速状态标签（向量化）
            df = self._add_state_labels_fast(df)

            # 5. 计算未来收益率（优化版）
            df = self._calculate_future_returns_fast(df)

            return df

        except Exception as e:
            self.logger.error(f"向量化特征计算出错: {e}")
            return df

    def _add_candlestick_features_fast(self, df, open_price, high_price, low_price, close_price):
        """快速K线形态识别 - 向量化实现"""
        # 计算实体大小和影线（向量化）
        body_size = np.abs(close_price - open_price)
        upper_shadow = high_price - np.maximum(open_price, close_price)
        lower_shadow = np.minimum(open_price, close_price) - low_price
        total_range = high_price - low_price
        total_range = np.where(total_range == 0, 0.001, total_range)  # 避免除零

        # 向量化计算主要形态
        df['pattern_doji'] = (body_size / total_range < 0.1).astype(int)
        df['pattern_hammer'] = ((lower_shadow > 2 * body_size) &
                               (upper_shadow < 0.2 * total_range) &
                               (body_size / total_range < 0.3)).astype(int)
        df['pattern_shooting_star'] = ((upper_shadow > 2 * body_size) &
                                      (lower_shadow < 0.2 * total_range) &
                                      (body_size / total_range < 0.3)).astype(int)

        # 向量化的吞噬形态计算
        bullish_engulf = np.zeros(len(df))
        bearish_engulf = np.zeros(len(df))

        if len(df) > 1:
            # 向量化条件计算
            current_bullish = close_price[1:] > open_price[1:]  # 当日阳线
            prev_bearish = close_price[:-1] < open_price[:-1]   # 前日阴线
            engulf_up = close_price[1:] > open_price[:-1]       # 当日收盘高于前日开盘
            engulf_down = open_price[1:] < close_price[:-1]     # 当日开盘低于前日收盘

            # 看涨吞噬条件
            bullish_mask = current_bullish & prev_bearish & engulf_up & engulf_down
            bullish_engulf[1:] = bullish_mask.astype(int)

            # 看跌吞噬条件
            current_bearish = close_price[1:] < open_price[1:]  # 当日阴线
            prev_bullish = close_price[:-1] > open_price[:-1]   # 前日阳线
            engulf_down2 = close_price[1:] < open_price[:-1]    # 当日收盘低于前日开盘
            engulf_up2 = open_price[1:] > close_price[:-1]      # 当日开盘高于前日收盘

            bearish_mask = current_bearish & prev_bullish & engulf_down2 & engulf_up2
            bearish_engulf[1:] = bearish_mask.astype(int)

        df['pattern_engulfing_bullish'] = bullish_engulf
        df['pattern_engulfing_bearish'] = bearish_engulf

        # 添加缺失的复杂形态（简化版本）
        df = self._add_complex_patterns_fast(df, open_price, high_price, low_price, close_price)

        # 综合形态强度
        df['candlestick_strength'] = (df['pattern_doji'] + df['pattern_hammer'] +
                                     df['pattern_shooting_star'] + df['pattern_engulfing_bullish'] +
                                     df['pattern_engulfing_bearish'] + df.get('pattern_morning_star', 0) +
                                     df.get('pattern_evening_star', 0))

        return df

    def _add_complex_patterns_fast(self, df, open_price, high_price, low_price, close_price):
        """添加缺失的复杂K线形态 - 高性能版本"""
        # 初始化复杂形态列
        df['pattern_morning_star'] = 0
        df['pattern_evening_star'] = 0
        df['pattern_three_white_soldiers'] = 0
        df['pattern_three_black_crows'] = 0
        df['pattern_hanging_man'] = 0

        if len(df) < 3:
            return df

        # 计算实体大小
        body_size = np.abs(close_price - open_price)
        total_range = high_price - low_price
        total_range = np.where(total_range == 0, 0.001, total_range)

        # 简化的三日形态检测（向量化）
        for i in range(2, len(df)):
            try:
                # 启明星形态（简化版）
                if (close_price[i-2] < open_price[i-2] and  # 第一天阴线
                    body_size[i-1] / total_range[i-1] < 0.3 and  # 第二天小实体
                    close_price[i] > open_price[i] and  # 第三天阳线
                    close_price[i] > (open_price[i-2] + close_price[i-2]) / 2):  # 收盘价超过第一天中点
                    df.iloc[i, df.columns.get_loc('pattern_morning_star')] = 1

                # 黄昏星形态（简化版）
                if (close_price[i-2] > open_price[i-2] and  # 第一天阳线
                    body_size[i-1] / total_range[i-1] < 0.3 and  # 第二天小实体
                    close_price[i] < open_price[i] and  # 第三天阴线
                    close_price[i] < (open_price[i-2] + close_price[i-2]) / 2):  # 收盘价低于第一天中点
                    df.iloc[i, df.columns.get_loc('pattern_evening_star')] = 1

                # 三白兵形态（简化版）
                if (close_price[i-2] > open_price[i-2] and  # 连续三天阳线
                    close_price[i-1] > open_price[i-1] and
                    close_price[i] > open_price[i] and
                    close_price[i-1] > close_price[i-2] and  # 收盘价递增
                    close_price[i] > close_price[i-1]):
                    df.iloc[i, df.columns.get_loc('pattern_three_white_soldiers')] = 1

                # 三黑鸦形态（简化版）
                if (close_price[i-2] < open_price[i-2] and  # 连续三天阴线
                    close_price[i-1] < open_price[i-1] and
                    close_price[i] < open_price[i] and
                    close_price[i-1] < close_price[i-2] and  # 收盘价递减
                    close_price[i] < close_price[i-1]):
                    df.iloc[i, df.columns.get_loc('pattern_three_black_crows')] = 1

            except Exception:
                # 静默处理错误
                continue

        # 上吊线形态（与锤子线类似，但出现在上升趋势中）
        lower_shadow = np.minimum(open_price, close_price) - low_price
        upper_shadow = high_price - np.maximum(open_price, close_price)

        hanging_man_mask = ((lower_shadow > 2 * body_size) &
                           (upper_shadow < 0.2 * total_range) &
                           (body_size / total_range < 0.3))
        df['pattern_hanging_man'] = hanging_man_mask.astype(int)

        return df

    def _add_trend_features_fast(self, df, close_price):
        """快速趋势特征计算 - 向量化实现"""
        # 计算移动平均线（向量化）
        df['ma5'] = df['close'].rolling(window=5, min_periods=1).mean()
        df['ma20'] = df['close'].rolling(window=20, min_periods=1).mean()
        df['ma60'] = df['close'].rolling(window=60, min_periods=1).mean()

        # 计算斜率（向量化）
        df['ma20_slope'] = df['ma20'].pct_change(5)
        df['ma60_slope'] = df['ma60'].pct_change(10)

        # 简化的趋势状态（向量化）
        ma5 = df['ma5'].values
        ma20 = df['ma20'].values
        ma60 = df['ma60'].values
        ma20_slope = df['ma20_slope'].values

        # 趋势判断（向量化）
        uptrend = ((close_price > ma20) & (ma20 > ma60) & (ma20_slope > 0.002)).astype(int)
        downtrend = ((close_price < ma20) & (ma20 < ma60) & (ma20_slope < -0.002)).astype(int)

        df['trend_uptrend'] = uptrend
        df['trend_downtrend'] = downtrend
        df['trend_strength'] = np.abs(ma20_slope) * 100

        return df

    def _add_support_resistance_fast(self, df, high_price, low_price, close_price):
        """快速支撑阻力计算 - 向量化实现"""
        window = 20

        # 使用滚动窗口计算支撑阻力（向量化）
        df['support_level'] = df['low'].rolling(window=window, min_periods=1).quantile(0.1)
        df['resistance_level'] = df['high'].rolling(window=window, min_periods=1).quantile(0.9)

        # 计算距离（向量化）
        support = df['support_level'].values
        resistance = df['resistance_level'].values

        distance_to_support = np.maximum(0.001, (close_price - support) / close_price)
        distance_to_resistance = np.maximum(0.001, (resistance - close_price) / close_price)

        df['risk_reward_ratio'] = distance_to_resistance / distance_to_support
        df['support_distance_pct'] = distance_to_support * 100
        df['resistance_distance_pct'] = distance_to_resistance * 100

        return df

    def _add_state_labels_fast(self, df):
        """快速状态标签生成 - 向量化实现"""
        # 简化的状态判断（向量化）
        uptrend = df.get('trend_uptrend', 0)
        downtrend = df.get('trend_downtrend', 0)
        risk_reward = df.get('risk_reward_ratio', 1.0)
        candlestick_strength = df.get('candlestick_strength', 0)

        # 状态评分（向量化）
        state_score = (uptrend * 2 - downtrend * 2 +
                      np.where(risk_reward > 2, 1, 0) +
                      np.where(candlestick_strength > 0, 0.5, 0))

        # 状态映射
        df['stock_state_value'] = np.clip(state_score, -2, 2)

        return df

    def _calculate_future_returns_fast(self, df):
        """快速未来收益率计算 - 完全向量化版本"""
        if len(df) < 3:
            df['future_return_2d'] = np.nan
            return df

        try:
            # 完全向量化的未来收益率计算
            close_prices = df['close'].values

            # 使用numpy的向量化操作计算2日后收益率
            future_returns = np.full(len(close_prices), np.nan)

            # 向量化计算：避免循环
            valid_mask = (~np.isnan(close_prices)) & (close_prices > 0)

            # 计算2日后的价格（向前移位2位）
            future_prices = np.roll(close_prices, -2)
            future_valid_mask = np.roll(valid_mask, -2)

            # 只对有效数据计算收益率
            combined_mask = valid_mask & future_valid_mask
            combined_mask[-2:] = False  # 最后两个位置没有未来数据

            # 向量化计算收益率
            future_returns[combined_mask] = (
                (future_prices[combined_mask] - close_prices[combined_mask]) /
                close_prices[combined_mask]
            )

            df['future_return_2d'] = future_returns

        except Exception:
            # 静默处理错误
            df['future_return_2d'] = np.nan

        return df

    def _calculate_future_returns(self, df):
        """
        计算个股未来收益率特征
        
        Args:
            df: 单只股票的DataFrame
            
        Returns:
            DataFrame: 添加了未来收益率的数据
        """
        stock_code = df['stock_code'].iloc[0] if 'stock_code' in df.columns and len(df) > 0 else "未知"
        self.logger.info(f"计算股票 {stock_code} 的未来收益率...")
        
        # 初始化future_return_2d列
        df['future_return_2d'] = np.nan
        
        try:
            # 确保交易日历已加载
            if self.trading_days is None or not self.trading_days:
                self._load_trading_calendar()
            
            if self.trading_days is None or not self.trading_days:
                self.logger.error("交易日历为空或加载失败，无法计算未来收益率")
                return df
                
            # 确保按日期排序
            if 'date_str' not in df.columns:
                self.logger.warning(f"股票 {stock_code} 没有date_str列，无法计算未来收益率")
                return df
                
            df = df.sort_values('date_str')
            
            # 检查是否有close列
            if 'close' not in df.columns:
                self.logger.warning(f"股票 {stock_code} 没有close列，无法计算未来收益率")
                return df
                
            # 获取所有日期和它们在交易日历中的索引
            date_indices = {}
            valid_dates = 0
            
            for i, date_str in enumerate(df['date_str']):
                # 确保日期字符串格式正确
                try:
                    if isinstance(date_str, pd.Timestamp):
                        date_str = date_str.strftime('%Y%m%d')
                    elif not isinstance(date_str, str):
                        date_str = str(date_str)
                        
                    # 如果日期在交易日历中，记录其索引
                    if date_str in self.trading_days:
                        date_indices[i] = self.trading_days.index(date_str)
                        valid_dates += 1
                except Exception as e:
                    self.logger.warning(f"处理日期 {date_str} 时出错: {e}")
                    continue
            
            if valid_dates == 0:
                self.logger.warning(f"股票 {stock_code} 没有有效的交易日期，无法计算未来收益率")
                return df
                
            self.logger.info(f"找到 {valid_dates} 个有效交易日期")
            
            # 计算未来2天收益率
            calculated_returns = 0
            for i in range(len(df)):
                if i not in date_indices:
                    continue
                    
                current_idx = date_indices[i]
                
                # 确保当前行有效
                try:
                    current_close = df.iloc[i]['close']
                    
                    if pd.isna(current_close) or current_close <= 0:
                        continue
                        
                    # 检查是否有足够的未来交易日
                    if current_idx + 2 < len(self.trading_days):
                        future_date_str = self.trading_days[current_idx + 2]
                        future_row = df[df['date_str'] == future_date_str]
                        
                        if not future_row.empty:
                            future_close = future_row.iloc[0]['close']
                            if not pd.isna(future_close) and future_close > 0:
                                # 计算收益率
                                future_return = (future_close - current_close) / current_close
                                df.iloc[i, df.columns.get_loc('future_return_2d')] = future_return
                                calculated_returns += 1
                except Exception as e:
                    self.logger.warning(f"计算第 {i} 行的未来收益率时出错: {e}")
                    continue
                            
            valid_ratio = df['future_return_2d'].notna().sum() / len(df) if len(df) > 0 else 0
            self.logger.info(f"股票 {stock_code} 的未来收益率计算完成，计算了 {calculated_returns} 个值，有效值比例: {valid_ratio:.2f}")
            
            # 检查是否没有计算出任何有效值
            if calculated_returns == 0:
                self.logger.warning(f"股票 {stock_code} 未能计算出任何有效的未来收益率值")
                
        except Exception as e:
            self.logger.error(f"计算股票 {stock_code} 的未来收益率时出错: {e}")
            self.logger.error(traceback.format_exc())
            
        return df
        
    def _detect_candlestick_patterns(self, df):
        """
        检测K线形态
        
        Args:
            df: 单只股票的DataFrame
            
        Returns:
            DataFrame: 添加K线形态特征的数据
        """
        # 初始化形态列
        pattern_columns = [
            'pattern_doji', 'pattern_hammer', 'pattern_inverted_hammer', 
            'pattern_engulfing_bullish', 'pattern_engulfing_bearish',
            'pattern_morning_star', 'pattern_evening_star',
            'pattern_three_white_soldiers', 'pattern_three_black_crows',
            'pattern_hanging_man', 'pattern_shooting_star'
        ]
        
        for col in pattern_columns:
            df[col] = 0
            
        # 提取价格数据
        open_price = df['open'].values
        high_price = df['high'].values
        low_price = df['low'].values
        close_price = df['close'].values
        
        # 计算实体大小和影线
        body_size = np.abs(close_price - open_price)
        upper_shadow = high_price - np.maximum(open_price, close_price)
        lower_shadow = np.minimum(open_price, close_price) - low_price
        total_range = high_price - low_price
        
        # 避免除零错误
        total_range = np.where(total_range == 0, 0.001, total_range)
        
        # 1. 十字星(Doji) - 开盘价和收盘价几乎相同
        df['pattern_doji'] = np.where(body_size / total_range < 0.1, 1, 0)
        
        # 2. 锤子线(Hammer) - 下影线长，上影线短，实体小
        df['pattern_hammer'] = np.where(
            (lower_shadow > 2 * body_size) & 
            (upper_shadow < 0.2 * total_range) & 
            (body_size / total_range < 0.3),
            1, 0
        )
        
        # 3. 倒锤子线(Inverted Hammer)
        df['pattern_inverted_hammer'] = np.where(
            (upper_shadow > 2 * body_size) & 
            (lower_shadow < 0.2 * total_range) & 
            (body_size / total_range < 0.3),
            1, 0
        )
        
        # 4. 吞噬形态(Engulfing Pattern)
        for i in range(1, len(df)):
            # 4.1 看涨吞噬
            if (close_price[i] > open_price[i] and  # 当日阳线
                close_price[i-1] < open_price[i-1] and  # 前日阴线
                close_price[i] > open_price[i-1] and  # 当日收盘价高于前日开盘价
                open_price[i] < close_price[i-1]):  # 当日开盘价低于前日收盘价
                df.iloc[i, df.columns.get_loc('pattern_engulfing_bullish')] = 1

            # 4.2 看跌吞噬
            if (close_price[i] < open_price[i] and  # 当日阴线
                close_price[i-1] > open_price[i-1] and  # 前日阳线
                close_price[i] < open_price[i-1] and  # 当日收盘价低于前日开盘价
                open_price[i] > close_price[i-1]):  # 当日开盘价高于前日收盘价
                df.iloc[i, df.columns.get_loc('pattern_engulfing_bearish')] = 1

        # 5. 三日形态检测 (需要至少3天数据)
        for i in range(2, len(df)):
            # 5.1 启明星 (Morning Star) - 看涨反转形态
            # 第一天：阴线，第二天：十字星或小实体，第三天：阳线
            if (close_price[i-2] < open_price[i-2] and  # 第一天阴线
                abs(close_price[i-1] - open_price[i-1]) / total_range[i-1] < 0.3 and  # 第二天小实体
                close_price[i] > open_price[i] and  # 第三天阳线
                close_price[i] > (open_price[i-2] + close_price[i-2]) / 2):  # 第三天收盘价超过第一天实体中点
                df.iloc[i, df.columns.get_loc('pattern_morning_star')] = 1

            # 5.2 黄昏星 (Evening Star) - 看跌反转形态
            # 第一天：阳线，第二天：十字星或小实体，第三天：阴线
            if (close_price[i-2] > open_price[i-2] and  # 第一天阳线
                abs(close_price[i-1] - open_price[i-1]) / total_range[i-1] < 0.3 and  # 第二天小实体
                close_price[i] < open_price[i] and  # 第三天阴线
                close_price[i] < (open_price[i-2] + close_price[i-2]) / 2):  # 第三天收盘价低于第一天实体中点
                df.iloc[i, df.columns.get_loc('pattern_evening_star')] = 1

            # 5.3 三白兵 (Three White Soldiers) - 强烈看涨形态
            # 连续三天阳线，每天收盘价都比前一天高，实体逐渐增大
            if (close_price[i-2] > open_price[i-2] and  # 第一天阳线
                close_price[i-1] > open_price[i-1] and  # 第二天阳线
                close_price[i] > open_price[i] and  # 第三天阳线
                close_price[i-1] > close_price[i-2] and  # 第二天收盘价高于第一天
                close_price[i] > close_price[i-1] and  # 第三天收盘价高于第二天
                body_size[i-1] >= body_size[i-2] and  # 实体逐渐增大
                body_size[i] >= body_size[i-1]):
                df.iloc[i, df.columns.get_loc('pattern_three_white_soldiers')] = 1

            # 5.4 三黑鸦 (Three Black Crows) - 强烈看跌形态
            # 连续三天阴线，每天收盘价都比前一天低，实体逐渐增大
            if (close_price[i-2] < open_price[i-2] and  # 第一天阴线
                close_price[i-1] < open_price[i-1] and  # 第二天阴线
                close_price[i] < open_price[i] and  # 第三天阴线
                close_price[i-1] < close_price[i-2] and  # 第二天收盘价低于第一天
                close_price[i] < close_price[i-1] and  # 第三天收盘价低于第二天
                body_size[i-1] >= body_size[i-2] and  # 实体逐渐增大
                body_size[i] >= body_size[i-1]):
                df.iloc[i, df.columns.get_loc('pattern_three_black_crows')] = 1

        # 6. 上吊线和流星线检测 (单日形态)
        for i in range(len(df)):
            # 6.1 上吊线 (Hanging Man) - 在上升趋势中出现的看跌信号
            # 特征：下影线长，上影线短，实体小，出现在高位
            if (lower_shadow[i] > 2 * body_size[i] and  # 下影线长于实体的2倍
                upper_shadow[i] < 0.2 * total_range[i] and  # 上影线很短
                body_size[i] / total_range[i] < 0.3):  # 实体相对较小
                # 检查是否在相对高位 (近期价格的上半部分)
                if i >= 10:
                    recent_high = np.max(close_price[max(0, i-10):i+1])
                    recent_low = np.min(close_price[max(0, i-10):i+1])
                    if close_price[i] > recent_low + (recent_high - recent_low) * 0.7:
                        df.iloc[i, df.columns.get_loc('pattern_hanging_man')] = 1

            # 6.2 流星线 (Shooting Star) - 在上升趋势中出现的看跌信号
            # 特征：上影线长，下影线短，实体小，出现在高位
            if (upper_shadow[i] > 2 * body_size[i] and  # 上影线长于实体的2倍
                lower_shadow[i] < 0.2 * total_range[i] and  # 下影线很短
                body_size[i] / total_range[i] < 0.3):  # 实体相对较小
                # 检查是否在相对高位
                if i >= 10:
                    recent_high = np.max(close_price[max(0, i-10):i+1])
                    recent_low = np.min(close_price[max(0, i-10):i+1])
                    if close_price[i] > recent_low + (recent_high - recent_low) * 0.7:
                        df.iloc[i, df.columns.get_loc('pattern_shooting_star')] = 1
        
        # 综合形态强度
        df['candlestick_pattern_strength'] = df[pattern_columns].sum(axis=1)
        
        return df
        
    def _detect_chart_patterns(self, df):
        """
        检测经典图表形态，如头肩顶/底、双顶/双底、三角形等
        
        Args:
            df: 单只股票的DataFrame
            
        Returns:
            DataFrame: 添加图表形态特征的数据
        """
        # 初始化形态列
        pattern_columns = [
            'pattern_head_shoulders', 'pattern_inverse_head_shoulders',
            'pattern_double_top', 'pattern_double_bottom',
            'pattern_triangle_ascending', 'pattern_triangle_descending',
            'pattern_triangle_symmetrical', 'pattern_flag_bullish', 'pattern_flag_bearish'
        ]
        
        for col in pattern_columns:
            df[col] = 0
        
        # 检测窗口大小 - 增大窗口以更好地捕捉复杂形态
        window_size = 30
        if len(df) < window_size:
            return df
        
        # 提取价格数据
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values
        volume = df['volume'].values if 'volume' in df.columns else None
        
        # 使用滑动窗口检测形态
        for i in range(window_size, len(df)):
            try:
                window_close = close[i-window_size:i+1]
                window_high = high[i-window_size:i+1]
                window_low = low[i-window_size:i+1]
                window_volume = volume[i-window_size:i+1] if volume is not None else None
                
                # 查找局部极值点(峰和谷)
                peaks = self._find_peaks(window_close)
                troughs = self._find_peaks(-window_close)  # 通过取反找到低谷
                
                # 1. 检测头肩顶形态 (看跌反转形态)
                # 头肩顶需要三个峰，中间的峰(头部)最高，两侧的峰(肩部)高度相近
                if len(peaks) >= 3:
                    # 获取最近的三个峰
                    last_three_peaks = sorted(peaks[-3:])
                    if len(last_three_peaks) == 3:
                        left_shoulder, head, right_shoulder = last_three_peaks
                        
                        # 确保头部高于肩部
                        if (window_close[head] > window_close[left_shoulder] and 
                            window_close[head] > window_close[right_shoulder]):
                            
                            # 确保两肩高度相近 (允许10%的差异)
                            shoulder_diff = abs(window_close[left_shoulder] - window_close[right_shoulder])
                            if shoulder_diff / window_close[left_shoulder] < 0.1:
                                
                                # 确保有颈线 (两肩之间的低点连线)
                                # 至少需要一个低点在左肩和头部之间，一个在头部和右肩之间
                                left_troughs = [t for t in troughs if left_shoulder < t < head]
                                right_troughs = [t for t in troughs if head < t < right_shoulder]
                                
                                if left_troughs and right_troughs:
                                    # 颈线应该大致水平
                                    neck_left = window_close[left_troughs[-1]]
                                    neck_right = window_close[right_troughs[0]]
                                    
                                    if abs(neck_left - neck_right) / neck_left < 0.05:
                                        # 检查最后一个价格是否接近或低于颈线，表示形态已完成
                                        if window_close[-1] <= neck_right * 1.01:
                                            df.iloc[i, df.columns.get_loc('pattern_head_shoulders')] = 1
                
                # 2. 检测头肩底形态 (看涨反转形态)
                if len(troughs) >= 3:
                    # 获取最近的三个低谷
                    last_three_troughs = sorted(troughs[-3:])
                    if len(last_three_troughs) == 3:
                        left_shoulder, head, right_shoulder = last_three_troughs
                        
                        # 确保头部低于肩部
                        if (window_close[head] < window_close[left_shoulder] and 
                            window_close[head] < window_close[right_shoulder]):
                            
                            # 确保两肩高度相近
                            shoulder_diff = abs(window_close[left_shoulder] - window_close[right_shoulder])
                            if shoulder_diff / window_close[left_shoulder] < 0.1:
                                
                                # 确保有颈线 (两肩之间的高点连线)
                                left_peaks = [p for p in peaks if left_shoulder < p < head]
                                right_peaks = [p for p in peaks if head < p < right_shoulder]
                                
                                if left_peaks and right_peaks:
                                    # 颈线应该大致水平
                                    neck_left = window_close[left_peaks[-1]]
                                    neck_right = window_close[right_peaks[0]]
                                    
                                    if abs(neck_left - neck_right) / neck_left < 0.05:
                                        # 检查最后一个价格是否接近或高于颈线，表示形态已完成
                                        if window_close[-1] >= neck_right * 0.99:
                                            df.iloc[i, df.columns.get_loc('pattern_inverse_head_shoulders')] = 1
                
                # 3. 检测双顶形态 (看跌反转形态)
                if len(peaks) >= 2:
                    # 获取最近的两个峰
                    last_two_peaks = sorted(peaks[-2:])
                    if len(last_two_peaks) == 2:
                        peak1, peak2 = last_two_peaks
                        
                        # 确保两个顶点高度相近 (允许3%的差异)
                        if abs(window_close[peak1] - window_close[peak2]) / window_close[peak1] < 0.03:
                            # 确保两峰之间有明显的低谷
                            between_troughs = [t for t in troughs if peak1 < t < peak2]
                            if between_troughs:
                                trough = between_troughs[0]  # 使用最显著的低谷
                                
                                # 确保低谷显著低于两个顶点
                                if (window_close[trough] < window_close[peak1] * 0.97 and
                                    window_close[trough] < window_close[peak2] * 0.97):
                                    
                                    # 检查形态是否已完成 - 价格跌破颈线(支撑位)
                                    if window_close[-1] < window_close[trough]:
                                        df.iloc[i, df.columns.get_loc('pattern_double_top')] = 1
            
                # 4. 检测双底形态 (看涨反转形态)
                if len(troughs) >= 2:
                    # 获取最近的两个谷
                    last_two_troughs = sorted(troughs[-2:])
                    if len(last_two_troughs) == 2:
                        trough1, trough2 = last_two_troughs
                        
                        # 确保两个底点高度相近
                        if abs(window_close[trough1] - window_close[trough2]) / window_close[trough1] < 0.03:
                            # 确保两谷之间有明显的高点
                            between_peaks = [p for p in peaks if trough1 < p < trough2]
                            if between_peaks:
                                peak = between_peaks[0]
                                
                                # 确保高点显著高于两个底点
                                if (window_close[peak] > window_close[trough1] * 1.03 and
                                    window_close[peak] > window_close[trough2] * 1.03):
                                    
                                    # 检查形态是否已完成 - 价格突破颈线(阻力位)
                                    if window_close[-1] > window_close[peak]:
                                        df.iloc[i, df.columns.get_loc('pattern_double_bottom')] = 1
            
                # 5. 检测上升三角形形态 (看涨持续形态)
                if len(peaks) >= 2 and len(troughs) >= 2:
                    # 上升三角形有平直的顶部阻力位和上升的底部支撑位
                    recent_peaks = sorted(peaks[-3:]) if len(peaks) >= 3 else sorted(peaks[-2:])
                    recent_troughs = sorted(troughs[-3:]) if len(troughs) >= 3 else sorted(troughs[-2:])
                    
                    if len(recent_peaks) >= 2 and len(recent_troughs) >= 2:
                        # 检查顶部是否相对平直 (高点大致相同)
                        peak_values = [window_close[p] for p in recent_peaks]
                        if max(peak_values) - min(peak_values) < min(peak_values) * 0.03:
                            # 检查底部是否上升 (低点逐渐抬高)
                            trough_values = [window_close[t] for t in sorted(recent_troughs)]
                            is_ascending = True
                            for j in range(1, len(trough_values)):
                                if trough_values[j] < trough_values[j-1]:
                                    is_ascending = False
                                    break
                                    
                            if is_ascending and trough_values[-1] > trough_values[0] * 1.02:
                                # 检查价格是否接近形态顶部
                                resistance = np.mean(peak_values)
                                if window_close[-1] > resistance * 0.97:
                                    df.iloc[i, df.columns.get_loc('pattern_triangle_ascending')] = 1
                
                # 6. 检测下降三角形形态 (看跌持续形态)
                if len(peaks) >= 2 and len(troughs) >= 2:
                    # 下降三角形有下降的顶部阻力位和平直的底部支撑位
                    recent_peaks = sorted(peaks[-3:]) if len(peaks) >= 3 else sorted(peaks[-2:])
                    recent_troughs = sorted(troughs[-3:]) if len(troughs) >= 3 else sorted(troughs[-2:])
                    
                    if len(recent_peaks) >= 2 and len(recent_troughs) >= 2:
                        # 检查底部是否相对平直
                        trough_values = [window_close[t] for t in recent_troughs]
                        if max(trough_values) - min(trough_values) < min(trough_values) * 0.03:
                            # 检查顶部是否下降
                            peak_values = [window_close[p] for p in sorted(recent_peaks)]
                            is_descending = True
                            for j in range(1, len(peak_values)):
                                if peak_values[j] > peak_values[j-1]:
                                    is_descending = False
                                    break
                                    
                            if is_descending and peak_values[-1] < peak_values[0] * 0.98:
                                # 检查价格是否接近形态底部
                                support = np.mean(trough_values)
                                if window_close[-1] < support * 1.03:
                                    df.iloc[i, df.columns.get_loc('pattern_triangle_descending')] = 1
                
                # 7. 检测对称三角形形态
                if len(peaks) >= 3 and len(troughs) >= 3:
                    # 对称三角形有下降的顶部和上升的底部
                    peak_values = [window_close[p] for p in sorted(peaks[-3:])]
                    trough_values = [window_close[t] for t in sorted(troughs[-3:])]
                    
                    # 检查顶部是否下降
                    is_tops_descending = True
                    for j in range(1, len(peak_values)):
                        if peak_values[j] > peak_values[j-1]:
                            is_tops_descending = False
                            break
                    
                    # 检查底部是否上升
                    is_bottoms_ascending = True
                    for j in range(1, len(trough_values)):
                        if trough_values[j] < trough_values[j-1]:
                            is_bottoms_ascending = False
                            break
                    
                    if is_tops_descending and is_bottoms_ascending:
                        # 对称三角形收敛，计算收敛点
                        diff_first = peak_values[0] - trough_values[0]
                        diff_last = peak_values[-1] - trough_values[-1]
                        
                        # 收敛度检查 - 末端差异应该小于起始差异
                        if diff_last < diff_first * 0.7:
                            # 检查价格是否接近收敛点
                            midpoint = (peak_values[-1] + trough_values[-1]) / 2
                            if abs(window_close[-1] - midpoint) < diff_last * 0.5:
                                df.iloc[i, df.columns.get_loc('pattern_triangle_symmetrical')] = 1
                
                # 8. 检测看涨旗形 (Bull Flag) - 上涨趋势中的短期回调整理
                if len(window_close) >= 20:  # 确保有足够数据点
                    # 检查前段是否有明显上涨趋势
                    uptrend_window = window_close[:15]
                    uptrend_start = uptrend_window[0]
                    uptrend_end = uptrend_window[-1]
                    
                    if uptrend_end > uptrend_start * 1.05:  # 至少5%的上涨
                        # 检查最近部分是否横盘或小幅下跌
                        flag_window = window_close[15:]
                        flag_start = flag_window[0]
                        flag_end = flag_window[-1]
                        
                        # 旗形应该在上涨之后有小幅回调或横盘整理
                        if flag_end > flag_start * 0.95 and flag_end < flag_start * 1.02:
                            # 最后一个价格应该接近突破整理区间上沿
                            if window_close[-1] > max(flag_window[:-1]) * 0.98:
                                df.iloc[i, df.columns.get_loc('pattern_flag_bullish')] = 1
                
                # 9. 检测看跌旗形 (Bear Flag) - 下跌趋势中的短期反弹整理
                if len(window_close) >= 20:
                    # 检查前段是否有明显下跌趋势
                    downtrend_window = window_close[:15]
                    downtrend_start = downtrend_window[0]
                    downtrend_end = downtrend_window[-1]
                    
                    if downtrend_end < downtrend_start * 0.95:  # 至少5%的下跌
                        # 检查最近部分是否横盘或小幅上涨
                        flag_window = window_close[15:]
                        flag_start = flag_window[0]
                        flag_end = flag_window[-1]
                        
                        # 旗形应该在下跌之后有小幅反弹或横盘整理
                        if flag_end < flag_start * 1.05 and flag_end > flag_start * 0.98:
                            # 最后一个价格应该接近突破整理区间下沿
                            if window_close[-1] < min(flag_window[:-1]) * 1.02:
                                df.iloc[i, df.columns.get_loc('pattern_flag_bearish')] = 1
            
            except Exception as e:
                self.logger.warning(f"检测图表形态时出错: {e}")
                continue
        
        # 综合形态强度
        df['chart_pattern_strength'] = df[pattern_columns].sum(axis=1)
        
        return df
    
    def _find_peaks(self, prices, min_distance=5, prominence=0.01, width=1):
        """
        寻找价格序列中的峰值点
        
        Args:
            prices: 价格序列
            min_distance: 峰值间最小距离
            prominence: 最小峰值凸显度(相对高度)
            width: 最小峰值宽度
            
        Returns:
            list: 峰值索引列表
        """
        try:
            from scipy.signal import find_peaks, peak_prominences

            # 确保数据足够长
            if len(prices) < min_distance * 2:
                return []

            # 计算平均价格用于设置相对prominence
            mean_price = np.mean(prices)
            abs_prominence = prominence * mean_price

            # 找到峰值点
            peaks, properties = find_peaks(
                prices, 
                distance=min_distance,
                prominence=abs_prominence,
                width=width
            )
            
            # 如果没有找到足够的峰值，可以尝试降低要求
            if len(peaks) < 2 and len(prices) >= 20:
                # 降低突出度要求
                peaks, properties = find_peaks(
                    prices, 
                    distance=min(3, min_distance),
                    prominence=abs_prominence * 0.5,
                    width=width
                )
                
            # 按照突出度对峰值排序
            if len(peaks) > 0:
                prominences = peak_prominences(prices, peaks)[0]
                # 根据突出度对峰值排序（降序）
                sorted_indices = np.argsort(-prominences)
                peaks = peaks[sorted_indices]
                
            return peaks.tolist()
            
        except (ImportError, Exception) as e:
            self.logger.warning(f"峰值检测出错: {e}，使用简单算法替代")
            # 简单的峰值检测算法作为备选
            result = []
            if len(prices) < 7:  # 数据太少
                return result
                
            for i in range(3, len(prices) - 3):
                # 当前点高于其左右3个点则视为峰值
                if (prices[i] > prices[i-1] and prices[i] > prices[i-2] and prices[i] > prices[i-3] and 
                    prices[i] > prices[i+1] and prices[i] > prices[i+2] and prices[i] > prices[i+3]):
                    result.append(i)
                    
            return result
        
    def _analyze_price_trend(self, df):
        """
        分析价格趋势状态：上升趋势、下降趋势、盘整
        
        Args:
            df: 单只股票的DataFrame
            
        Returns:
            DataFrame: 添加趋势状态的数据
        """
        # 确保必要的列存在
        if 'ma20' not in df.columns or 'ma60' not in df.columns:
            if 'close' in df.columns:
                # 计算移动平均线
                df['ma20'] = df['close'].rolling(window=20).mean()
                df['ma60'] = df['close'].rolling(window=60).mean()
            else:
                return df
        
        # 初始化趋势状态列
        df['trend_state'] = 'unknown'
        df['trend_strength'] = 0.0
        
        # 至少需要60个数据点来确定趋势
        if len(df) < 60:
            return df
            
        # 计算短期和长期移动平均线的斜率
        df['ma20_slope'] = df['ma20'].diff(5) / df['ma20'].shift(5)
        df['ma60_slope'] = df['ma60'].diff(10) / df['ma60'].shift(10)
        
        # 定义趋势状态
        for i in range(60, len(df)):
            current_price = df.iloc[i]['close']
            ma20 = df.iloc[i]['ma20']
            ma60 = df.iloc[i]['ma60']
            ma20_slope = df.iloc[i]['ma20_slope']
            ma60_slope = df.iloc[i]['ma60_slope']
            
            # 上升趋势: 价格在均线上方，短期均线向上，长期均线向上或水平
            if (current_price > ma20 > ma60 and 
                ma20_slope > 0.002 and ma60_slope > -0.0005):
                trend_state = 'uptrend'
                # 强度由斜率决定
                trend_strength = min(1.0, (ma20_slope * 100 + ma60_slope * 50))
                
            # 下降趋势: 价格在均线下方，短期均线向下，长期均线向下或水平
            elif (current_price < ma20 < ma60 and 
                  ma20_slope < -0.002 and ma60_slope < 0.0005):
                trend_state = 'downtrend'
                # 强度由斜率绝对值决定
                trend_strength = min(1.0, (abs(ma20_slope) * 100 + abs(ma60_slope) * 50))
                
            # 盘整：价格在短期和长期均线之间波动，均线斜率接近水平
            elif (abs(ma20_slope) < 0.003 and abs(ma60_slope) < 0.002 and
                  abs((current_price / ma20) - 1) < 0.03):
                trend_state = 'consolidation'
                # 盘整强度由价格与均线的接近程度决定
                trend_strength = 1.0 - abs((current_price / ma20) - 1) * 33
                
            # 突破：价格刚刚突破均线，短期均线拐头向上
            elif (current_price > ma20 and 
                  df.iloc[i-1]['close'] <= df.iloc[i-1]['ma20'] and
                  ma20_slope > 0):
                trend_state = 'breakout'
                # 突破强度由价格突破幅度和成交量决定
                price_breakout = (current_price / ma20) - 1
                
                # 计算成交量增加倍数，添加安全检查避免除以零
                avg_vol = df.iloc[i-5:i]['volume'].mean()
                if avg_vol > 0:
                    vol_increase = df.iloc[i]['volume'] / avg_vol
                else:
                    vol_increase = 1.0  # 当平均成交量为零时使用默认值
                
                trend_strength = min(1.0, price_breakout * 20 + (vol_increase - 1) * 0.5)
                
            # 假突破：价格突破后又回落到均线下方
            elif (current_price < ma20 and 
                  df.iloc[i-1]['close'] > df.iloc[i-1]['ma20'] and
                  df.iloc[i-2]['close'] > df.iloc[i-2]['ma20'] and
                  ma20_slope < 0):
                trend_state = 'false_breakout'
                # 假突破强度由回落幅度决定
                trend_strength = min(1.0, abs(1 - (current_price / ma20)) * 20)
                
            else:
                trend_state = 'unclear'
                trend_strength = 0.2
                
            df.iloc[i, df.columns.get_loc('trend_state')] = trend_state
            df.iloc[i, df.columns.get_loc('trend_strength')] = trend_strength
            
        # 将趋势状态转换为数值型特征，便于模型使用
        trend_mapping = {
            'uptrend': 2, 
            'breakout': 1, 
            'consolidation': 0, 
            'false_breakout': -1, 
            'downtrend': -2,
            'unclear': 0
        }
        df['trend_state_value'] = df['trend_state'].map(trend_mapping)
        
        return df
        
    def _calculate_risk_reward_ratios(self, df):
        """
        计算支撑位和阻力位，并转换为盈亏比
        
        Args:
            df: 单只股票的DataFrame
            
        Returns:
            DataFrame: 添加支撑位和阻力位盈亏比的数据
        """
        # 确保必要的列存在
        if 'close' not in df.columns or 'high' not in df.columns or 'low' not in df.columns:
            return df
            
        # 窗口大小
        window_size = 20
        if len(df) < window_size:
            return df
            
        # 初始化支撑位和阻力位列
        df['support_level'] = np.nan
        df['resistance_level'] = np.nan
        df['risk_reward_ratio'] = np.nan
        
        # 使用滑动窗口计算支撑位和阻力位
        for i in range(window_size, len(df)):
            window_close = df.iloc[i-window_size:i]['close'].values
            window_high = df.iloc[i-window_size:i]['high'].values
            window_low = df.iloc[i-window_size:i]['low'].values
            
            # 找出低点和高点
            support_level = np.percentile(window_low, 10)  # 10%分位数作为支撑位
            resistance_level = np.percentile(window_high, 90)  # 90%分位数作为阻力位
            
            current_price = df.iloc[i]['close']
            
            # 计算到支撑位和阻力位的距离
            distance_to_support = (current_price - support_level) / current_price
            distance_to_resistance = (resistance_level - current_price) / current_price
            
            # 确保距离为正值且不为零
            distance_to_support = max(0.001, distance_to_support)
            distance_to_resistance = max(0.001, distance_to_resistance)
            
            # 计算盈亏比 = 潜在收益/潜在风险
            risk_reward_ratio = distance_to_resistance / distance_to_support
            
            df.iloc[i, df.columns.get_loc('support_level')] = support_level
            df.iloc[i, df.columns.get_loc('resistance_level')] = resistance_level
            df.iloc[i, df.columns.get_loc('risk_reward_ratio')] = risk_reward_ratio
            
        # 添加相对支撑/阻力强度
        df['support_strength'] = 0.0
        df['resistance_strength'] = 0.0
        
        # 计算支撑和阻力的强度
        for i in range(window_size, len(df)):
            if i >= 2 * window_size:
                # 检查过去的价格是否曾在支撑位附近反弹
                support_level = df.iloc[i]['support_level']
                support_touches = sum(1 for j in range(i-window_size, i) 
                                     if abs(df.iloc[j]['low'] - support_level) / support_level < 0.01)
                df.iloc[i, df.columns.get_loc('support_strength')] = min(1.0, support_touches / 5)
                
                # 检查过去的价格是否曾在阻力位附近回落
                resistance_level = df.iloc[i]['resistance_level']
                resistance_touches = sum(1 for j in range(i-window_size, i) 
                                        if abs(df.iloc[j]['high'] - resistance_level) / resistance_level < 0.01)
                df.iloc[i, df.columns.get_loc('resistance_strength')] = min(1.0, resistance_touches / 5)
        
        return df
        
   
        
    def _generate_state_labels(self, df):
        """
        生成综合状态标签，用于分类股票当前所处的市场状态
        
        Args:
            df: 单只股票的DataFrame
            
        Returns:
            DataFrame: 添加综合状态标签的数据
        """
        # 初始化状态标签列
        df['stock_state'] = 'neutral'
        
        # 至少需要20个数据点
        if len(df) < 20:
            return df
            
        # 综合多个特征确定状态
        for i in range(20, len(df)):
            # 获取当前行数据
            row = df.iloc[i]
            
            # 检查是否有趋势状态
            if 'trend_state' in df.columns:
                trend = row['trend_state']
            else:
                trend = 'unknown'
                
            # 检查K线形态
            bullish_patterns = sum([
                row.get('pattern_hammer', 0),
                row.get('pattern_engulfing_bullish', 0),
                row.get('pattern_morning_star', 0),
                row.get('pattern_three_white_soldiers', 0)
            ])
            
            bearish_patterns = sum([
                row.get('pattern_hanging_man', 0),
                row.get('pattern_engulfing_bearish', 0),
                row.get('pattern_evening_star', 0),
                row.get('pattern_three_black_crows', 0)
            ])
            
            # 检查风险收益比
            risk_reward = row.get('risk_reward_ratio', 1.0)
            
            # 检查RSI超买超卖
            rsi_value = row.get('rsi12', 50)
            
            # 检查成交量异常
            volume_ratio = row.get('volume_ratio', 1.0)
            
            # 结合各种特征确定状态
            if trend == 'uptrend' and bullish_patterns >= 1 and risk_reward > 2.0:
                state = 'strong_buy'
            elif trend == 'breakout' and volume_ratio > 1.5:
                state = 'breakout_buy'
            elif trend == 'uptrend' or (bullish_patterns >= 1 and rsi_value < 70):
                state = 'buy'
            elif trend == 'downtrend' and bearish_patterns >= 1 and risk_reward < 0.5:
                state = 'strong_sell'
            elif trend == 'false_breakout' and volume_ratio > 1.5:
                state = 'false_breakout_sell'
            elif trend == 'downtrend' or (bearish_patterns >= 1 and rsi_value > 30):
                state = 'sell'
            elif trend == 'consolidation' and 0.8 < risk_reward < 1.2:
                state = 'consolidation'
            elif rsi_value < 30 and risk_reward > 1.5:
                state = 'oversold'
            elif rsi_value > 70 and risk_reward < 0.7:
                state = 'overbought'
            else:
                state = 'neutral'
                
            df.iloc[i, df.columns.get_loc('stock_state')] = state
            
        # 将状态转换为数值，便于模型使用
        state_mapping = {
            'strong_buy': 2, 
            'breakout_buy': 1.5,
            'buy': 1, 
            'neutral': 0, 
            'sell': -1, 
            'false_breakout_sell': -1.5,
            'strong_sell': -2,
            'consolidation': 0,
            'oversold': 0.5,
            'overbought': -0.5
        }
        df['stock_state_value'] = df['stock_state'].map(state_mapping)
        
        return df
            
    def _load_market_status_by_date(self, date):
        """
        加载指定日期的市场状态数据
        
        Args:
            date: 日期，格式为YYYYMMDD
            
        Returns:
            dict: 市场状态数据
        """
        try:
            # 将日期转换为字符串格式
            date_str = date
            if isinstance(date, pd.Timestamp):
                date_str = date.strftime('%Y%m%d')
                
            # 首先尝试从_load_all_market_status获取的数据中查找
            all_market_status = self._load_all_market_status()
            if all_market_status and date_str in all_market_status:
                return all_market_status[date_str]
            return None    
        except Exception as e:
            self.logger.warning(f"加载日期 {date} 的市场状态数据时出错: {e}")
            return None

    def _detect_stock_change_points(self, stock_features_df):
        """
        检测股票价格关键变化点
        
        Args:
            stock_features_df: 股票特征DataFrame
            
        Returns:
            dict: 变化点检测结果，格式为 {stock_code: {'change_points': [...], 'returns': [...], 'dates': [...]}}
        """
        # 检查缓存
        # 创建缓存目录
        cache_dir = self.model_dir / "features_cache"
        ensure_dir(cache_dir)
        
        # 生成缓存文件名
        try:
            date_range = f"{pd.to_datetime(stock_features_df['date_str']).min().strftime('%Y%m%d')}_{pd.to_datetime(stock_features_df['date_str']).max().strftime('%Y%m%d')}"
            num_stocks = len(stock_features_df['stock_code'].unique())
            cache_file = cache_dir / f"change_points_{num_stocks}stocks_{date_range}.pkl"
            
            # 如果缓存存在，直接加载并返回
            if cache_file.exists():
                self.logger.info(f"从缓存加载变化点检测结果: {cache_file}")
                try:
                    with open(cache_file, 'rb') as f:
                        return pickle.load(f)
                except Exception as e:
                    self.logger.warning(f"加载缓存失败: {e}，将重新计算变化点")
        except Exception as e:
            self.logger.warning(f"生成缓存键失败: {e}，将不使用缓存")
        
        self.logger.info("开始检测股票价格关键变化点...")
        
        # 仅使用future_return_2d作为变化点检测的核心指标
        # 其他指标仅作为参考，但不直接用于变化点检测
        indicators = {'future_return_2d': 1.0}  # 最核心指标，唯一权重
        
        # 检查是否有future_return_2d列
        if 'future_return_2d' not in stock_features_df.columns:
            self.logger.error(f"股票特征数据中缺少必要的future_return_2d列，无法进行变化点检测")
            self.logger.info(f"可用的列有: {stock_features_df.columns.tolist()}")
            
            # 检查是否需要重新计算future_return_2d
            if 'close' in stock_features_df.columns and 'date_str' in stock_features_df.columns:
                self.logger.info("尝试重新计算future_return_2d列...")
                
                # 按股票分组处理
                grouped = stock_features_df.groupby('stock_code')
                all_processed = []
                
                for stock_code, stock_df in grouped:
                    try:
                        # 计算future_return_2d
                        processed_df = self._calculate_future_returns(stock_df)
                        all_processed.append(processed_df)
                    except Exception as e:
                        self.logger.error(f"重新计算股票 {stock_code} 的future_return_2d时出错: {e}")
                        all_processed.append(stock_df)  # 保留原始数据
                
                # 合并结果
                if all_processed:
                    stock_features_df = pd.concat(all_processed, ignore_index=False)
                    self.logger.info(f"重新计算future_return_2d完成，现在有效值比例: {stock_features_df['future_return_2d'].notna().sum() / len(stock_features_df):.2f}")
                else:
                    self.logger.error("重新计算future_return_2d失败")
                    return {}
            else:
                self.logger.error("缺少计算future_return_2d所需的必要列，无法继续变化点检测")
                return {}
        
        # 处理不同股票
        results = {}
        stock_codes = stock_features_df['stock_code'].unique()
        
        self.logger.info(f"开始处理 {len(stock_codes)} 只股票的变化点检测...")
        
        # 按股票代码分组处理
        for i, stock_code in enumerate(stock_codes):
            if i % 100 == 0:
                self.logger.info(f"已处理 {i}/{len(stock_codes)} 只股票")
                
            stock_df = stock_features_df[stock_features_df['stock_code'] == stock_code].copy()
            if len(stock_df) < 30:  # 确保有足够数据
                continue
                
            # 确保股票数据中有future_return_2d列
            if 'future_return_2d' not in stock_df.columns:
                self.logger.warning(f"股票 {stock_code} 缺少future_return_2d列，无法进行变化点检测")
                continue
                
            # 检查future_return_2d列是否有足够的有效值
            valid_ratio = stock_df['future_return_2d'].notna().sum() / len(stock_df)
            if valid_ratio < 0.3:  # 至少30%的数据有效
                self.logger.warning(f"股票 {stock_code} 的future_return_2d有效值比例过低: {valid_ratio:.2f}，跳过")
                continue
                
            # 调用单只股票的变化点检测方法
            result = self._detect_single_stock_change_points(stock_code, stock_df, indicators)
            
            # 保存有效结果
            if result and 'change_points' in result and len(result['change_points']) > 0:
                results[stock_code] = result
        
        successful_count = len(results)
        total_count = len(stock_codes)
        success_rate = successful_count / total_count * 100 if total_count > 0 else 0
        
        self.logger.info(f"变化点检测完成，成功率: {success_rate:.1f}% ({successful_count}/{total_count})")
        
        # 计算所有股票平均变化点数量
        if results:
            total_points = sum(len(r['change_points']) for r in results.values() if 'change_points' in r)
            avg_points = total_points / len(results)
            self.logger.info(f"平均每个股票变化点数: {avg_points:.2f}")
            
        # 保存结果到缓存
        try:
            if 'cache_file' in locals():
                self.logger.info(f"保存变化点检测结果到缓存: {cache_file}")
                with open(cache_file, 'wb') as f:
                    pickle.dump(results, f)
        except Exception as e:
            self.logger.warning(f"保存缓存失败: {e}")
            
        return results

    def collect_stock_sector_change_point_features(self, stock_features_df, sector_features_df, change_points=None, is_training=False, use_cache=True):
        """
        计算个股相对于板块的相对特征，只处理变化点对应的日期
        
        Args:
            stock_features_df: 个股特征DataFrame
            sector_features_df: 板块特征DataFrame
            change_points: 变化点检测结果，格式为 {stock_code: {'change_points': [...], 'dates': [...]}}
            is_training: 是否为训练数据，用于区分缓存文件
            use_cache: 是否使用缓存，默认为True
            
        Returns:
            tuple: (添加了相对特征的DataFrame, 股票板块关联结果DataFrame)
        """
        self.logger.info("开始计算个股相对于板块的特征...")
        
        if stock_features_df.empty or sector_features_df.empty:
            self.logger.warning("特征数据为空，返回空DataFrame")
            return stock_features_df, pd.DataFrame()
        
        # 检查缓存
        cache_file = None
        if use_cache:
            # 创建缓存目录
            cache_dir = self.model_dir / "features_cache"
            ensure_dir(cache_dir)
            
            # 生成缓存文件名
            data_type = "train" if is_training else "predict"
            
            # 使用日期范围和股票数量作为缓存键的一部分
            try:
                date_range = f"{pd.to_datetime(stock_features_df['date_str']).min().strftime('%Y%m%d')}_{pd.to_datetime(stock_features_df['date_str']).max().strftime('%Y%m%d')}"
                num_stocks = len(stock_features_df['stock_code'].unique())
                cache_file = cache_dir / f"relative_features_{data_type}_{num_stocks}stocks_{date_range}.pkl"
                
                # 如果缓存存在，直接加载并返回
                if cache_file.exists():
                    self.logger.info(f"从缓存加载相对特征: {cache_file}")
                    try:
                        # 加载特征缓存
                        with open(cache_file, 'rb') as f:
                            result_df = pickle.load(f)
                            
                        # 尝试加载关系数据缓存
                        relations_cache_file = cache_file.parent / f"relations_{cache_file.name}"
                        relations_df = pd.DataFrame()
                        
                        if relations_cache_file.exists():
                            self.logger.info(f"从缓存加载板块关联结果: {relations_cache_file}")
                            try:
                                with open(relations_cache_file, 'rb') as f:
                                    relations_df = pickle.load(f)
                                self.logger.info(f"成功加载板块关联结果缓存，包含 {len(relations_df)} 条记录")
                            except Exception as e:
                                self.logger.warning(f"加载板块关联结果缓存失败: {e}，将使用空DataFrame")
                                relations_df = pd.DataFrame()
                        
                        return result_df, relations_df
                    except Exception as e:
                        self.logger.warning(f"加载缓存失败: {e}，将重新计算特征")
            except Exception as e:
                self.logger.warning(f"生成缓存键失败: {e}，将不使用缓存")
        
        # 性能优化：复制数据前先创建索引，避免反复查询
        self.logger.info("创建高效索引以加速处理...")
        
        # 复制数据，避免修改原始数据
        result_df = stock_features_df.copy()
        
        # 预处理股票龙头特征
        result_df = self._prepare_stock_leadership_features(result_df)
        
        # 获取股票板块映射函数
        get_stock_sectors_func = self._load_stock_sector_mapping()
        
        # 加载一次数据，供所有股票使用
        data = None
        try:
            from to.stock_sectors_mapping import load_data
            data = load_data()
            self.logger.info("成功加载股票板块映射数据")
        except ImportError:
            try:
                from stock_sectors_mapping import load_data
                data = load_data()
                self.logger.info("成功加载股票板块映射数据")
            except ImportError:
                self.logger.warning("无法导入load_data函数，股票板块映射可能不完整")
            except Exception as e:
                self.logger.warning(f"加载stock_sectors_mapping数据时出错: {str(e)}")
        except Exception as e:
            self.logger.warning(f"加载to.stock_sectors_mapping数据时出错: {str(e)}")
        
        # 注意：移除了无用的relative_cols定义，因为我们将直接从_process_single_stock_date返回的结果中获取特征
                
        # 排除的非行业概念板块列表
        excluded_sectors = [
            'TGN中证500成份股', 'TGN融资融券', 'TGN深股通', 
            'TGN标普道琼斯A股', 'TGNMSCI概念', 'TGN沪深300样本股'
        ]
        
        # 创建股票到板块的映射缓存，一次性获取所有映射
        self.logger.info("一次性构建所有股票的板块映射关系...")
        stock_codes = result_df['stock_code'].unique()
        stock_sector_mapping = {}
        
        # 使用多进程加速股票板块映射构建
        from concurrent.futures import ThreadPoolExecutor
        from functools import partial
        
        def get_stock_sectors_wrapper(stock_code):
            sectors = get_stock_sectors_func(stock_code=stock_code, data=data)
            if sectors:
                return stock_code, [s for s in sectors if s not in excluded_sectors]
            return stock_code, []
        
        # 使用线程池并行处理股票到板块的映射
        with ThreadPoolExecutor(max_workers=10) as executor:
            for stock_code, sectors in executor.map(get_stock_sectors_wrapper, stock_codes):
                stock_sector_mapping[stock_code] = sectors
                
        # 确定需要处理的股票-日期组合
        process_combinations = []
        
        # 修复问题1: 正确检查变化点数据是否有效
        has_valid_change_points = False
        if change_points:
            # 检查是否有任何有效的变化点
            for stock_code, cp_data in change_points.items():
                if cp_data and isinstance(cp_data, dict) and 'dates' in cp_data:
                    dates = cp_data['dates']
                    if isinstance(dates, list) and dates:
                        has_valid_change_points = True
                        for date_str in dates:
                            process_combinations.append((stock_code, date_str))
                    elif isinstance(dates, np.ndarray) and dates.size > 0:
                        has_valid_change_points = True
                        for date_str in dates:
                            process_combinations.append((stock_code, date_str))
            
            if has_valid_change_points:
                self.logger.info(f"将处理 {len(process_combinations)} 个变化点对应的股票-日期组合")
            else:
                self.logger.warning("未找到有效的变化点数据，将处理所有股票-日期组合")
                has_valid_change_points = False
        
        if not has_valid_change_points:
            # 如果没有提供变化点，则处理所有组合
            self.logger.warning("将处理所有股票-日期组合")
            stock_groups = result_df.groupby(['stock_code', 'date_str'])
            process_combinations = [(stock_code, date_str) for (stock_code, date_str) in stock_groups.groups.keys()]
        
        # 创建性能优化的索引
        result_df_idx = result_df.set_index(['stock_code', 'date_str'])
        sector_features_df_idx = sector_features_df.set_index(['sector_code', 'date'])
        
        # 优化：增大批处理大小，减少线程池创建开销
        batch_size = 5000  # 增大批处理大小
        total_groups = len(process_combinations)
        processed = 0

        # 创建结果字典
        results = {}

        # 添加进度日志
        self.logger.info(f"开始处理 {total_groups} 个股票-日期组合，每批次 {batch_size} 个...")
        start_time = time.time()

        # 优化：减少线程数，避免过度并发
        max_workers = min(8, os.cpu_count())  # 减少线程数

        # 按批次处理
        for batch_start in range(0, total_groups, batch_size):
            batch_end = min(batch_start + batch_size, total_groups)
            batch = process_combinations[batch_start:batch_end]

            batch_start_time = time.time()
            self.logger.info(f"开始处理第 {batch_start//batch_size + 1} 批，共 {len(batch)} 个组合...")

            # 优化：对于小批次，直接顺序处理，避免线程池开销
            if len(batch) < 100:
                batch_success = 0
                batch_errors = 0
                for stock_code, date_str in batch:
                    try:
                        result = self._process_single_stock_date(
                            stock_code, date_str, result_df_idx,
                            stock_sector_mapping, sector_features_df_idx
                        )
                        if result:
                            key = (result['stock_code'], result['date'])
                            results[key] = result
                            batch_success += 1
                    except Exception as e:
                        self.logger.error(f"处理股票 {stock_code} 在 {date_str} 的特征时出错: {str(e)}")
                        batch_errors += 1
            else:
                # 使用线程池并行处理大批次
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    # 提交所有任务
                    futures_dict = {}
                    for stock_code, date_str in batch:
                        future = executor.submit(
                            self._process_single_stock_date,
                            stock_code,
                            date_str,
                            result_df_idx,
                            stock_sector_mapping,
                            sector_features_df_idx
                        )
                        futures_dict[future] = (stock_code, date_str)

                    # 收集结果
                    batch_success = 0
                    batch_errors = 0
                    for future in as_completed(futures_dict):
                        try:
                            result = future.result()
                            if result:
                                key = (result['stock_code'], result['date'])
                                results[key] = result
                                batch_success += 1
                        except Exception as e:
                            stock_code, date_str = futures_dict[future]
                            self.logger.error(f"处理股票 {stock_code} 在 {date_str} 的特征时出错: {str(e)}")
                            batch_errors += 1

            # 更新处理进度
            processed += len(batch)
            batch_time = time.time() - batch_start_time
            self.logger.info(f"第 {batch_start//batch_size + 1} 批处理完成：成功 {batch_success}，失败 {batch_errors}，用时 {batch_time:.2f} 秒")
            self.logger.info(f"总进度: 已处理 {processed}/{total_groups} 个股票-日期组合 ({processed/total_groups:.1%})")

        total_time = time.time() - start_time
        self.logger.info(f"所有批次处理完成，总用时 {total_time:.2f} 秒，平均每个组合 {total_time/total_groups:.4f} 秒")
        
        # 优化：批量更新DataFrame，避免逐行操作
        if results:
            self.logger.info(f"共找到 {len(results)} 个有效的股票-板块关联")
            try:
                results_df = pd.DataFrame.from_dict(results, orient='index')
                results_df = results_df.reset_index(drop=True)

                self.logger.info("开始集成特征到result_df和relations_df...")

                # 定义基本关联信息列
                basic_cols = ['stock_code', 'date', 'most_relevant_sector', 'sector_correlation_score', 'is_sector_leader']

                # 分离板块特征：除了基本关联信息外的所有列都是板块特征
                sector_feature_cols = [col for col in results_df.columns if col not in basic_cols]

                # 创建relations_df（直接使用results_df，避免重复循环）
                relations_df = results_df.copy()
                self.logger.info(f"创建relations_df，包含 {len(relations_df)} 条记录和 {len(relations_df.columns)} 个特征")
                self.logger.info(f"其中板块特征列数量: {len(sector_feature_cols)}")

                # 优化：使用merge操作批量更新，而不是逐行更新
                # 准备更新数据
                update_data = results_df[basic_cols].copy()
                update_data = update_data.rename(columns={'date': 'date_str'})  # 匹配result_df的列名

                # 初始化新列（如果不存在）
                for col in ['most_relevant_sector', 'sector_correlation_score', 'is_sector_leader']:
                    if col not in result_df.columns:
                        result_df[col] = None

                # 使用merge + update的方式批量更新
                # 创建临时索引用于快速更新
                result_df_temp = result_df.set_index(['stock_code', 'date_str'])
                update_data_temp = update_data.set_index(['stock_code', 'date_str'])

                # 批量更新
                for col in ['most_relevant_sector', 'sector_correlation_score', 'is_sector_leader']:
                    result_df_temp.loc[update_data_temp.index, col] = update_data_temp[col]

                # 重置索引
                result_df = result_df_temp.reset_index()

                self.logger.info(f"成功批量更新result_df中的 {len(update_data)} 条记录的关联信息")

            except Exception as e:
                self.logger.error(f"处理结果数据时出错: {str(e)}")
                import traceback
                self.logger.error(f"错误详情: {traceback.format_exc()}")
        else:
            self.logger.warning("没有找到有效的股票-板块关联数据")
        
        # 保存计算结果到缓存
        if use_cache and cache_file:
            try:
                self.logger.info(f"保存相对特征到缓存文件: {cache_file}")
                with open(cache_file, 'wb') as f:
                    pickle.dump(result_df, f)
            except Exception as e:
                self.logger.warning(f"保存缓存失败: {e}")
        
                # 如果前面的代码没有创建relations_df（比如在结果为空的情况下），则创建一个空的DataFrame
        if 'relations_df' not in locals():
            self.logger.info("没有找到有效的股票板块关联数据，创建空的relations_df")
            relations_df = pd.DataFrame()
        
        # 创建关联结果缓存文件
        if use_cache and cache_file:
            try:
                relations_cache_file = cache_file.parent / f"relations_{cache_file.name}"
                self.logger.info(f"保存板块关联结果到缓存文件: {relations_cache_file}")
                with open(relations_cache_file, 'wb') as f:
                    pickle.dump(relations_df, f)
            except Exception as e:
                self.logger.warning(f"保存关联结果缓存失败: {e}")
        
        return result_df, relations_df

    def _load_stock_sector_mapping(self):
        """
        加载股票与板块的映射关系函数
        
        Returns:
            function: get_stock_sectors函数引用，用于获取股票板块映射
        """
        try:
            # 尝试从stock_sectors_mapping模块获取
            try:
                # 将当前工作目录添加到sys.path
                sys.path.append(os.getcwd())
                # 尝试导入
                try:
                    from stock_sectors_mapping import get_stock_sectors
                    self.logger.info("成功导入stock_sectors_mapping.get_stock_sectors函数")
                    return get_stock_sectors
                except ImportError:
                    # 如果导入失败，尝试从to目录导入
                    try:
                        sys.path.append(str(get_project_root()))
                        from to.stock_sectors_mapping import get_stock_sectors
                        self.logger.info("成功导入to.stock_sectors_mapping.get_stock_sectors函数")
                        return get_stock_sectors
                    except ImportError:
                        self.logger.warning("无法导入get_stock_sectors函数")
            except Exception as e:
                self.logger.error(f"尝试导入get_stock_sectors函数时出错: {e}")
            
            # 如果都失败了，返回一个默认函数，该函数始终返回空字典
            self.logger.warning("未能导入get_stock_sectors函数，将使用默认空映射函数")
            return lambda *args, **kwargs: {}
            
        except Exception as e:
            self.logger.error(f"加载股票板块映射函数时出错: {e}")
            return lambda *args, **kwargs: {}

    def _add_board_structure_features(self, features, current_day, sector_code, board_structure=None):
        """
        添加板块涨跌停结构特征，借鉴sector_stock_analysis中的分析逻辑
        
        Args:
            features: 特征字典
            current_day: 当日数据
            sector_code: 板块代码
            board_structure: 涨跌停板结构特征数据
            
        Returns:
            更新后的特征字典
        """
        # 注释: 本方法是特征提取的重要组成部分，配合整体的相似度匹配方法，
        # 我们通过提取历史数据中的各种特征（包括涨跌停板结构特征），
        # 在预测时通过计算当前市场特征与历史特征的相似度来识别潜在的交易机会。
        # 这种基于相似度的模式识别方法在量化交易中被广泛应用，
        # 无需复杂的机器学习训练过程，但能够有效捕捉市场模式。
        try:
            # 只有当board_structure不为None时才添加板块涨跌停结构特征
            if board_structure is not None:
                # 1. 涨跌停统计特征
                for field in ["limit_up_count", "limit_down_count", "near_limit_up_count", 
                             "near_limit_down_count", "one_word_limit_up_count", 
                             "broken_limit_up_count", "limit_open_count"]:
                    if field in board_structure:
                        features[field] = board_structure[field]
                
                # 2. 连板特征
                if "max_consecutive_limit_up" in board_structure:
                    features["max_consecutive_limit_up"] = board_structure["max_consecutive_limit_up"]
                
                # 3. 溢价特征 - 只保留有效的溢价特征
                for field in ["yesterday_limit_up_premium", "yesterday_max_limit_up_premium"]:
                    if field in board_structure:
                        features[field] = board_structure[field]
                
                # 4. 市值分布特征
                if "market_cap_distribution" in board_structure and isinstance(board_structure["market_cap_distribution"], dict):
                    for size, count in board_structure["market_cap_distribution"].items():
                        features[f"market_cap_{size}"] = count
                
                # 5. 涨停板市值分布
                if "limit_board_distribution" in board_structure and isinstance(board_structure["limit_board_distribution"], dict):
                    for size, count in board_structure["limit_board_distribution"].items():
                        features[f"limit_board_{size}"] = count

                # 6. 连板分布特征 - 简化版本
                if "board_counts" in board_structure and isinstance(board_structure["board_counts"], dict):
                    # 只保留关键的连板统计
                    total_boards = sum(board_structure["board_counts"].values())
                    if total_boards > 0:
                        features["total_board_count"] = total_boards
                        # 高连板比例（3板及以上）
                        high_boards = sum(count for board_type, count in board_structure["board_counts"].items()
                                        if any(str(i) in board_type for i in range(3, 11)))
                        features["high_board_ratio"] = high_boards / total_boards if total_boards > 0 else 0
                
                # 6. 计算涨停结构指标
                if "stock_count" in board_structure and board_structure["stock_count"] > 0:
                    # 涨停占比
                    if "limit_up_count" in board_structure:
                        features["limit_up_ratio"] = board_structure["limit_up_count"] / board_structure["stock_count"]
                    
                    # 跌停占比
                    if "limit_down_count" in board_structure:
                        features["limit_down_ratio"] = board_structure["limit_down_count"] / board_structure["stock_count"]
                    
                    # 一字板占比
                    if "one_word_limit_up_count" in board_structure:
                        features["one_word_limit_ratio"] = board_structure["one_word_limit_up_count"] / max(1, board_structure["limit_up_count"])
                    
                    # 炸板率
                    if "broken_limit_up_count" in board_structure and "limit_up_count" in board_structure:
                        broken_count = board_structure["broken_limit_up_count"]
                        limit_count = board_structure["limit_up_count"]
                        total_limits = limit_count + broken_count
                        if total_limits > 0:
                            ratio = broken_count / total_limits
                            features["broken_ratio"] = ratio
                            # 添加调试日志
                            if ratio > 0:
                                self.logger.debug(f"计算炸板率: 板块={sector_code}, 日期={current_day.get('date_str', 'unknown')}, 炸板数={broken_count}, 涨停数={limit_count}, 炸板率={ratio:.4f}")
                        else:
                            features["broken_ratio"] = 0
                
                # 7. 涨停质量特征 - 简化版本
                if "one_word_limit_up_count" in board_structure and "limit_up_count" in board_structure:
                    limit_up_count = board_structure["limit_up_count"]
                    if limit_up_count > 0:
                        # 一字板占比（高质量涨停）
                        features["one_word_limit_ratio"] = board_structure["one_word_limit_up_count"] / limit_up_count
                
                # 8. 计算板块强度指标
                if all(k in board_structure for k in ["avg_pct_chg", "avg_turnover", "avg_amplitude"]):
                    # 综合强度 = 平均涨幅 * (1 + 平均换手率/100) * (1 + 平均振幅/100)
                    features["sector_strength"] = board_structure["avg_pct_chg"] * (1 + board_structure["avg_turnover"]/100) * (1 + board_structure["avg_amplitude"]/100)
                
                # 8. 计算热度指数（与sector_stock_analysis.py保持一致）
                heat_score = 0
                weights = {
                    "limit_up_count": 0.30,          # 涨停数量权重
                    "limit_up_ratio": 0.20,          # 涨停占比权重
                    "max_consecutive_limit_up": 0.25, # 最高连板权重
                    "one_word_limit_up_count": 0.15,  # 一字板权重
                    "broken_limit_up_count": -0.10    # 炸板数量负向权重
                }
                
                for metric, weight in weights.items():
                    if metric in board_structure:
                        if metric == "limit_up_ratio":
                            # 涨停占比标准化（20%为满分）
                            value = min(100, board_structure.get(metric, 0) * 500)
                        elif metric == "limit_up_count":
                            # 涨停数量标准化（5个为满分）
                            value = min(100, board_structure.get(metric, 0) * 20)
                        elif metric == "max_consecutive_limit_up":
                            # 最高连板标准化（10板为满分）
                            value = min(100, board_structure.get(metric, 0) * 10)
                        else:
                            value = board_structure.get(metric, 0)
                            
                        heat_score += value * weight
                
                features["board_heat_index"] = max(0, min(100, heat_score))

                # 8. 板块强度综合评分
                if all(k in board_structure for k in ["avg_pct_chg", "limit_up_count", "stock_count"]):
                    avg_pct_chg = board_structure["avg_pct_chg"]
                    limit_ratio = board_structure["limit_up_count"] / max(1, board_structure["stock_count"])
                    features["sector_momentum_score"] = avg_pct_chg * (1 + limit_ratio * 10)
            
            
            return features
            
        except Exception as e:
            self.logger.error(f"添加板块 {sector_code} 的涨跌停结构特征时出错: {e}")
            return features

    def _calculate_similarity(self, vec1, vec2):
        """计算两个向量的相似度
        
        Args:
            vec1: 向量1
            vec2: 向量2
            
        Returns:
            float: 相似度分数
        """
        # 余弦相似度
        if np.linalg.norm(vec1) * np.linalg.norm(vec2) == 0:
            return 0
        return np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))

    def _calculate_feature_importance(self, features_df, target_column, method='correlation'):
        """
        计算特征重要性，用于加权相似度计算
        
        Args:
            features_df: 特征数据框
            target_column: 目标列名（如'next_day_return'）
            method: 特征重要性计算方法，可选'correlation'或'mutual_info'
            
        Returns:
            dict: 特征重要性字典，键为特征名，值为重要性分数
        """
        try:
            # 确保数据框包含目标列
            if target_column not in features_df.columns:
                self.logger.error(f"目标列 {target_column} 不存在于特征数据框中")
                return {}
                
            # 移除非数值列
            numeric_features = features_df.select_dtypes(include=['number']).columns
            numeric_features = [col for col in numeric_features if col != target_column]
            
            # 初始化特征重要性字典
            feature_importance = {}
            
            if method == 'correlation':
                # 使用相关系数计算特征重要性
                for feature in numeric_features:
                    if features_df[feature].nunique() > 1:  # 确保特征有变化
                        # 计算特征与目标的绝对相关系数
                        correlation = abs(features_df[feature].corr(features_df[target_column]))
                        feature_importance[feature] = correlation
            
            elif method == 'mutual_info':
                # 使用互信息计算特征重要性（需要安装sklearn）
                try:
                    from sklearn.feature_selection import mutual_info_regression
                    
                    # 填充缺失值
                    X = features_df[numeric_features].fillna(0)
                    y = features_df[target_column].fillna(0)
                    
                    # 计算互信息
                    mi_scores = mutual_info_regression(X, y)
                    
                    # 创建特征重要性字典
                    for i, feature in enumerate(numeric_features):
                        feature_importance[feature] = mi_scores[i]
                        
                except ImportError:
                    self.logger.warning("无法导入sklearn，将使用相关系数方法")
                    return self._calculate_feature_importance(features_df, target_column, method='correlation')
            
            else:
                self.logger.warning(f"不支持的特征重要性计算方法: {method}，将使用相关系数方法")
                return self._calculate_feature_importance(features_df, target_column, method='correlation')
            
            # 标准化特征重要性，使总和为1
            total_importance = sum(feature_importance.values())
            if total_importance > 0:
                for feature in feature_importance:
                    feature_importance[feature] /= total_importance
            
            # 按重要性降序排序
            feature_importance = {k: v for k, v in sorted(feature_importance.items(), key=lambda item: item[1], reverse=True)}
            
            self.logger.info(f"成功计算 {len(feature_importance)} 个特征的重要性")
            return feature_importance
            
        except Exception as e:
            self.logger.error(f"计算特征重要性时出错: {e}")
            return {}

    def _analyze_feature_contributions(self, pattern_details, top_n=20):
        """
        分析特征贡献度，找出对相似度匹配最重要的特征
        
        Args:
            pattern_details: 包含匹配模式和特征贡献度的详情列表
            top_n: 返回的最重要特征数量
            
        Returns:
            dict: 按重要性排序的特征贡献度字典
        """
        try:
            if not pattern_details:
                return {}
                
            # 汇总所有特征的贡献度
            feature_importance = {}
            
            for pattern in pattern_details:
                if 'feature_contributions' not in pattern:
                    continue
                    
                contributions = pattern['feature_contributions']
                similarity = pattern['similarity']
                
                # 根据模式相似度加权特征贡献度
                for feature, contrib in contributions.items():
                    if feature not in feature_importance:
                        feature_importance[feature] = 0
                    # 累加加权贡献度（乘以相似度权重）
                    feature_importance[feature] += abs(contrib) * similarity
            
            # 标准化特征重要性
            if feature_importance:
                total = sum(feature_importance.values())
                if total > 0:
                    for feature in feature_importance:
                        feature_importance[feature] /= total
            
            # 按重要性降序排序并限制数量
            sorted_features = {k: v for k, v in sorted(
                feature_importance.items(), key=lambda item: item[1], reverse=True
            )[:top_n]}
            
            return sorted_features
            
        except Exception as e:
            self.logger.error(f"分析特征贡献度时出错: {e}")
            return {}


            
    def _evaluate_selected_stocks_old(self, selected_stocks, all_stock_features, evaluation_days=2):
        """
        评估选出股票的实际表现，与预测结果进行比较 (旧方法，用于单日评估)
        
        Args:
            selected_stocks: 选出的股票列表
            all_stock_features: 包含所有股票特征的DataFrame，包括未来收益率数据
            evaluation_days: 评估的天数，默认为2天（对应future_return_2d）
            
        Returns:
            dict: 评估结果
        """
        stocks_count = len(selected_stocks.get('selected_stocks', []))
        self.logger.info(f"评估单日选出的 {stocks_count} 只股票的实际表现...")
        
        try:
            if not selected_stocks or 'selected_stocks' not in selected_stocks or not selected_stocks['selected_stocks']:
                self.logger.warning("没有选出的股票，无法进行评估")
                return {"evaluation_summary": "无选出股票"}
                
            # 获取预测日期
            prediction_date = selected_stocks.get('date', '')
            if not prediction_date:
                self.logger.warning("无法获取预测日期，无法进行评估")
                return {"evaluation_summary": "无预测日期"}
                
            # 转换日期格式
            prediction_date = pd.to_datetime(prediction_date)
            
            # 计算评估日期
            trading_calendar = self._load_trading_calendar()
            if not trading_calendar:
                self.logger.warning("无法加载交易日历，无法确定评估日期")
                return {"evaluation_summary": "无交易日历"}
                
            # 将交易日历转换为日期对象列表
            trading_dates = [pd.to_datetime(d) for d in trading_calendar]
            trading_dates.sort()
            
            # 找出预测日期在交易日历中的位置
            try:
                date_index = trading_dates.index(prediction_date)
            except ValueError:
                # 找到最接近的交易日
                closest_date_index = min(range(len(trading_dates)), 
                                        key=lambda i: abs((trading_dates[i] - prediction_date).days))
                date_index = closest_date_index
                self.logger.warning(f"预测日期 {prediction_date} 不是交易日，使用最近的交易日 {trading_dates[date_index]}")
            
            # 确保有足够的未来交易日进行评估
            if date_index + evaluation_days >= len(trading_dates):
                self.logger.warning(f"预测日期 {prediction_date} 后没有足够的交易日进行评估")
                return {"evaluation_summary": "无足够未来交易日"}
                
            # 获取评估日期
            evaluation_date = trading_dates[date_index + evaluation_days]
            
            # 准备评估数据
            selected_stock_codes = [stock['stock_code'] for stock in selected_stocks['selected_stocks']]
            
            # 获取实际收益率数据
            evaluation_data = all_stock_features[
                (all_stock_features['date_str'] == evaluation_date.strftime('%Y%m%d')) & 
                (all_stock_features['stock_code'].isin(selected_stock_codes))
            ].copy()
            
            if evaluation_data.empty:
                self.logger.warning(f"无法获取评估日期 {evaluation_date} 的股票数据")
                return {"evaluation_summary": "无评估数据"}
            
            # 合并预测和实际数据
            evaluation_results = []
            for stock in selected_stocks['selected_stocks']:
                stock_code = stock['stock_code']
                actual_data = evaluation_data[evaluation_data['stock_code'] == stock_code]
                
                if actual_data.empty:
                    # 如果没有找到实际数据，记录缺失
                    evaluation_results.append({
                        'stock_code': stock_code,
                        'stock_name': stock.get('stock_name', ''),
                        'predicted_return': stock.get('expected_return', 0),
                        'actual_return': None,
                        'prediction_accuracy': None,
                        'rank': stock.get('rank', 0),
                        'status': '数据缺失'
                    })
                else:
                    # 获取实际收益率
                    actual_return = actual_data.iloc[0].get('future_return_2d', None)
                    if actual_return is None:
                        # 尝试从其他列获取实际收益率
                        for col in ['return_2d', 'return_2day', 'change_percent_2d']:
                            if col in actual_data.columns:
                                actual_return = actual_data.iloc[0][col]
                                break
                    
                    # 如果仍然没有找到实际收益率，尝试计算收益率
                    if actual_return is None and 'close' in actual_data.columns and 'prev_close' in actual_data.columns:
                        try:
                            close = float(actual_data.iloc[0]['close'])
                            prev_close = float(actual_data.iloc[0]['prev_close'])
                            if prev_close > 0:
                                actual_return = (close - prev_close) / prev_close * 100
                            else:
                                actual_return = None
                        except (ValueError, TypeError):
                            actual_return = None
                    
                    # 计算预测准确性
                    predicted_return = stock.get('expected_return', 0)
                    if actual_return is not None:
                        # 预测方向是否正确
                        direction_correct = (predicted_return > 0 and actual_return > 0) or \
                                          (predicted_return < 0 and actual_return < 0)
                        
                        # 计算预测误差
                        if predicted_return != 0:
                            prediction_error = abs(actual_return - predicted_return) / abs(predicted_return)
                            prediction_accuracy = max(0, 1 - prediction_error)
                        else:
                            prediction_accuracy = 0 if actual_return != 0 else 1
                            
                        # 确定状态
                        if direction_correct:
                            if prediction_accuracy > 0.7:
                                status = '预测优秀'
                            elif prediction_accuracy > 0.4:
                                status = '方向正确'
                            else:
                                status = '方向正确但误差大'
                        else:
                            status = '方向错误'
                    else:
                        direction_correct = None
                        prediction_accuracy = None
                        status = '无法评估'
                    
                    # 添加到评估结果
                    evaluation_results.append({
                        'stock_code': stock_code,
                        'stock_name': stock.get('stock_name', ''),
                        'predicted_return': predicted_return,
                        'actual_return': actual_return,
                        'direction_correct': direction_correct,
                        'prediction_accuracy': prediction_accuracy,
                        'status': status,
                        'rank': stock.get('rank', 0),
                        'score': stock.get('score', 0)
                    })
            
            # 计算总体评估指标
            valid_results = [r for r in evaluation_results if r['actual_return'] is not None and not pd.isna(r['actual_return'])]
            if valid_results:
                # 安全计算平均实际收益率
                actual_returns = [r['actual_return'] for r in valid_results if pd.notna(r['actual_return'])]
                avg_actual_return = sum(actual_returns) / len(actual_returns) if actual_returns else 0.0

                # 计算方向正确率
                direction_correct_count = sum(1 for r in valid_results if r['direction_correct'] == True)
                direction_accuracy = direction_correct_count / len(valid_results) if valid_results else 0.0

                # 计算平均预测准确度
                valid_accuracy_results = [r for r in valid_results if r['prediction_accuracy'] is not None and not pd.isna(r['prediction_accuracy'])]
                avg_prediction_accuracy = sum(r['prediction_accuracy'] for r in valid_accuracy_results) / len(valid_accuracy_results) if valid_accuracy_results else 0.0

                # 计算胜率（实际收益为正的比例）
                win_count = sum(1 for r in valid_results if r['actual_return'] > 0)
                win_rate = win_count / len(valid_results) if valid_results else 0.0

                # 计算盈亏比指标
                profit_results = [r['actual_return'] for r in valid_results if r['actual_return'] > 0]
                loss_results = [abs(r['actual_return']) for r in valid_results if r['actual_return'] < 0]

                avg_profit = sum(profit_results) / len(profit_results) if profit_results else 0.0
                avg_loss = sum(loss_results) / len(loss_results) if loss_results else 0.0
                profit_loss_ratio = avg_profit / avg_loss if avg_loss > 0 else float('inf') if avg_profit > 0 else 0.0

                # 计算最大回撤
                cumulative_returns = []
                cumulative = 0
                for r in valid_results:
                    cumulative += r['actual_return']
                    cumulative_returns.append(cumulative)

                max_drawdown = 0.0
                if cumulative_returns:
                    peak = cumulative_returns[0]
                    for cum_return in cumulative_returns:
                        if cum_return > peak:
                            peak = cum_return
                        drawdown = peak - cum_return
                        if drawdown > max_drawdown:
                            max_drawdown = drawdown

                # 计算夏普比率（简化版本，假设无风险利率为0）
                if len(actual_returns) > 1:
                    returns_std = np.std(actual_returns, ddof=1)
                    sharpe_ratio = avg_actual_return / returns_std if returns_std > 0 else 0.0
                else:
                    sharpe_ratio = 0.0
                
                # 计算评估总结
                evaluation_summary = {
                    'prediction_date': prediction_date.strftime('%Y%m%d'),
                    'evaluation_date': evaluation_date.strftime('%Y%m%d') if isinstance(evaluation_date, pd.Timestamp) else str(evaluation_date),
                    'total_stocks': len(selected_stock_codes),
                    'evaluated_stocks': len(valid_results),
                    'avg_actual_return': avg_actual_return,
                    'direction_accuracy': direction_accuracy,
                    'avg_prediction_accuracy': avg_prediction_accuracy,
                    'win_rate': win_rate,
                    'avg_profit': avg_profit,
                    'avg_loss': avg_loss,
                    'profit_loss_ratio': profit_loss_ratio,
                    'max_drawdown': max_drawdown,
                    'sharpe_ratio': sharpe_ratio,
                    'performance_rating': self._get_performance_rating(avg_actual_return, win_rate, direction_accuracy)
                }
            else:
                evaluation_summary = {
                    'prediction_date': prediction_date.strftime('%Y%m%d'),
                    'evaluation_date': evaluation_date.strftime('%Y%m%d') if isinstance(evaluation_date, pd.Timestamp) else str(evaluation_date),
                    'total_stocks': len(selected_stock_codes),
                    'evaluated_stocks': 0,
                    'performance_rating': '无法评估'
                }
            
            # 组织最终评估结果
            final_evaluation = {
                'evaluation_summary': evaluation_summary,
                'stock_evaluations': evaluation_results
            }
            
            # 安全格式化输出，避免NaN问题
            avg_return_str = f"{evaluation_summary.get('avg_actual_return', 0):.2f}" if pd.notna(evaluation_summary.get('avg_actual_return')) else "N/A"
            direction_acc_str = f"{evaluation_summary.get('direction_accuracy', 0) * 100:.1f}" if pd.notna(evaluation_summary.get('direction_accuracy')) else "N/A"
            win_rate_str = f"{evaluation_summary.get('win_rate', 0) * 100:.1f}" if pd.notna(evaluation_summary.get('win_rate')) else "N/A"
            profit_loss_str = f"{evaluation_summary.get('profit_loss_ratio', 0):.2f}" if pd.notna(evaluation_summary.get('profit_loss_ratio')) and evaluation_summary.get('profit_loss_ratio') != float('inf') else "N/A"
            sharpe_str = f"{evaluation_summary.get('sharpe_ratio', 0):.2f}" if pd.notna(evaluation_summary.get('sharpe_ratio')) else "N/A"

            self.logger.info(f"评估完成，平均实际收益率: {avg_return_str}%, "
                          f"方向准确率: {direction_acc_str}%, "
                          f"胜率: {win_rate_str}%, "
                          f"盈亏比: {profit_loss_str}, "
                          f"夏普比率: {sharpe_str}")
            
            return final_evaluation
            
        except Exception as e:
            self.logger.error(f"评估选出股票时出错: {e}")
            self.logger.error(traceback.format_exc())
            return {"evaluation_summary": f"评估出错: {str(e)}"}
    
    def _get_performance_rating(self, avg_return, win_rate, direction_accuracy):
        """
        根据平均收益率、胜率和方向准确率评定表现等级
        
        Args:
            avg_return: 平均收益率
            win_rate: 胜率
            direction_accuracy: 方向准确率
            
        Returns:
            str: 表现等级
        """
        # 评分标准
        if avg_return > 3 and win_rate > 0.6 and direction_accuracy > 0.7:
            return '优秀'
        elif avg_return > 1.5 and win_rate > 0.55 and direction_accuracy > 0.6:
            return '良好'
        elif avg_return > 0 and win_rate > 0.5 and direction_accuracy > 0.5:
            return '一般'
        elif avg_return <= 0 and win_rate < 0.5 and direction_accuracy < 0.5:
            return '较差'
        else:
            return '中性'

    def _prepare_stock_leadership_features(self, stock_df):
        """
        预处理股票的龙头特征，为计算股票在板块中的地位做准备
        
        Args:
            stock_df: 股票特征DataFrame
            
        Returns:
            添加了龙头特征的DataFrame
        """
        self.logger.info("开始预处理股票龙头特征...")
        
        if stock_df.empty:
            return stock_df
            
        result_df = stock_df.copy()
        
        # 1. 识别连板天数
        if 'is_limit_up' in result_df.columns:
            # 初始化连续涨停天数列
            result_df['consecutive_limit_days'] = 0
            
            # 按股票代码分组处理
            for stock_code, group in result_df.groupby('stock_code'):
                # 确保按日期排序
                group = group.sort_values('date_str')
                
                # 计算连续涨停天数
                consecutive_days = 0
                for idx, row in group.iterrows():
                    if row['is_limit_up']:
                        consecutive_days += 1
                    else:
                        consecutive_days = 0
                    
                    # 更新连续涨停天数
                    result_df.loc[idx, 'consecutive_limit_days'] = consecutive_days
        
        # 2. 识别一字板
        if all(col in result_df.columns for col in ['open', 'high', 'low', 'close', 'is_limit_up']):
            # 判断开盘、最高、最低、收盘价格是否相同，且是涨停
            result_df['is_one_word_limit'] = (
                (result_df['open'] == result_df['high']) & 
                (result_df['high'] == result_df['low']) & 
                (result_df['low'] == result_df['close']) & 
                result_df['is_limit_up']
            )
        else:
            result_df['is_one_word_limit'] = False
            
        # 3. 识别高弹性股票
        if 'amplitude' in result_df.columns:
            # 振幅大于5%且非涨跌停的股票视为高弹性
            result_df['is_high_elasticity'] = (
                (result_df['amplitude'] >= 5.0) & 
                (~result_df['is_limit_up']) & 
                (~result_df.get('is_limit_down', pd.Series(False, index=result_df.index)))
            )
        else:
            result_df['is_high_elasticity'] = False
            
        # 4. 判断市值类型
        if 'total_mv' in result_df.columns:
            # 根据总市值划分大中小市值
            result_df['market_cap_type'] = 'small'  # 默认小市值
            result_df.loc[result_df['total_mv'] > 50, 'market_cap_type'] = 'medium'  # 50-200亿为中市值
            result_df.loc[result_df['total_mv'] > 200, 'market_cap_type'] = 'large'  # >200亿为大市值
        else:
            result_df['market_cap_type'] = 'small'
            
        # 5. 判断是否开盘涨停
        if all(col in result_df.columns for col in ['open', 'preClose', 'is_limit_up']):
            # ST股票涨停幅度是5%，非ST是10%
            # 这里简化处理，统一按照9.5%的阈值判断是否开盘涨停
            result_df['is_open_limit_up'] = (
                (result_df['open'] / result_df['preClose'] - 1 > 0.095) &
                result_df['is_limit_up']
            )
        else:
            result_df['is_open_limit_up'] = False
            
        # 6. 判断是否突破前期高点
        if 'close' in result_df.columns:
            # 按股票代码分组处理
            for stock_code, group in result_df.groupby('stock_code'):
                # logger.info(f"列：{group.columns}")
                # 确保按日期排序
                group = group.sort_values('date_str')
                
                # 计算60日最高价
                group['max_60d'] = group['close'].rolling(window=60, min_periods=1).max().shift(1)
                
                # 判断是否突破前期高点
                group['break_previous_high'] = group['close'] > group['max_60d']
                
                # 更新回原始DataFrame
                result_df.loc[group.index, 'max_60d'] = group['max_60d']
                result_df.loc[group.index, 'break_previous_high'] = group['break_previous_high']
        else:
            result_df['break_previous_high'] = False
            
        return result_df
        
    def _process_single_stock_date(self, stock_code, date_str, result_df_idx, stock_sector_mapping, sector_features_df_idx):
        """处理单个股票日期组合，找到最相关的板块并返回板块特征

        Args:
            stock_code: 股票代码
            date_str: 日期字符串
            result_df_idx: 股票特征DataFrame索引
            stock_sector_mapping: 股票与板块的映射字典
            sector_features_df_idx: 板块特征DataFrame索引

        Returns:
            dict: 包含最相关板块信息和板块特征的字典，如果无效则返回None
        """
        try:
            # 使用索引快速获取数据
            try:
                stock_row = result_df_idx.loc[(stock_code, date_str)]
            except KeyError:
                return None

            # 查找股票所属板块
            sectors = stock_sector_mapping.get(stock_code, [])
            if not sectors:
                return None

            # 使用向量化操作获取相关板块数据
            sector_rows = []
            sector_codes = []
            for sector_code in sectors:
                try:
                    sector_row = sector_features_df_idx.loc[(sector_code, date_str)]
                    sector_rows.append(sector_row)
                    sector_codes.append(sector_code)
                except KeyError:
                    continue

            if not sector_rows:
                return None
            
            # 优化：直接获取标量值，避免重复的类型检查
            def get_scalar_value(value, default=0):
                """快速获取标量值"""
                if isinstance(value, pd.Series):
                    return value.iloc[0] if not value.empty else default
                return value if pd.notna(value) else default

            # 批量获取股票特征 - 统一字段名映射
            stock_change = get_scalar_value(stock_row.get('pct_chg', 0))  # 统一使用pct_chg
            stock_volume = get_scalar_value(stock_row.get('vol', 0))
            stock_turnover = get_scalar_value(stock_row.get('turnover_rate', 0))
            stock_amplitude = get_scalar_value(stock_row.get('amplitude', 0))
            if isinstance(stock_amplitude, pd.Series):
                stock_amplitude = stock_amplitude.iloc[0] if not stock_amplitude.empty else 0
            
            # 计算与各板块的相关性评分（向量化处理）
            sector_scores = []
            
            for i, sector_row in enumerate(sector_rows):
                sector_code = sector_codes[i]
                
                # 1. 涨跌幅相关性 - 修复：板块使用change_percent字段
                sector_change = sector_row.get('change_percent', 0)
                if isinstance(sector_change, pd.Series):
                    sector_change = sector_change.iloc[0] if not sector_change.empty else 0
                    
                return_correlation = 0.0
                # 修复: 添加类型安全检查和isfinite检查
                try:
                    if (isinstance(stock_change, (int, float, np.number)) and 
                        isinstance(sector_change, (int, float, np.number)) and
                        np.isfinite(stock_change) and np.isfinite(sector_change)):
                        return_diff = abs(stock_change - sector_change)
                        return_correlation = 1.0 / (1.0 + return_diff)
                except Exception as e:
                    # 安全失败，保持默认值
                    return_correlation = 0.0
                
                # 2. 成交量相关性
                volume_correlation = 0.0
                sector_volume = sector_row.get('vol', 0)
                if isinstance(sector_volume, pd.Series):
                    sector_volume = sector_volume.iloc[0] if not sector_volume.empty else 0
                    
                if stock_volume > 0 and sector_volume > 0:
                    vol_ratio = min(stock_volume, sector_volume) / max(stock_volume, sector_volume)
                    volume_correlation = vol_ratio
                
                # 3. 换手率相关性
                turnover_correlation = 0.0
                sector_turnover = get_scalar_value(sector_row.get('turnover_rate', 0))

                if stock_turnover > 0 and sector_turnover > 0:
                    turnover_ratio = min(stock_turnover, sector_turnover) / max(stock_turnover, sector_turnover)
                    turnover_correlation = turnover_ratio

                # 4. 波动率相关性
                volatility_correlation = 0.0
                sector_amplitude = get_scalar_value(sector_row.get('amplitude', 0))

                if stock_amplitude > 0 and sector_amplitude > 0:
                    amp_ratio = min(stock_amplitude, sector_amplitude) / max(stock_amplitude, sector_amplitude)
                    volatility_correlation = amp_ratio
                
                # 5. 技术指标相关性
                tech_correlation = 0.0
                tech_indicators = ['rsi14', 'ma5_slope', 'momentum_10d']
                matching_indicators = []
                
                # 修复：检查存在的技术指标
                for ind in tech_indicators:
                    has_stock_ind = ind in stock_row.index
                    has_sector_ind = ind in sector_row.index
                    if has_stock_ind and has_sector_ind:
                        matching_indicators.append(ind)
                
                if matching_indicators:
                    tech_diffs = []
                    for ind in matching_indicators:
                        stock_val = stock_row[ind]
                        sector_val = sector_row[ind]
                        
                        # 确保值为标量
                        if isinstance(stock_val, pd.Series):
                            stock_val = stock_val.iloc[0] if not stock_val.empty else 0
                        if isinstance(sector_val, pd.Series):
                            sector_val = sector_val.iloc[0] if not sector_val.empty else 0
                            
                        # 确保是数值，避免比较错误
                        if pd.notna(stock_val) and pd.notna(sector_val):
                            tech_diffs.append(abs(stock_val - sector_val))
                            
                    avg_diff = sum(tech_diffs) / len(tech_diffs) if tech_diffs else 1.0
                    tech_correlation = 1.0 / (1.0 + avg_diff)
                
                # 6. 个股在板块中的地位
                leadership_score = 0.0
                is_leader = False
                
                # 使用已计算的龙头特征（优化版本）
                consecutive_days = get_scalar_value(stock_row.get('consecutive_limit_days', 0))

                if consecutive_days >= 2:
                    leadership_score += min(1.0, consecutive_days / 3.0)

                # 批量获取布尔值特征
                is_one_word_limit = get_scalar_value(stock_row.get('is_one_word_limit', False), False)
                is_high_elasticity = get_scalar_value(stock_row.get('is_high_elasticity', False), False)
                is_limit_up = get_scalar_value(stock_row.get('is_limit_up', False), False)
                market_cap_type = get_scalar_value(stock_row.get('market_cap_type', ''), '')

                if is_one_word_limit:
                    leadership_score += 0.5

                if is_high_elasticity:
                    leadership_score += 0.3

                if market_cap_type == 'large' and (is_limit_up or consecutive_days > 0):
                    leadership_score += 0.4
                
                leadership_score = min(1.0, leadership_score)
                
                # 7. 涨跌停结构相似性
                limit_structure_score = 0.0
                
                # 修复：安全地检查字段是否存在
                limit_up_count_exists = 'limit_up_count' in sector_row.index
                limit_down_count_exists = 'limit_down_count' in sector_row.index
                
                has_limit_up_count = False
                has_limit_down_count = False
                
                if limit_up_count_exists:
                    limit_up_count = sector_row['limit_up_count']
                    if isinstance(limit_up_count, pd.Series):
                        limit_up_count = limit_up_count.iloc[0] if not limit_up_count.empty else 0
                    has_limit_up_count = limit_up_count > 0
                    
                if limit_down_count_exists:
                    limit_down_count = sector_row['limit_down_count']
                    if isinstance(limit_down_count, pd.Series):
                        limit_down_count = limit_down_count.iloc[0] if not limit_down_count.empty else 0
                    has_limit_down_count = limit_down_count > 0
                
                if has_limit_up_count or has_limit_down_count:
                    is_stock_limit_up = stock_row.get('is_limit_up', False)
                    if isinstance(is_stock_limit_up, pd.Series):
                        is_stock_limit_up = is_stock_limit_up.iloc[0] if not is_stock_limit_up.empty else False
                        
                    is_stock_limit_down = stock_row.get('is_limit_down', False)
                    if isinstance(is_stock_limit_down, pd.Series):
                        is_stock_limit_down = is_stock_limit_down.iloc[0] if not is_stock_limit_down.empty else False
                    
                    if is_stock_limit_up and has_limit_up_count:
                        limit_structure_score += 0.5
                    if is_stock_limit_down and has_limit_down_count:
                        limit_structure_score += 0.5
                
                # 计算加权总分 - 使用更高效的权重计算方式
                # 预定义权重，避免重复创建字典
                return_weight = 0.3
                volume_weight = 0.15
                turnover_weight = 0.15
                volatility_weight = 0.1
                tech_weight = 0.1
                leadership_weight = 0.1
                limit_weight = 0.1
                
                # 直接计算加权和
                total_score = (
                    return_correlation * return_weight +
                    volume_correlation * volume_weight +
                    turnover_correlation * turnover_weight +
                    volatility_correlation * volatility_weight +
                    tech_correlation * tech_weight +
                    leadership_score * leadership_weight +
                    limit_structure_score * limit_weight
                )
                
                sector_scores.append({
                    'sector_code': sector_code,
                    'total_score': total_score,
                    'return_correlation': return_correlation,
                    'volume_correlation': volume_correlation,
                    'turnover_correlation': turnover_correlation, 
                    'volatility_correlation': volatility_correlation,
                    'tech_correlation': tech_correlation,
                    'leadership_score': leadership_score,
                    'limit_structure_score': limit_structure_score
                })
            
            # 如果至少有一个有效的板块相关性，添加到结果中
            if sector_scores:
                # 找出最高分的板块
                sorted_scores = sorted(sector_scores, key=lambda x: x['total_score'], reverse=True)
                best_sector = sorted_scores[0]
                best_sector_code = best_sector['sector_code']

                # 获取最相关板块的完整特征
                best_sector_features = {}
                try:
                    best_sector_row = sector_features_df_idx.loc[(best_sector_code, date_str)]
                    # 将最相关板块的所有特征添加到结果中
                    for col in best_sector_row.index:
                        if col not in ['sector_code', 'date']:  # 排除标识列
                            best_sector_features[col] = best_sector_row[col]
                except KeyError:
                    self.logger.warning(f"无法获取板块 {best_sector_code} 在 {date_str} 的完整特征")

                # 返回结果：基本关联信息 + 最相关板块的完整特征
                result = {
                    'stock_code': stock_code,
                    'date': date_str,
                    'most_relevant_sector': best_sector_code,
                    'sector_correlation_score': best_sector['total_score'],
                    'is_sector_leader': best_sector['leadership_score'] > 0.5
                }

                # 添加最相关板块的完整特征
                result.update(best_sector_features)

                return result
            
            return None
        except Exception as e:
            self.logger.error(f"处理股票 {stock_code} 在 {date_str} 的特征时出错: {str(e)}")
            # 添加详细的错误跟踪以便更好地调试
            import traceback
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            return None


def main():
    """主函数，用于命令行调用"""
    parser = argparse.ArgumentParser(description="板块轮动模型训练与预测")
    parser.add_argument('--mode', type=str, choices=['train', 'select_stocks'], 
                      help='操作模式：train (训练模型), select_stocks (选股)')
    parser.add_argument('--start_date', type=str, help='开始日期，格式为YYYYMMDD')
    parser.add_argument('--end_date', type=str, help='结束日期，格式为YYYYMMDD')
    parser.add_argument('--date', type=str, help='预测日期，格式为YYYYMMDD')
    parser.add_argument('--top_n', type=int, default=50, help='选择前N个板块或股票')
    parser.add_argument('--max_workers', type=int, help='并行训练的进程数')
    parser.add_argument('--sector_list', type=str, help='板块列表文件路径，用于板块轮动模型训练')
    parser.add_argument('--model_dir', type=str, help='模型保存目录')
    parser.add_argument('--model_name', type=str, default='sector_rotation_model', help='模型名称')
    parser.add_argument('--save_results', type=str, help='保存结果的文件路径')
    parser.add_argument('--stock_list', type=str, help='股票列表文件路径，用于选股')
    parser.add_argument('--use_cache', action='store_true', help='是否使用特征缓存，默认为True')
    parser.add_argument('--no_cache', dest='use_cache', action='store_false', help='不使用特征缓存')
    parser.set_defaults(use_cache=True)
    
    args = parser.parse_args()
    print(f"开始")
   
    logger.setLevel(logging.DEBUG)
    # 创建SectorModelTrainer实例
    trainer = SectorModelTrainer(model_dir=args.model_dir)
    
    # 加载板块列表
    sector_list = []
    try:
        # 优先使用参数指定的板块列表文件
        if args.sector_list and os.path.exists(args.sector_list):
            with open(args.sector_list, 'r', encoding='utf-8') as f:
                sector_list = [line.strip() for line in f if line.strip()]
            logger.info(f"从文件 {args.sector_list} 加载了 {len(sector_list)} 个板块")
        else:
            # 尝试从stock_sectors_mapping模块获取
            try:
                # 将当前工作目录添加到sys.path
                sys.path.append(os.getcwd())
                from stock_sectors_mapping import get_tng_sectors
                sector_list = get_tng_sectors()
                logger.info(f"从get_tng_sectors加载了 {len(sector_list)} 个板块")
            except ImportError:
                # 如果导入失败，尝试从to目录导入
                try:
                    sys.path.append(str(get_project_root()))
                    from to.stock_sectors_mapping import get_tng_sectors
                    sector_list = get_tng_sectors()
                    logger.info(f"从to.stock_sectors_mapping加载了 {len(sector_list)} 个板块")
                except ImportError:
                    logger.error("无法导入get_tng_sectors函数，请提供板块列表文件")
    except Exception as e:
        logger.error(f"获取板块列表时出错: {e}")
        logger.error(traceback.format_exc())
    
    # 加载股票列表（如果需要）
    stock_list = None
    if args.stock_list and os.path.exists(args.stock_list):
        try:
            with open(args.stock_list, 'r', encoding='utf-8') as f:
                stock_list = [line.strip() for line in f if line.strip()]
            logger.info(f"从文件 {args.stock_list} 加载了 {len(stock_list)} 只股票")
        except Exception as e:
            logger.error(f"加载股票列表时出错: {e}")
    
    # 运行模式
    if args.mode == 'train':
        # 训练板块轮动模型（如果已存在模型，会先尝试加载）
        if not sector_list:
            print("错误: 训练板块轮动模型需要提供板块列表")
            return
                    
        # 调用训练函数 - 内部已包含了2025年2月生命周期预测和结果保存
        model_result = trainer.train_rotation_model(
                sector_list=sector_list,
                start_date=args.start_date,
                end_date=args.end_date,
                model_name=args.model_name)
            
        if model_result:
            print(f"板块轮动模型训练成功，2025年2月的生命周期阶段预测结果已保存在results目录下")
        else:
            print("板块轮动模型训练失败")
    
    elif args.mode == 'select_stocks':
        # 使用变化点方法进行选股
        top_n = args.top_n if args.top_n else 50
        model_name = args.model_name if args.model_name else "stock_selector"
        
        # 设置日期范围
        start_date = args.start_date
        end_date = args.end_date if args.end_date else datetime.now().strftime("%Y%m%d")
        
        print(f"开始运行基于变化点检测的智能选股框架...")
        print(f"- 时间范围: {start_date} 至 {end_date}")
        print(f"- 选出数量: 前 {top_n} 只股票")
        print(f"- 模型名称: {model_name}")
        print(f"- 使用缓存: {'是' if args.use_cache else '否'}")
        print("-" * 80)
        
        # 调用选股函数
        selected_stocks = trainer.select_stocks_by_change_points(
            start_date=start_date,
            end_date=end_date,
            model_name=model_name,
            top_n=top_n,
            use_cache=args.use_cache
        )
        
        if selected_stocks and selected_stocks.get('selected_stocks'):
            # 打印选股结果
            selected_list = selected_stocks.get('selected_stocks', [])
            print(f"\n选股结果 (日期: {selected_stocks.get('date', 'N/A')}, 共选出 {len(selected_list)} 只股票):")
            print("-" * 80)
            print(f"{'排名':<6}{'股票代码':<12}{'股票名称':<15}{'预期收益率':>12}{'概率':>10}{'置信度':>10}{'得分':>10}")
            print("-" * 80)

            for i, stock in enumerate(selected_list):
                if i < 20:  # 只显示前20只
                    stock_name = stock.get('stock_name', 'N/A')
                    expected_return = stock.get('expected_return', 0)
                    probability = stock.get('probability', 0)
                    confidence = stock.get('confidence', 0)
                    score = stock.get('score', 0)

                    print(f"{i+1:<6}{stock['stock_code']:<12}{stock_name:<15}{expected_return:>12.2f}%{probability*100:>9.1f}%{confidence*100:>9.1f}%{score:>9.2f}")

            if len(selected_list) > 20:
                print(f"... 还有 {len(selected_list)-20} 只股票未显示")

            print(f"\n选股结果已保存到 {trainer.model_dir / f'{model_name}_results.json'}")
        else:
            print("选股失败或未选出任何股票，请检查日志获取详细信息")
            if selected_stocks:
                print(f"选股结果状态: {selected_stocks}")
            else:
                print("选股结果为空")
    else:
        print("请指定有效的运行模式: --mode train 或 --mode select_stocks")
        print("\n示例用法:")
        print("训练板块轮动模型:")
        print("  python train_sector_models.py --mode train --start_date 20230101 --end_date 20231231 --model_name sector_rotation_model")
        print("\n智能选股:")
        print("  python train_sector_models.py --mode select_stocks --start_date 20230101 --end_date 20231231 --top_n 50 --model_name stock_selector")
        print("  python train_sector_models.py --mode select_stocks --start_date 20230101 --end_date 20231231 --top_n 50 --no_cache  # 不使用缓存")


if __name__ == "__main__":
    main() 