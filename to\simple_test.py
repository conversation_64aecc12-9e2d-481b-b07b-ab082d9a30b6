#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试修复
"""

try:
    from advanced_quant_data import ParquetDataStorage
    print("✅ 成功导入ParquetDataStorage")
    
    storage = ParquetDataStorage()
    print("✅ 成功创建存储实例")
    
    # 测试获取少量数据
    test_stocks = ['000001.SZ', '000002.SZ']
    start_date = '20241210'
    end_date = '20241210'
    
    print(f"测试获取数据: {test_stocks}, {start_date}-{end_date}")
    
    df = storage.get_daily_data(test_stocks, start_date, end_date)
    
    if df is not None and not df.empty:
        print(f"✅ 成功获取数据，形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        print("前几行:")
        print(df.head(2))
    else:
        print("❌ 获取数据失败或为空")
        
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    print(traceback.format_exc())
