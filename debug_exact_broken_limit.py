#!/usr/bin/env python3
"""
精确调试炸板股检测问题 - 检查具体股票的具体数据
"""

import pandas as pd
import numpy as np
from pathlib import Path
import logging
import sys
import os

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_stock_data_sample(date_str="20230103"):
    """加载样本股票数据，检查字段结构"""
    logger.info(f"=== 检查 {date_str} 的股票数据字段结构 ===")

    try:
        # 使用正确的数据加载器
        from to.data_loaders import get_data_loader

        loader = get_data_loader()

        # 获取一些股票代码用于测试
        stock_list = ['000001.SZ', '000002.SZ', '300001.SZ']  # 测试几只股票

        # 获取单日数据
        df = loader.get_daily_data(stock_list, date_str, date_str)

        if df is None or df.empty:
            logger.error(f"未找到日期 {date_str} 的股票数据")
            return None

        logger.info(f"成功加载数据，形状: {df.shape}")
        logger.info(f"数据列: {df.columns.tolist()}")

        # 检查第一行数据
        if len(df) > 0:
            first_row = df.iloc[0]
            stock_code = first_row.get('code', 'unknown')

            logger.info(f"样本股票: {stock_code}")

            # 检查关键字段是否存在
            required_fields = ['high', 'close', 'pre_close', 'open', 'pct_chg']
            missing_fields = []
            existing_fields = {}

            for field in required_fields:
                if field in df.columns:
                    value = first_row[field]
                    existing_fields[field] = value
                    logger.info(f"  ✅ {field}: {value} (类型: {type(value)})")
                else:
                    missing_fields.append(field)
                    logger.warning(f"  ❌ {field}: 字段不存在")

            if missing_fields:
                logger.error(f"缺少关键字段: {missing_fields}")
                return None
            else:
                logger.info("✅ 所有关键字段都存在")

                # 转换为字典格式以便后续处理
                stock_data = {}
                for _, row in df.iterrows():
                    code = row.get('code', row.get('stock_code', 'unknown'))
                    stock_data[code] = row.to_dict()

                return stock_data
        else:
            logger.error("数据为空")
            return None

    except Exception as e:
        logger.error(f"加载股票数据失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def test_broken_limit_detection(stock_data, test_stocks, test_date):
    """测试特定股票的炸板检测"""
    logger.info(f"=== 测试 {test_date} 的炸板检测 ===")
    
    # 模拟SectorBoardAnalyzer的炸板检测逻辑
    def get_stock_limit_ratios(stock_code):
        """获取股票涨停比例"""
        if 'ST' in stock_code.upper():
            return 0.05, -0.05
        elif stock_code.startswith('688'):  # 科创板
            return 0.20, -0.20
        elif stock_code.startswith('300'):  # 创业板
            return 0.20, -0.20
        elif '.BJ' in stock_code:  # 北交所
            return 0.30, -0.30
        else:
            return 0.10, -0.10
    
    def is_broken_limit_detailed(stock_data, stock_code):
        """详细的炸板检测，输出每一步计算过程"""
        try:
            logger.info(f"\n--- 检测股票 {stock_code} 是否炸板 ---")
            
            # 获取数据
            pct_chg = stock_data.get('pct_chg', stock_data.get('change_percent', 0))
            close = stock_data.get('close', None)
            pre_close = stock_data.get('pre_close', None)
            high = stock_data.get('high', None)
            open_price = stock_data.get('open', None)
            
            logger.info(f"原始数据:")
            logger.info(f"  pct_chg: {pct_chg}")
            logger.info(f"  close: {close}")
            logger.info(f"  pre_close: {pre_close}")
            logger.info(f"  high: {high}")
            logger.info(f"  open: {open_price}")
            
            # 检查数据完整性
            if pre_close is None or high is None or close is None:
                logger.warning(f"  ❌ 关键数据缺失")
                return False, "关键数据缺失"
                
            # 确定涨停比例
            limit_up_ratio, _ = get_stock_limit_ratios(stock_code)
            logger.info(f"涨停比例: {limit_up_ratio*100:.1f}%")
            
            # 计算理论涨停价
            theoretical_limit = pre_close * (1 + limit_up_ratio)
            rounded_limit = round(theoretical_limit, 2)
            
            logger.info(f"计算过程:")
            logger.info(f"  理论涨停价: {pre_close} * (1 + {limit_up_ratio}) = {theoretical_limit}")
            logger.info(f"  四舍五入涨停价: {rounded_limit}")
            
            # 判断条件
            high_limit_diff = abs(high - rounded_limit)
            close_limit_diff = abs(close - rounded_limit)
            
            is_high_reached_limit = high_limit_diff < 0.005
            is_close_not_limit = close_limit_diff >= 0.005
            
            logger.info(f"判断条件:")
            logger.info(f"  最高价与涨停价差值: |{high} - {rounded_limit}| = {high_limit_diff}")
            logger.info(f"  最高价达到涨停: {is_high_reached_limit} (差值 < 0.005)")
            logger.info(f"  收盘价与涨停价差值: |{close} - {rounded_limit}| = {close_limit_diff}")
            logger.info(f"  收盘价未达涨停: {is_close_not_limit} (差值 >= 0.005)")
            
            is_broken = is_high_reached_limit and is_close_not_limit
            
            logger.info(f"最终结果: {'✅ 炸板' if is_broken else '❌ 非炸板'}")
            
            return is_broken, {
                'pct_chg': pct_chg,
                'close': close,
                'pre_close': pre_close,
                'high': high,
                'open': open_price,
                'limit_ratio': limit_up_ratio,
                'theoretical_limit': theoretical_limit,
                'rounded_limit': rounded_limit,
                'high_limit_diff': high_limit_diff,
                'close_limit_diff': close_limit_diff,
                'high_reached_limit': is_high_reached_limit,
                'close_not_limit': is_close_not_limit
            }
            
        except Exception as e:
            logger.error(f"计算股票 {stock_code} 炸板时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False, f"计算错误: {e}"
    
    # 测试指定的股票
    broken_count = 0
    for stock_code in test_stocks:
        if stock_code in stock_data:
            is_broken, details = is_broken_limit_detailed(stock_data[stock_code], stock_code)
            if is_broken:
                broken_count += 1
                logger.info(f"🎯 确认炸板: {stock_code}")
            else:
                logger.warning(f"❌ 未检测到炸板: {stock_code}")
        else:
            logger.error(f"❌ 股票 {stock_code} 在数据中不存在")
    
    logger.info(f"\n=== 测试结果 ===")
    logger.info(f"测试股票数: {len(test_stocks)}")
    logger.info(f"检测到炸板数: {broken_count}")
    
    return broken_count

def check_sector_analysis_logic():
    """检查sector_stock_analysis.py中的炸板逻辑"""
    logger.info("=== 检查sector_stock_analysis.py中的炸板逻辑 ===")
    
    try:
        # 导入SectorBoardAnalyzer
        from to.sector_stock_analysis import SectorBoardAnalyzer
        
        # 创建分析器实例
        analyzer = SectorBoardAnalyzer()
        
        # 检查配置
        logger.info(f"默认涨停比例: {analyzer.limit_up_pct}")
        logger.info(f"特殊配置: {analyzer.special_limit_configs}")
        
        # 测试炸板检测方法
        test_data = {
            'close': 10.85,
            'pre_close': 10.0,
            'high': 11.0,
            'open': 10.5,
            'pct_chg': 8.5
        }
        
        result = analyzer._is_broken_limit(test_data, '000001.SZ')
        logger.info(f"测试数据炸板检测结果: {result}")
        
        return True
        
    except Exception as e:
        logger.error(f"检查sector_stock_analysis逻辑失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    logger.info("=== 开始精确调试炸板股检测问题 ===")
    
    # 1. 检查样本数据字段结构
    sample_data = load_stock_data_sample("20230103")
    if sample_data is None:
        logger.error("无法加载样本数据，退出")
        return
    
    # 2. 检查sector_stock_analysis逻辑
    if not check_sector_analysis_logic():
        logger.error("sector_stock_analysis逻辑检查失败")
        return
    
    # 3. 测试已知炸板股
    test_date = "20240603"
    test_stocks = ["300563.SZ", "300462.SZ", "603009.SH"]
    
    logger.info(f"\n=== 加载测试日期 {test_date} 的数据 ===")

    try:
        from to.data_loaders import get_data_loader

        loader = get_data_loader()

        # 获取测试日期的数据
        df = loader.get_daily_data(test_stocks, test_date, test_date)

        if df is not None and not df.empty:
            logger.info(f"成功加载数据，形状: {df.shape}")
            logger.info(f"数据列: {df.columns.tolist()}")

            # 转换为字典格式
            test_data = {}
            for _, row in df.iterrows():
                code = row.get('code', row.get('stock_code', 'unknown'))
                test_data[code] = row.to_dict()

            logger.info(f"转换后的股票数据: {list(test_data.keys())}")

            broken_count = test_broken_limit_detection(test_data, test_stocks, test_date)

            if broken_count == 0:
                logger.error("❌ 已知炸板股未被检测到，存在问题！")

                # 详细分析原因
                logger.info("\n=== 详细分析原因 ===")
                for stock_code in test_stocks:
                    if stock_code in test_data:
                        data = test_data[stock_code]
                        logger.info(f"\n股票 {stock_code} 的完整数据:")
                        for key, value in data.items():
                            logger.info(f"  {key}: {value}")
                    else:
                        logger.error(f"股票 {stock_code} 不在数据中")
                        # 检查是否是后缀问题
                        base_code = stock_code.split('.')[0]
                        found_variants = [k for k in test_data.keys() if k.startswith(base_code)]
                        if found_variants:
                            logger.info(f"  找到相似代码: {found_variants}")
                        else:
                            logger.info(f"  数据中的所有股票: {list(test_data.keys())}")
            else:
                logger.info(f"✅ 成功检测到 {broken_count} 只炸板股")
        else:
            logger.error(f"无法加载 {test_date} 的数据")

    except Exception as e:
        logger.error(f"测试已知炸板股失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
    
    logger.info("=== 精确调试完成 ===")

if __name__ == "__main__":
    main()
