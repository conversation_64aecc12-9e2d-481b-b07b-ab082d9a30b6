#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试重构后的代码结构
"""

import sys
import os

def test_basic_structure():
    """测试基本代码结构"""
    print("开始测试重构后的代码结构...")
    
    # 检查文件是否存在
    train_file = "train_sector_models.py"
    if not os.path.exists(train_file):
        print(f"❌ 文件不存在: {train_file}")
        return False
    
    print(f"✅ 文件存在: {train_file}")
    
    # 检查关键方法是否存在
    with open(train_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查重构后的关键方法
    key_methods = [
        '_analyze_change_point_patterns_direct',
        '_train_similarity_matching_system', 
        '_train_ml_model_system',
        '_predict_with_sector_stratified_similarity',
        '_calculate_similarity_and_predict',
        '_create_ensemble_model'
    ]
    
    missing_methods = []
    for method in key_methods:
        if f"def {method}" in content:
            print(f"✅ 方法存在: {method}")
        else:
            print(f"❌ 方法缺失: {method}")
            missing_methods.append(method)
    
    if missing_methods:
        print(f"缺失方法: {missing_methods}")
        return False
    
    # 检查类定义
    if "class SectorModelTrainer" in content:
        print("✅ SectorModelTrainer类存在")
    else:
        print("❌ SectorModelTrainer类缺失")
        return False
    
    print("🎉 基本代码结构测试通过！")
    return True

def test_method_signatures():
    """测试方法签名"""
    print("\n检查方法签名...")
    
    try:
        # 尝试解析Python文件
        import ast
        
        with open("train_sector_models.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析AST
        tree = ast.parse(content)
        
        # 查找SectorModelTrainer类
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef) and node.name == "SectorModelTrainer":
                print("✅ 成功解析SectorModelTrainer类")
                
                # 检查关键方法
                methods_found = []
                for item in node.body:
                    if isinstance(item, ast.FunctionDef):
                        methods_found.append(item.name)
                
                key_methods = [
                    '_analyze_change_point_patterns_direct',
                    '_train_similarity_matching_system', 
                    '_train_ml_model_system',
                    '_predict_with_sector_stratified_similarity'
                ]
                
                for method in key_methods:
                    if method in methods_found:
                        print(f"✅ 方法签名正确: {method}")
                    else:
                        print(f"❌ 方法签名缺失: {method}")
                
                break
        
        print("✅ 方法签名检查完成")
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 解析错误: {e}")
        return False

def test_import_structure():
    """测试导入结构"""
    print("\n检查导入结构...")
    
    with open("train_sector_models.py", 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 检查关键导入
    key_imports = [
        'import pandas as pd',
        'import numpy as np', 
        'from sklearn',
        'class SectorModelTrainer'
    ]
    
    found_imports = []
    for line in lines[:200]:  # 只检查前200行
        for imp in key_imports:
            if imp in line:
                found_imports.append(imp)
                break
    
    for imp in key_imports:
        if imp in found_imports:
            print(f"✅ 导入存在: {imp}")
        else:
            print(f"⚠️  导入可能缺失: {imp}")
    
    return True

def main():
    """主测试函数"""
    print("=" * 60)
    print("重构后代码结构测试")
    print("=" * 60)
    
    tests = [
        ("基本结构测试", test_basic_structure),
        ("方法签名测试", test_method_signatures), 
        ("导入结构测试", test_import_structure)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ {test_name}失败: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通过" if results[i] else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！重构代码结构正确！")
        return True
    else:
        print("⚠️  部分测试失败，需要检查代码结构")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
