#!/usr/bin/env python3
"""
测试高级变化点预测系统
验证重构后的系统功能
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_test_data():
    """创建测试数据"""
    np.random.seed(42)
    
    # 创建股票数据
    n_stocks = 100
    n_days = 30
    
    stock_data = []
    for i in range(n_stocks):
        stock_code = f"00{i:04d}.SZ"
        for j in range(n_days):
            date_str = f"2024{(j % 12) + 1:02d}{(j % 28) + 1:02d}"
            
            # 模拟价格数据
            close = np.random.uniform(10, 50)
            volume = np.random.uniform(10000, 1000000)
            pct_chg = np.random.uniform(-10, 10)
            
            # 模拟技术指标
            ma5 = close * np.random.uniform(0.95, 1.05)
            ma20 = close * np.random.uniform(0.9, 1.1)
            rsi = np.random.uniform(20, 80)
            
            # 模拟future_return_2d（小数形式）
            future_return_2d = np.random.uniform(-0.1, 0.1)
            
            stock_data.append({
                'stock_code': stock_code,
                'date_str': date_str,
                'close': close,
                'volume': volume,
                'pct_chg': pct_chg,
                'preClose': close * (1 + np.random.uniform(-0.05, 0.05)),
                'ma5': ma5,
                'ma20': ma20,
                'rsi': rsi,
                'future_return_2d': future_return_2d,
                'most_relevant_sector': f"sector_{i % 5}",  # 5个板块
                'sector_correlation_score': np.random.uniform(0.3, 0.9)
            })
    
    # 创建板块数据
    sector_data = []
    for i in range(5):
        sector_code = f"sector_{i}"
        for j in range(n_days):
            date_str = f"2024{(j % 12) + 1:02d}{(j % 28) + 1:02d}"
            
            sector_data.append({
                'sector_code': sector_code,
                'date_str': date_str,
                'change_percent': np.random.uniform(-5, 5),
                'volume': np.random.uniform(100000, 10000000),
                'amount': np.random.uniform(1000000, 100000000)
            })
    
    # 创建关联关系数据
    relations_data = []
    for i in range(n_stocks):
        stock_code = f"00{i:04d}.SZ"
        sector_code = f"sector_{i % 5}"
        
        relations_data.append({
            'stock_code': stock_code,
            'sector_code': sector_code,
            'weight': np.random.uniform(0.1, 1.0)
        })
    
    return (pd.DataFrame(stock_data), 
            pd.DataFrame(sector_data), 
            pd.DataFrame(relations_data))

def test_advanced_feature_engineering():
    """测试高级特征工程"""
    logger.info("=== 测试高级特征工程 ===")
    
    try:
        from advanced_feature_engineering import AdaptiveFeatureScaler
        
        # 创建测试特征
        features = {
            'price_feature': np.random.uniform(10, 100, 1000),
            'volume_feature': np.random.exponential(10000, 1000),
            'ratio_feature': np.random.normal(0, 0.1, 1000),
            'indicator_feature': np.random.uniform(0, 100, 1000),
            'binary_feature': np.random.choice([0, 1], 1000)
        }
        
        # 测试自适应标准化
        scaler = AdaptiveFeatureScaler()
        scaled_features = scaler.fit_transform(features)
        
        logger.info(f"✅ 特征标准化成功，处理 {len(scaled_features)} 个特征")
        
        # 测试特征质量报告
        quality_report = scaler.get_quality_report()
        logger.info(f"特征质量报告: {quality_report}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 高级特征工程测试失败: {e}")
        return False

def test_relation_feature_processor():
    """测试关联特征处理器"""
    logger.info("=== 测试关联特征处理器 ===")
    
    try:
        from relation_feature_processor import RelationFeatureProcessor
        
        stock_data, sector_data, relations_data = create_test_data()
        
        processor = RelationFeatureProcessor()
        relation_features = processor.extract_relation_features(
            stock_data, sector_data, relations_data
        )
        
        logger.info(f"✅ 关联特征提取成功，生成 {len(relation_features)} 个特征")
        
        # 测试特征重要性排名
        target = stock_data['future_return_2d'].values
        importance_ranking = processor.get_feature_importance_ranking(
            relation_features, target
        )
        
        logger.info(f"特征重要性排名前5: {list(importance_ranking.items())[:5]}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 关联特征处理器测试失败: {e}")
        return False

def test_hierarchical_data_structure():
    """测试层次化数据结构"""
    logger.info("=== 测试层次化数据结构 ===")
    
    try:
        from hierarchical_data_structure import HierarchicalDataStructure
        
        stock_data, sector_data, relations_data = create_test_data()
        
        # 创建层次化数据结构
        data_structure = HierarchicalDataStructure()
        
        # 添加数据
        data_structure.add_stock_data(stock_data)
        data_structure.add_sector_data(sector_data)
        data_structure.add_relations_data(relations_data)
        
        # 测试数据访问
        stock_code = "000001.SZ"
        date_str = "20240101"
        
        hierarchical_data = data_structure.get_hierarchical_data(stock_code, date_str)
        
        logger.info(f"✅ 层次化数据结构测试成功")
        logger.info(f"获取到的数据结构: {list(hierarchical_data.keys())}")
        
        # 测试统计信息
        stats = data_structure.get_statistics()
        logger.info(f"数据统计: {stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 层次化数据结构测试失败: {e}")
        return False

def test_enhanced_training_pipeline():
    """测试增强的训练管道"""
    logger.info("=== 测试增强的训练管道 ===")
    
    try:
        from train_sector_models import SectorModelTrainer
        
        stock_data, sector_data, relations_data = create_test_data()
        
        trainer = SectorModelTrainer()
        
        # 测试重构后的变化点模式分析
        patterns = trainer._analyze_stock_change_point_patterns(
            stock_data, sector_data, relations_data
        )
        
        if patterns and 'patterns' in patterns:
            logger.info(f"✅ 增强的训练管道测试成功")
            logger.info(f"生成的模式数量: {len(patterns.get('patterns', []))}")
            logger.info(f"高质量模式数量: {patterns.get('high_quality_patterns', 0)}")
            logger.info(f"特征重要性数量: {len(patterns.get('feature_importance', {}))}")
            return True
        else:
            logger.error("❌ 训练管道未生成有效模式")
            return False
        
    except Exception as e:
        logger.error(f"❌ 增强的训练管道测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_enhanced_prediction_pipeline():
    """测试增强的预测管道"""
    logger.info("=== 测试增强的预测管道 ===")
    
    try:
        from train_sector_models import SectorModelTrainer
        
        stock_data, sector_data, relations_data = create_test_data()
        
        trainer = SectorModelTrainer()
        
        # 先训练模式
        patterns = trainer._analyze_stock_change_point_patterns(
            stock_data, sector_data, relations_data
        )
        
        if not patterns:
            logger.error("❌ 无法获取训练模式")
            return False
        
        # 创建预测数据（使用部分训练数据模拟）
        predict_data = stock_data.sample(n=50, random_state=42).copy()
        
        # 测试增强的相似度匹配预测
        predictions = trainer._predict_with_similarity_matching(
            predict_data, relations_data, patterns
        )
        
        if predictions:
            logger.info(f"✅ 增强的预测管道测试成功")
            logger.info(f"预测结果数量: {len(predictions)}")
            
            # 分析预测结果
            scores = [pred.get('prediction_score', 0) for pred in predictions.values()]
            logger.info(f"预测得分范围: [{min(scores):.6f}, {max(scores):.6f}]")
            logger.info(f"预测得分平均值: {np.mean(scores):.6f}")
            logger.info(f"非零预测数量: {sum(1 for s in scores if abs(s) > 0.001)}")
            
            return True
        else:
            logger.error("❌ 预测管道未生成预测结果")
            return False
        
    except Exception as e:
        logger.error(f"❌ 增强的预测管道测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_model_preparation_with_relations():
    """测试模型数据准备（使用relations_df）"""
    logger.info("=== 测试模型数据准备（使用relations_df）===")
    
    try:
        from train_sector_models import SectorModelTrainer
        
        stock_data, sector_data, relations_data = create_test_data()
        
        trainer = SectorModelTrainer()
        
        # 模拟期望特征列表
        expected_features = ['close', 'volume', 'pct_chg', 'ma5', 'ma20', 'rsi']
        
        # 测试重构后的模型数据准备
        test_matrix, stock_info = trainer._prepare_test_data_for_model_with_features(
            stock_data.sample(n=20, random_state=42), 
            relations_data, 
            expected_features
        )
        
        if len(test_matrix) > 0:
            logger.info(f"✅ 模型数据准备测试成功")
            logger.info(f"测试矩阵形状: {test_matrix.shape}")
            logger.info(f"股票信息数量: {len(stock_info)}")
            logger.info(f"特征维度: {test_matrix.shape[1]}")
            return True
        else:
            logger.error("❌ 模型数据准备失败")
            return False
        
    except Exception as e:
        logger.error(f"❌ 模型数据准备测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主测试函数"""
    logger.info("开始测试高级变化点预测系统...")
    
    start_time = time.time()
    success_count = 0
    total_tests = 6
    
    # 测试列表
    tests = [
        ("高级特征工程", test_advanced_feature_engineering),
        ("关联特征处理器", test_relation_feature_processor),
        ("层次化数据结构", test_hierarchical_data_structure),
        ("增强的训练管道", test_enhanced_training_pipeline),
        ("增强的预测管道", test_enhanced_prediction_pipeline),
        ("模型数据准备", test_model_preparation_with_relations)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"开始测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} 测试通过")
                success_count += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试出错: {e}")
    
    # 总结
    elapsed_time = time.time() - start_time
    logger.info(f"\n{'='*60}")
    logger.info(f"测试完成！")
    logger.info(f"总测试数: {total_tests}")
    logger.info(f"通过测试: {success_count}")
    logger.info(f"失败测试: {total_tests - success_count}")
    logger.info(f"成功率: {success_count/total_tests*100:.1f}%")
    logger.info(f"总耗时: {elapsed_time:.2f} 秒")
    
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！高级变化点预测系统重构成功！")
        logger.info("✅ relations_df现在被充分利用")
        logger.info("✅ 训练和预测逻辑已统一")
        logger.info("✅ 多层次特征工程已实现")
        logger.info("✅ 系统性能得到优化")
    else:
        logger.error("❌ 部分测试失败，需要进一步调试")
    
    return success_count == total_tests

if __name__ == "__main__":
    main()
