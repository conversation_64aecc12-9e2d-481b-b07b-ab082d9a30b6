#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试列名问题的脚本
"""

import pandas as pd
import numpy as np

def test_column_names():
    """测试列名是否会被截断"""
    
    # 创建测试DataFrame
    test_df = pd.DataFrame({
        'stock_code': ['000001', '000002'],
        'date_str': ['20240101', '20240102'],
        'most_relevant_sector': ['TGN上证380成份股', 'TGN央企国企改革'],
        'sector_correlation_score': [0.8, 0.7],
        'is_sector_leader': [True, False]
    })
    
    print("原始DataFrame列名:")
    print(test_df.columns.tolist())
    
    # 测试合并操作
    test_df2 = pd.DataFrame({
        'stock_code': ['000001', '000002'],
        'date_str': ['20240101', '20240102'],
        'avg_pct_chg': [2.5, 1.8],
        'avg_volume': [1000000, 2000000]
    })
    
    # 测试merge操作
    merged_df = pd.merge(test_df, test_df2, on=['stock_code', 'date_str'], how='left')
    print("\n合并后DataFrame列名:")
    print(merged_df.columns.tolist())
    
    # 检查most_relevant_sector字段
    if 'most_relevant_sector' in merged_df.columns:
        print("\n✅ most_relevant_sector字段存在")
        print(f"值: {merged_df['most_relevant_sector'].tolist()}")
    else:
        print("\n❌ most_relevant_sector字段丢失")
        if 'most_relevant' in merged_df.columns:
            print("发现most_relevant字段")
            print(f"值: {merged_df['most_relevant'].tolist()}")
    
    # 测试重复列处理逻辑
    print("\n测试重复列处理逻辑:")
    
    # 模拟有重复列的情况
    test_df3 = merged_df.copy()
    test_df3['volume_sector'] = [100, 200]  # 添加一个以_sector结尾的列
    
    print(f"添加volume_sector列后: {test_df3.columns.tolist()}")
    
    # 模拟原来的重复列处理逻辑
    exclude_from_rename = ['most_relevant_sector', 'sector_correlation_score', 'is_sector_leader']
    duplicate_cols = [col for col in test_df3.columns 
                     if col.endswith('_sector') and col not in exclude_from_rename]
    
    print(f"需要处理的重复列: {duplicate_cols}")
    
    if duplicate_cols:
        for col in duplicate_cols:
            original_col = col.replace('_sector', '')
            if original_col in test_df3.columns:
                print(f"删除重复列: {col}")
                test_df3 = test_df3.drop(columns=[col])
            else:
                print(f"重命名列: {col} -> {original_col}")
                test_df3 = test_df3.rename(columns={col: original_col})
    
    print(f"处理后的列名: {test_df3.columns.tolist()}")
    
    # 检查most_relevant_sector是否还存在
    if 'most_relevant_sector' in test_df3.columns:
        print("✅ most_relevant_sector字段仍然存在")
    else:
        print("❌ most_relevant_sector字段在处理后丢失")

if __name__ == "__main__":
    test_column_names()
