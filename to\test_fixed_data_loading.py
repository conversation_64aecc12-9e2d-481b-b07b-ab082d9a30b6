#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的数据加载功能
"""

import pandas as pd
import logging
from advanced_quant_data import ParquetDataStorage

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_data_loading():
    """测试数据加载"""
    logger.info("=== 测试修复后的数据加载功能 ===")
    
    try:
        # 创建存储实例
        storage = ParquetDataStorage()
        
        # 获取股票列表
        stock_list = storage.get_stock_list()
        if not stock_list:
            logger.error("无法获取股票列表")
            return False
        
        # 取前5只股票进行测试
        test_stocks = stock_list[:5]
        logger.info(f"测试股票: {test_stocks}")
        
        # 测试获取日线数据
        start_date = '20241201'
        end_date = '20241210'
        
        logger.info(f"测试获取日线数据: {start_date} 到 {end_date}")
        df = storage.get_daily_data(test_stocks, start_date, end_date)
        
        if df is None or df.empty:
            logger.error("获取日线数据失败")
            return False
        
        logger.info(f"成功获取数据，形状: {df.shape}")
        logger.info(f"列名: {df.columns.tolist()}")
        
        # 检查关键字段
        required_fields = ['stock_code', 'date_str', 'open', 'high', 'low', 'close']
        missing_fields = [field for field in required_fields if field not in df.columns]
        
        if missing_fields:
            logger.warning(f"缺少关键字段: {missing_fields}")
        else:
            logger.info("✅ 所有关键字段都存在")
        
        # 显示样例数据
        logger.info("样例数据:")
        print(df.head(3))
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_generate_daily_features():
    """测试generate_daily_features的数据获取"""
    logger.info("=== 测试generate_daily_features数据获取 ===")
    
    try:
        from generate_daily_features import StockFeatureGenerator
        
        # 创建特征生成器
        generator = StockFeatureGenerator()
        
        # 获取股票列表
        stock_list = generator.get_stock_list()
        if not stock_list:
            logger.error("无法获取股票列表")
            return False
        
        # 取前3只股票进行测试
        test_stocks = stock_list[:3]
        logger.info(f"测试股票: {test_stocks}")
        
        # 测试获取日线数据
        test_date = '20241210'
        logger.info(f"测试获取日期 {test_date} 的数据")
        
        df = generator.get_daily_data(test_stocks, test_date)
        
        if df is None or df.empty:
            logger.error("获取日线数据失败")
            return False
        
        logger.info(f"成功获取数据，形状: {df.shape}")
        logger.info(f"列名: {df.columns.tolist()}")
        
        # 检查日期范围
        if 'date_str' in df.columns:
            date_range = f"{df['date_str'].min()} 到 {df['date_str'].max()}"
            logger.info(f"日期范围: {date_range}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主测试函数"""
    logger.info("开始测试修复后的数据加载功能...")
    
    # 测试1: 直接数据加载
    test1_passed = test_data_loading()
    logger.info(f"直接数据加载测试: {'通过' if test1_passed else '失败'}")
    
    # 测试2: generate_daily_features数据获取
    test2_passed = test_generate_daily_features()
    logger.info(f"generate_daily_features测试: {'通过' if test2_passed else '失败'}")
    
    # 总结
    all_passed = test1_passed and test2_passed
    
    if all_passed:
        logger.info("🎉 所有测试通过！数据加载问题已修复")
        return True
    else:
        logger.error("❌ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
