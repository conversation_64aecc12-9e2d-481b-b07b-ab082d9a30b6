#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试板块unknown问题修复的脚本

专门测试most_relevant_sector字段是否被正确保留
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from train_sector_models import SectorModelTrainer

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_sector_fix.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

def test_merge_all_features():
    """测试_merge_all_features方法是否正确保留most_relevant_sector字段"""
    logger = logging.getLogger(__name__)
    logger.info("=" * 50)
    logger.info("测试_merge_all_features方法")
    logger.info("=" * 50)
    
    try:
        trainer = SectorModelTrainer()
        
        # 创建测试数据
        # result_df包含个股特征和板块关联信息
        result_df = pd.DataFrame({
            'stock_code': ['000001', '000002', '000003'],
            'date_str': ['20240101', '20240101', '20240101'],
            'close': [10.0, 20.0, 30.0],
            'volume': [1000000, 2000000, 3000000],
            'most_relevant_sector': ['TGN上证380成份股', 'TGN央企国企改革', 'TGN周期'],
            'sector_correlation_score': [0.8, 0.7, 0.9],
            'is_sector_leader': [True, False, True]
        })
        
        # relations_df包含板块特征
        relations_df = pd.DataFrame({
            'stock_code': ['000001', '000002', '000003'],
            'date_str': ['20240101', '20240101', '20240101'],
            'avg_pct_chg': [2.5, 1.8, 3.2],
            'avg_volume': [5000000, 4000000, 6000000],
            'limit_up_count': [5, 3, 8],
            'sector_strength': [85.5, 72.3, 91.2]
        })
        
        logger.info(f"测试前 - result_df列: {list(result_df.columns)}")
        logger.info(f"测试前 - relations_df列: {list(relations_df.columns)}")
        logger.info(f"测试前 - result_df中most_relevant_sector值: {result_df['most_relevant_sector'].tolist()}")
        
        # 测试合并方法
        logger.info("调用_merge_all_features方法...")
        merged_df = trainer._merge_all_features(result_df, relations_df)
        
        logger.info(f"测试后 - merged_df列: {list(merged_df.columns)}")
        
        # 检查most_relevant_sector字段是否被正确保留
        if 'most_relevant_sector' in merged_df.columns:
            logger.info("✅ most_relevant_sector字段被正确保留")
            sector_values = merged_df['most_relevant_sector'].tolist()
            logger.info(f"most_relevant_sector值: {sector_values}")
            
            # 检查值是否正确
            expected_values = ['TGN上证380成份股', 'TGN央企国企改革', 'TGN周期']
            if sector_values == expected_values:
                logger.info("✅ most_relevant_sector值正确")
                return True
            else:
                logger.error(f"❌ most_relevant_sector值不正确，期望: {expected_values}, 实际: {sector_values}")
                return False
        else:
            logger.error("❌ most_relevant_sector字段丢失")
            if 'most_relevant' in merged_df.columns:
                logger.error("发现most_relevant字段，说明字段名被截断了")
                logger.error(f"most_relevant值: {merged_df['most_relevant'].tolist()}")
            return False
            
    except Exception as e:
        logger.error(f"测试_merge_all_features时出错: {e}")
        return False

def test_collect_stock_sector_change_point_features():
    """测试collect_stock_sector_change_point_features方法的完整流程"""
    logger = logging.getLogger(__name__)
    logger.info("=" * 50)
    logger.info("测试collect_stock_sector_change_point_features完整流程")
    logger.info("=" * 50)
    
    try:
        trainer = SectorModelTrainer()
        
        # 创建模拟的股票数据
        stock_data = pd.DataFrame({
            'stock_code': ['000001'] * 10,
            'date_str': [f'2024010{i}' for i in range(1, 11)],
            'close': np.random.rand(10) * 100 + 50,
            'volume': np.random.rand(10) * 1000000 + 500000,
            'pct_chg': np.random.randn(10) * 3,
            'turnover_rate': np.random.rand(10) * 5,
            'amplitude': np.random.rand(10) * 8
        })
        
        # 创建模拟的板块数据
        sector_data = pd.DataFrame({
            'sector_code': ['TGN上证380成份股'] * 10,
            'date': [f'2024010{i}' for i in range(1, 11)],
            'avg_pct_chg': np.random.randn(10) * 2,
            'avg_volume': np.random.rand(10) * 2000000 + 1000000,
            'avg_turnover': np.random.rand(10) * 3,
            'limit_up_count': np.random.randint(0, 10, 10),
            'stock_count': [50] * 10
        })
        
        # 创建模拟的变化点数据
        change_points = {
            '000001': {
                'change_points': [True, False, True, False, False, True, False, False, True, False],
                'dates': [f'2024010{i}' for i in range(1, 11)],
                'returns': np.random.randn(10) * 0.05
            }
        }
        
        logger.info("调用collect_stock_sector_change_point_features方法...")
        
        # 测试方法调用
        result = trainer.collect_stock_sector_change_point_features(
            stock_data, sector_data, change_points
        )
        
        if result is not None and len(result) > 0:
            logger.info(f"✅ 方法调用成功，返回 {len(result)} 条记录")
            
            # 检查返回的DataFrame中是否包含most_relevant_sector字段
            if 'most_relevant_sector' in result.columns:
                logger.info("✅ 返回的DataFrame包含most_relevant_sector字段")
                sector_counts = result['most_relevant_sector'].value_counts()
                logger.info(f"板块分布: {dict(sector_counts)}")
                
                unknown_count = (result['most_relevant_sector'] == 'unknown').sum()
                if unknown_count == 0:
                    logger.info("✅ 没有unknown板块记录")
                    return True
                else:
                    logger.warning(f"⚠️ 发现 {unknown_count} 个unknown板块记录")
                    return False
            else:
                logger.error("❌ 返回的DataFrame缺少most_relevant_sector字段")
                logger.info(f"实际列: {list(result.columns)}")
                return False
        else:
            logger.error("❌ 方法调用失败或返回空结果")
            return False
            
    except Exception as e:
        logger.error(f"测试collect_stock_sector_change_point_features时出错: {e}")
        return False

def main():
    """主测试函数"""
    logger = setup_logging()
    logger.info("开始测试板块unknown问题修复...")
    
    results = {
        'merge_features': test_merge_all_features(),
        'collect_features': test_collect_stock_sector_change_point_features()
    }
    
    logger.info("=" * 50)
    logger.info("测试结果总结")
    logger.info("=" * 50)
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        logger.info("🎉 所有测试通过！板块unknown问题已修复")
    else:
        logger.error("⚠️ 部分测试失败，需要进一步检查")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
